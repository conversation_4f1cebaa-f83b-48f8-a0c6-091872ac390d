package controllers

import (
	"hospital-management/internal/dto"
	"hospital-management/internal/services"
	"hospital-management/pkg/logger"
	customValidator "hospital-management/pkg/validator"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
)

type FileController struct {
	fileService *services.FileService
	validator   *validator.Validate
}

func NewFileController(fileService *services.FileService) *FileController {
	return &FileController{
		fileService: fileService,
		validator:   customValidator.NewValidator(),
	}
}

// UploadFile 文件上传
// @Summary 文件上传
// @Description 上传文件到服务器
// @Tags 文件管理
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "上传的文件"
// @Param business_type formData string false "业务类型"
// @Param business_id formData string false "业务ID"
// @Success 201 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /files/upload [post]
func (c *FileController) UploadFile(ctx *gin.Context) {
	// 获取上传的文件
	fileHeader, err := ctx.FormFile("file")
	if err != nil {
		logger.Error("Failed to get uploaded file:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "获取上传文件失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取业务参数
	businessType := ctx.PostForm("business_type")
	businessIDStr := ctx.PostForm("business_id")

	var businessID *uuid.UUID
	if businessIDStr != "" {
		id, err := uuid.Parse(businessIDStr)
		if err != nil {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "无效的业务ID",
			})
			return
		}
		businessID = &id
	}

	var businessTypePtr *string
	if businessType != "" {
		businessTypePtr = &businessType
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	file, err := c.fileService.UploadFile(fileHeader, userID.(uuid.UUID), businessTypePtr, businessID)
	if err != nil {
		logger.Error("Failed to upload file:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "文件上传失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "上传成功",
		"data":    file,
	})
}

// GetFiles 获取文件列表
// @Summary 获取文件列表
// @Description 获取文件列表，支持分页、搜索和筛选
// @Tags 文件管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param business_type query string false "业务类型"
// @Param business_id query string false "业务ID"
// @Param file_type query string false "文件类型"
// @Param is_public query bool false "是否公开"
// @Param uploader_id query string false "上传者ID"
// @Param keyword query string false "搜索关键词"
// @Param start_date query string false "开始日期"
// @Param end_date query string false "结束日期"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /files [get]
func (c *FileController) GetFiles(ctx *gin.Context) {
	var req dto.FileListRequest

	// 设置默认值
	req.Page = 1
	req.PageSize = 10

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	files, total, err := c.fileService.GetFiles(&req)
	if err != nil {
		logger.Error("Failed to get files:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取文件列表失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      files,
			"total":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	})
}

// DownloadFile 文件下载
// @Summary 文件下载
// @Description 下载指定的文件
// @Tags 文件管理
// @Accept json
// @Produce application/octet-stream
// @Param id path string true "文件ID"
// @Success 200 {file} file "文件内容"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "文件不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /files/{id}/download [get]
func (c *FileController) DownloadFile(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的文件ID",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	filePath, originalName, err := c.fileService.DownloadFile(id, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to download file:", err)
		if err.Error() == "文件不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "文件下载失败",
				"error":   err.Error(),
			})
		}
		return
	}

	// 设置响应头
	ctx.Header("Content-Description", "File Transfer")
	ctx.Header("Content-Transfer-Encoding", "binary")
	ctx.Header("Content-Disposition", "attachment; filename="+originalName)
	ctx.Header("Content-Type", "application/octet-stream")

	// 发送文件
	ctx.File(filePath)
}

// DeleteFile 删除文件
// @Summary 删除文件
// @Description 删除指定的文件
// @Tags 文件管理
// @Accept json
// @Produce json
// @Param id path string true "文件ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "文件不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /files/{id} [delete]
func (c *FileController) DeleteFile(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的文件ID",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	if err := c.fileService.DeleteFile(id, userID.(uuid.UUID)); err != nil {
		logger.Error("Failed to delete file:", err)
		if err.Error() == "文件不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "删除文件失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// GetBusinessFiles 获取业务关联文件
// @Summary 获取业务关联文件
// @Description 获取指定业务的关联文件列表
// @Tags 文件管理
// @Accept json
// @Produce json
// @Param businessId path string true "业务ID"
// @Param business_type query string true "业务类型"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /files/business/{businessId} [get]
func (c *FileController) GetBusinessFiles(ctx *gin.Context) {
	businessIDStr := ctx.Param("businessId")
	businessID, err := uuid.Parse(businessIDStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的业务ID",
		})
		return
	}

	businessType := ctx.Query("business_type")
	if businessType == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "业务类型不能为空",
		})
		return
	}

	// 调用服务
	files, err := c.fileService.GetBusinessFiles(businessType, businessID)
	if err != nil {
		logger.Error("Failed to get business files:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取业务关联文件失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    files,
	})
}
