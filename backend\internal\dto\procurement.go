package dto

import (
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// ===== 供应商管理相关 =====

// CreateSupplierRequest 创建供应商请求
type CreateSupplierRequest struct {
	Name        string                 `json:"name" validate:"required,max=255"`
	CreditCode  *string                `json:"credit_code,omitempty" validate:"omitempty,max=100"`
	Status      int16                  `json:"status" validate:"required,oneof=0 1"`
	ContactInfo map[string]interface{} `json:"contact_info,omitempty"`
}

// UpdateSupplierRequest 更新供应商请求
type UpdateSupplierRequest struct {
	Name        *string                `json:"name,omitempty" validate:"omitempty,max=255"`
	CreditCode  *string                `json:"credit_code,omitempty" validate:"omitempty,max=100"`
	ContactInfo map[string]interface{} `json:"contact_info,omitempty"`
}

// UpdateSupplierStatusRequest 更新供应商状态请求
type UpdateSupplierStatusRequest struct {
	Status int16 `json:"status" validate:"required,oneof=0 1"`
}

// SupplierResponse 供应商响应
type SupplierResponse struct {
	ID          uuid.UUID              `json:"id"`
	Name        string                 `json:"name"`
	CreditCode  *string                `json:"credit_code,omitempty"`
	Status      int16                  `json:"status"`
	ContactInfo map[string]interface{} `json:"contact_info,omitempty"`
	CreatedAt   string                 `json:"created_at"`
	UpdatedAt   string                 `json:"updated_at"`
}

// SupplierListRequest 供应商列表请求
type SupplierListRequest struct {
	Page     int    `form:"page" validate:"min=1"`
	PageSize int    `form:"page_size" validate:"min=1,max=100"`
	Keyword  string `form:"keyword"`
	Status   *int16 `form:"status" validate:"omitempty,oneof=0 1"`
}

// ===== 采购申请相关 =====

// PurchaseItemRequest 采购物品请求
type PurchaseItemRequest struct {
	ItemName        string           `json:"item_name" validate:"required,max=255"`
	Specification   *string          `json:"specification,omitempty" validate:"omitempty,max=500"`
	Unit            string           `json:"unit" validate:"required,max=50"`
	Quantity        decimal.Decimal  `json:"quantity" validate:"required,gt=0"`
	EstimatedPrice  *decimal.Decimal `json:"estimated_price,omitempty" validate:"omitempty,gte=0"`
	EstimatedAmount *decimal.Decimal `json:"estimated_amount,omitempty" validate:"omitempty,gte=0"`
	Remark          *string          `json:"remark,omitempty" validate:"omitempty,max=500"`
}

// CreatePurchaseRequisitionRequest 创建采购申请请求
type CreatePurchaseRequisitionRequest struct {
	Title string                `json:"title" validate:"required,max=255"`
	Items []PurchaseItemRequest `json:"items" validate:"required,dive"`
}

// UpdatePurchaseRequisitionRequest 更新采购申请请求
type UpdatePurchaseRequisitionRequest struct {
	Title *string               `json:"title,omitempty" validate:"omitempty,max=255"`
	Items []PurchaseItemRequest `json:"items,omitempty" validate:"omitempty,dive"`
}

// PurchaseItemResponse 采购物品响应
type PurchaseItemResponse struct {
	ItemName        string           `json:"item_name"`
	Specification   *string          `json:"specification,omitempty"`
	Unit            string           `json:"unit"`
	Quantity        decimal.Decimal  `json:"quantity"`
	EstimatedPrice  *decimal.Decimal `json:"estimated_price,omitempty"`
	EstimatedAmount *decimal.Decimal `json:"estimated_amount,omitempty"`
	Remark          *string          `json:"remark,omitempty"`
}

// PurchaseRequisitionResponse 采购申请响应
type PurchaseRequisitionResponse struct {
	ID              uuid.UUID                    `json:"id"`
	RequisitionCode string                       `json:"requisition_code"`
	ApplicantID     uuid.UUID                    `json:"applicant_id"`
	DepartmentID    uuid.UUID                    `json:"department_id"`
	Title           string                       `json:"title"`
	TotalAmount     *decimal.Decimal             `json:"total_amount,omitempty"`
	Status          string                       `json:"status"`
	CreatedAt       string                       `json:"created_at"`
	UpdatedAt       string                       `json:"updated_at"`
	Applicant       *UserSimpleResponse          `json:"applicant,omitempty"`
	Department      *DepartmentSimpleResponse    `json:"department,omitempty"`
	Items           []PurchaseItemResponse       `json:"items,omitempty"`
}

// PurchaseRequisitionListRequest 采购申请列表请求
type PurchaseRequisitionListRequest struct {
	Page         int        `form:"page" validate:"min=1"`
	PageSize     int        `form:"page_size" validate:"min=1,max=100"`
	Keyword      string     `form:"keyword"`
	Status       string     `form:"status" validate:"omitempty,oneof=DRAFT PENDING APPROVED REJECTED CLOSED"`
	ApplicantID  *uuid.UUID `form:"applicant_id" validate:"omitempty,uuid"`
	DepartmentID *uuid.UUID `form:"department_id" validate:"omitempty,uuid"`
}

// ===== 采购统计分析相关 =====

// PurchaseStatisticsRequest 采购统计请求
type PurchaseStatisticsRequest struct {
	StartDate    *string    `form:"start_date" validate:"omitempty,datetime=2006-01-02"`
	EndDate      *string    `form:"end_date" validate:"omitempty,datetime=2006-01-02"`
	DepartmentID *uuid.UUID `form:"department_id" validate:"omitempty,uuid"`
	SupplierID   *uuid.UUID `form:"supplier_id" validate:"omitempty,uuid"`
}

// PurchaseStatisticsResponse 采购统计响应
type PurchaseStatisticsResponse struct {
	TotalRequisitions     int64                              `json:"total_requisitions"`
	TotalAmount           decimal.Decimal                    `json:"total_amount"`
	ApprovedRequisitions  int64                              `json:"approved_requisitions"`
	ApprovedAmount        decimal.Decimal                    `json:"approved_amount"`
	DepartmentStats       []PurchaseDepartmentStatsResponse  `json:"department_stats"`
	SupplierStats         []PurchaseSupplierStatsResponse    `json:"supplier_stats"`
	MonthlyStats          []PurchaseMonthlyStatsResponse     `json:"monthly_stats"`
}

// PurchaseDepartmentStatsResponse 部门采购统计响应
type PurchaseDepartmentStatsResponse struct {
	DepartmentID   uuid.UUID       `json:"department_id"`
	DepartmentName string          `json:"department_name"`
	RequisitionCount int64         `json:"requisition_count"`
	TotalAmount    decimal.Decimal `json:"total_amount"`
}

// PurchaseSupplierStatsResponse 供应商采购统计响应
type PurchaseSupplierStatsResponse struct {
	SupplierID     uuid.UUID       `json:"supplier_id"`
	SupplierName   string          `json:"supplier_name"`
	ContractCount  int64           `json:"contract_count"`
	TotalAmount    decimal.Decimal `json:"total_amount"`
}

// PurchaseMonthlyStatsResponse 月度采购统计响应
type PurchaseMonthlyStatsResponse struct {
	Month           string          `json:"month"`
	RequisitionCount int64          `json:"requisition_count"`
	TotalAmount     decimal.Decimal `json:"total_amount"`
}

// ===== 辅助响应结构 =====

// SupplierSimpleResponse 供应商简单响应（用于关联显示）
type SupplierSimpleResponse struct {
	ID         uuid.UUID `json:"id"`
	Name       string    `json:"name"`
	CreditCode *string   `json:"credit_code,omitempty"`
	Status     int16     `json:"status"`
}

// PurchaseRequisitionSimpleResponse 采购申请简单响应（用于关联显示）
type PurchaseRequisitionSimpleResponse struct {
	ID              uuid.UUID        `json:"id"`
	RequisitionCode string           `json:"requisition_code"`
	Title           string           `json:"title"`
	TotalAmount     *decimal.Decimal `json:"total_amount,omitempty"`
	Status          string           `json:"status"`
}

// ===== 采购订单相关（预留接口） =====

// CreatePurchaseOrderRequest 创建采购订单请求（预留）
type CreatePurchaseOrderRequest struct {
	SupplierID      uuid.UUID       `json:"supplier_id" validate:"required,uuid"`
	ContractID      *uuid.UUID      `json:"contract_id,omitempty" validate:"omitempty,uuid"`
	TotalAmount     decimal.Decimal `json:"total_amount" validate:"required,gt=0"`
	OrderDetails    map[string]interface{} `json:"order_details,omitempty"`
}

// PurchaseOrderResponse 采购订单响应（预留）
type PurchaseOrderResponse struct {
	ID           uuid.UUID              `json:"id"`
	OrderCode    string                 `json:"order_code"`
	SupplierID   uuid.UUID              `json:"supplier_id"`
	ContractID   *uuid.UUID             `json:"contract_id,omitempty"`
	TotalAmount  decimal.Decimal        `json:"total_amount"`
	Status       string                 `json:"status"`
	OrderDetails map[string]interface{} `json:"order_details,omitempty"`
	CreatedAt    string                 `json:"created_at"`
	UpdatedAt    string                 `json:"updated_at"`
	Supplier     *SupplierSimpleResponse `json:"supplier,omitempty"`
}
