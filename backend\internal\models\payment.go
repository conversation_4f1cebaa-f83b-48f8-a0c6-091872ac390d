package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// Payment 付款单 - 对应 tbl_payments
type Payment struct {
	BaseModel
	PaymentCode   string          `json:"payment_code" gorm:"type:varchar(50);unique;not null" validate:"required,max=50"`
	SourceID      uuid.UUID       `json:"source_id" gorm:"type:uuid;not null" validate:"required"`
	SourceType    string          `json:"source_type" gorm:"type:varchar(50);not null" validate:"required,max=50"`
	PayeeName     string          `json:"payee_name" gorm:"type:varchar(255);not null" validate:"required,max=255"`
	PayeeAccount  string          `json:"payee_account" gorm:"type:varchar(100);not null" validate:"required,max=100"`
	PayeeBank     string          `json:"payee_bank" gorm:"type:varchar(255);not null" validate:"required,max=255"`
	Amount        decimal.Decimal `json:"amount" gorm:"type:decimal(18,2);not null" validate:"required,gte=0"`
	Status        string          `json:"status" gorm:"type:varchar(30);not null" validate:"required,max=30"`
	PaidAt        *time.Time      `json:"paid_at,omitempty" gorm:"type:timestamptz"`
	TransactionID *string         `json:"transaction_id,omitempty" gorm:"type:varchar(255)"`
}

// TableName 指定表名
func (Payment) TableName() string {
	return "tbl_payments"
}
