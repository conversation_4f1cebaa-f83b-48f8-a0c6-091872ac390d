import request from './request'

// 资产分类相关接口
export interface AssetCategory {
  id: number
  name: string
  code: string
  parent_id?: number
  level: number
  depreciation_method: 'straight_line' | 'declining_balance' | 'sum_of_years'
  depreciation_years: number
  description?: string
  created_at: string
  updated_at: string
  children?: AssetCategory[]
}

export interface AssetCategoryForm {
  name: string
  code: string
  parent_id?: number
  depreciation_method: 'straight_line' | 'declining_balance' | 'sum_of_years'
  depreciation_years: number
  description?: string
}

// 获取资产分类列表
export const getAssetCategories = (): Promise<AssetCategory[]> => {
  return request.get('/api/v1/asset/categories')
}

// 获取资产分类树形结构
export const getAssetCategoryTree = (): Promise<AssetCategory[]> => {
  return request.get('/api/v1/asset/categories/tree')
}

// 创建资产分类
export const createAssetCategory = (data: AssetCategoryForm): Promise<AssetCategory> => {
  return request.post('/api/v1/asset/categories', data)
}

// 更新资产分类
export const updateAssetCategory = (id: number, data: AssetCategoryForm): Promise<AssetCategory> => {
  return request.put(`/api/v1/asset/categories/${id}`, data)
}

// 删除资产分类
export const deleteAssetCategory = (id: number): Promise<void> => {
  return request.delete(`/api/v1/asset/categories/${id}`)
}

// 资产台账相关接口
export interface Asset {
  id: number
  asset_no: string
  name: string
  category_id: number
  category_name: string
  specification?: string
  model?: string
  brand?: string
  supplier_id?: number
  supplier_name?: string
  purchase_date: string
  purchase_price: number
  original_value: number
  current_value: number
  accumulated_depreciation: number
  depreciation_rate: number
  useful_life: number
  location: string
  department_id: number
  department_name: string
  custodian_id?: number
  custodian_name?: string
  status: 'normal' | 'maintenance' | 'idle' | 'scrapped'
  warranty_date?: string
  maintenance_cycle?: number
  last_maintenance_date?: string
  next_maintenance_date?: string
  remark?: string
  created_at: string
  updated_at: string
}

export interface AssetForm {
  name: string
  category_id: number
  specification?: string
  model?: string
  brand?: string
  supplier_id?: number
  purchase_date: string
  purchase_price: number
  location: string
  department_id: number
  custodian_id?: number
  warranty_date?: string
  maintenance_cycle?: number
  remark?: string
}

export interface AssetQuery {
  keyword?: string
  category_id?: number
  department_id?: number
  custodian_id?: number
  status?: string
  start_date?: string
  end_date?: string
  page?: number
  page_size?: number
}

// 获取资产列表
export const getAssets = (params: AssetQuery): Promise<{
  list: Asset[]
  total: number
  page: number
  page_size: number
}> => {
  return request.get('/api/v1/assets', { params })
}

// 获取资产详情
export const getAssetDetail = (id: number): Promise<Asset> => {
  return request.get(`/api/v1/assets/${id}`)
}

// 创建资产
export const createAsset = (data: AssetForm): Promise<Asset> => {
  return request.post('/api/v1/assets', data)
}

// 更新资产
export const updateAsset = (id: number, data: AssetForm): Promise<Asset> => {
  return request.put(`/api/v1/assets/${id}`, data)
}

// 删除资产
export const deleteAsset = (id: number): Promise<void> => {
  return request.delete(`/api/v1/assets/${id}`)
}

// 更新资产状态
export const updateAssetStatus = (id: number, status: string): Promise<void> => {
  return request.put(`/api/v1/assets/${id}/status`, { status })
}

// 资产折旧相关接口
export interface DepreciationRecord {
  id: string
  asset_id: string
  asset_name?: string
  period: string
  depreciation_amount: number
  accumulated_depreciation: number
  book_value: number
  depreciation_method: 'straight_line' | 'declining_balance' | 'sum_of_years'
  calculation_basis?: string
  remark?: string
  created_at: string
  created_by: string
  asset?: Asset
  creator?: {
    id: string
    user_name: string
    job_title?: string
  }
}

export interface DepreciationQuery {
  asset_id?: string
  period?: string
  start_date?: string
  end_date?: string
  page?: number
  page_size?: number
}

export interface DepreciationForm {
  asset_id: string
  period: string
  depreciation_amount: number
  accumulated_depreciation: number
  book_value: number
  depreciation_method: 'straight_line' | 'declining_balance' | 'sum_of_years'
  calculation_basis?: string
  remark?: string
}

export interface BatchDepreciationForm {
  period: string
  asset_ids: string[]
  force_recalc?: boolean
}

// 获取折旧记录
export const getDepreciationRecords = (params: DepreciationQuery): Promise<{
  list: DepreciationRecord[]
  total: number
  page: number
  page_size: number
  total_page: number
}> => {
  return request.get('/api/v1/asset/depreciation', { params })
}

// 获取折旧记录详情
export const getDepreciationRecordDetail = (id: string): Promise<DepreciationRecord> => {
  return request.get(`/api/v1/asset/depreciation/${id}`)
}

// 创建折旧记录
export const createDepreciationRecord = (data: DepreciationForm): Promise<DepreciationRecord> => {
  return request.post('/api/v1/asset/depreciation', data)
}

// 批量计算折旧
export const batchCalculateDepreciation = (data: BatchDepreciationForm): Promise<void> => {
  return request.post('/api/v1/asset/depreciation/batch-calculate', data)
}

// 资产维护相关接口
export interface MaintenanceRecord {
  id: string
  asset_id: string
  asset_name?: string
  maintenance_type: 'routine' | 'repair' | 'upgrade'
  maintenance_date: string
  maintenance_cost: number
  maintenance_company?: string
  maintenance_person?: string
  description: string
  next_maintenance_date?: string
  status: 'planned' | 'in_progress' | 'completed'
  attachment_urls?: string[]
  remark?: string
  created_at: string
  updated_at: string
  created_by: string
  asset?: Asset
  creator?: {
    id: string
    user_name: string
    job_title?: string
  }
}

export interface MaintenanceRecordForm {
  asset_id: string
  maintenance_type: 'routine' | 'repair' | 'upgrade'
  maintenance_date: string
  maintenance_cost: number
  maintenance_company?: string
  maintenance_person?: string
  description: string
  next_maintenance_date?: string
  status?: 'planned' | 'in_progress' | 'completed'
  attachment_urls?: string[]
  remark?: string
}

export interface MaintenanceQuery {
  asset_id?: string
  maintenance_type?: string
  status?: string
  start_date?: string
  end_date?: string
  page?: number
  page_size?: number
}

// 获取维护记录
export const getMaintenanceRecords = (params: MaintenanceQuery): Promise<{
  list: MaintenanceRecord[]
  total: number
  page: number
  page_size: number
  total_page: number
}> => {
  return request.get('/api/v1/asset/maintenance', { params })
}

// 获取维护记录详情
export const getMaintenanceRecordDetail = (id: string): Promise<MaintenanceRecord> => {
  return request.get(`/api/v1/asset/maintenance/${id}`)
}

// 创建维护记录
export const createMaintenanceRecord = (data: MaintenanceRecordForm): Promise<MaintenanceRecord> => {
  return request.post('/api/v1/asset/maintenance', data)
}

// 更新维护记录
export const updateMaintenanceRecord = (id: string, data: MaintenanceRecordForm): Promise<MaintenanceRecord> => {
  return request.put(`/api/v1/asset/maintenance/${id}`, data)
}

// 删除维护记录
export const deleteMaintenanceRecord = (id: string): Promise<void> => {
  return request.delete(`/api/v1/asset/maintenance/${id}`)
}

// 获取即将到期的维护
export const getUpcomingMaintenances = (days?: number): Promise<MaintenanceRecord[]> => {
  const params = days ? { days } : {}
  return request.get('/api/v1/asset/maintenance/upcoming', { params })
}

// 资产统计相关接口
export interface AssetStatistics {
  total_assets: number
  total_value: number
  total_depreciation: number
  net_value: number
  category_statistics: Array<{
    category_name: string
    count: number
    value: number
    depreciation: number
  }>
  department_statistics: Array<{
    department_name: string
    count: number
    value: number
  }>
  status_statistics: Array<{
    status: string
    count: number
    percentage: number
  }>
}

// 获取资产统计数据
export const getAssetStatistics = (): Promise<AssetStatistics> => {
  return request.get('/api/v1/asset/statistics')
}

// 资产分析相关接口
export interface AssetAnalysisQuery {
  start_date?: string
  end_date?: string
  category_id?: number
  department_id?: number
}

// 获取资产趋势分析
export const getAssetTrendAnalysis = (params: AssetAnalysisQuery): Promise<Array<{
  period: string
  purchase_count: number
  purchase_value: number
  depreciation_amount: number
}>> => {
  return request.get('/api/v1/asset/analysis/trend', { params })
}

// 获取资产利用率分析
export const getAssetUtilizationAnalysis = (params: AssetAnalysisQuery): Promise<Array<{
  asset_name: string
  utilization_rate: number
  maintenance_cost: number
  efficiency_score: number
}>> => {
  return request.get('/api/v1/asset/analysis/utilization', { params })
}
