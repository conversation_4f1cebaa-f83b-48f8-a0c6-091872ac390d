package controllers

import (
	"hospital-management/internal/dto"
	"hospital-management/internal/services"
	"hospital-management/pkg/logger"
	customValidator "hospital-management/pkg/validator"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
)

type ApprovalController struct {
	approvalService *services.ApprovalService
	validator       *validator.Validate
}

func NewApprovalController(approvalService *services.ApprovalService) *ApprovalController {
	return &ApprovalController{
		approvalService: approvalService,
		validator:       customValidator.NewValidator(),
	}
}

// CreateApprovalFlow 创建审批流
// @Summary 创建审批流
// @Description 创建新的审批流实例
// @Tags 审批管理
// @Accept json
// @Produce json
// @Param request body dto.CreateApprovalFlowRequest true "创建审批流请求"
// @Success 201 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /approval/flows [post]
func (c *ApprovalController) CreateApprovalFlow(ctx *gin.Context) {
	var req dto.CreateApprovalFlowRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	flow, err := c.approvalService.CreateApprovalFlow(&req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to create approval flow:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建审批流失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "创建成功",
		"data":    flow,
	})
}

// GetApprovalFlowByID 获取审批流详情
// @Summary 获取审批流详情
// @Description 根据ID获取审批流详情
// @Tags 审批管理
// @Accept json
// @Produce json
// @Param id path string true "审批流ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "审批流不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /approval/flows/{id} [get]
func (c *ApprovalController) GetApprovalFlowByID(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的审批流ID",
		})
		return
	}

	// 调用服务
	flow, err := c.approvalService.GetApprovalFlowByID(id)
	if err != nil {
		logger.Error("Failed to get approval flow:", err)
		if err.Error() == "审批流不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "获取审批流详情失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    flow,
	})
}

// GetApprovalFlows 获取审批流列表
// @Summary 获取审批流列表
// @Description 获取审批流列表，支持分页、搜索和筛选
// @Tags 审批管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param business_type query string false "业务类型"
// @Param status query string false "状态" Enums(pending, approved, rejected, cancelled)
// @Param created_by query string false "创建人ID"
// @Param keyword query string false "搜索关键词"
// @Param start_date query string false "开始日期"
// @Param end_date query string false "结束日期"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /approval/flows [get]
func (c *ApprovalController) GetApprovalFlows(ctx *gin.Context) {
	var req dto.ApprovalFlowListRequest

	// 设置默认值
	req.Page = 1
	req.PageSize = 10

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	flows, total, err := c.approvalService.GetApprovalFlows(&req)
	if err != nil {
		logger.Error("Failed to get approval flows:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取审批流列表失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      flows,
			"total":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	})
}

// GetTodoApprovals 获取待办审批列表
// @Summary 获取待办审批列表
// @Description 获取当前用户的待办审批任务列表
// @Tags 审批管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param business_type query string false "业务类型"
// @Param keyword query string false "搜索关键词"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /approval/todo [get]
func (c *ApprovalController) GetTodoApprovals(ctx *gin.Context) {
	var req dto.TodoApprovalRequest

	// 设置默认值
	req.Page = 1
	req.PageSize = 10

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	todos, total, err := c.approvalService.GetTodoApprovals(userID.(uuid.UUID), &req)
	if err != nil {
		logger.Error("Failed to get todo approvals:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取待办审批列表失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      todos,
			"total":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	})
}

// ApproveNode 审批操作
// @Summary 审批操作
// @Description 对审批节点进行审批、驳回或转办操作
// @Tags 审批管理
// @Accept json
// @Produce json
// @Param nodeId path string true "审批节点ID"
// @Param request body dto.ApprovalActionRequest true "审批操作请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "审批节点不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /approval/approve/{nodeId} [post]
func (c *ApprovalController) ApproveNode(ctx *gin.Context) {
	nodeIDStr := ctx.Param("nodeId")
	nodeID, err := uuid.Parse(nodeIDStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的审批节点ID",
		})
		return
	}

	var req dto.ApprovalActionRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	if err := c.approvalService.ApproveNode(nodeID, &req, userID.(uuid.UUID)); err != nil {
		logger.Error("Failed to approve node:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "审批操作失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "审批操作成功",
	})
}

// RejectNode 驳回操作
// @Summary 驳回操作
// @Description 驳回审批节点
// @Tags 审批管理
// @Accept json
// @Produce json
// @Param nodeId path string true "审批节点ID"
// @Param request body dto.ApprovalActionRequest true "驳回操作请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "审批节点不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /approval/reject/{nodeId} [post]
func (c *ApprovalController) RejectNode(ctx *gin.Context) {
	nodeIDStr := ctx.Param("nodeId")
	nodeID, err := uuid.Parse(nodeIDStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的审批节点ID",
		})
		return
	}

	var req dto.ApprovalActionRequest
	req.Action = "reject" // 强制设置为驳回

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	if err := c.approvalService.ApproveNode(nodeID, &req, userID.(uuid.UUID)); err != nil {
		logger.Error("Failed to reject node:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "驳回操作失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "驳回操作成功",
	})
}

// TransferNode 转办操作
// @Summary 转办操作
// @Description 将审批节点转办给其他人
// @Tags 审批管理
// @Accept json
// @Produce json
// @Param nodeId path string true "审批节点ID"
// @Param request body dto.ApprovalActionRequest true "转办操作请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "审批节点不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /approval/transfer/{nodeId} [post]
func (c *ApprovalController) TransferNode(ctx *gin.Context) {
	nodeIDStr := ctx.Param("nodeId")
	_, err := uuid.Parse(nodeIDStr) // nodeID
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的审批节点ID",
		})
		return
	}

	var req dto.ApprovalActionRequest
	req.Action = "transfer" // 强制设置为转办

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证转办参数
	if req.TransferTo == nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "转办目标用户不能为空",
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	_, exists := ctx.Get("user_id") // userID
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// TODO: 实现转办逻辑
	// 这里应该调用服务层的转办方法
	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "转办功能待实现",
	})
}

// UpdateApprovalFlowStatus 更新审批流状态
// @Summary 更新审批流状态
// @Description 更新审批流的状态
// @Tags 审批管理
// @Accept json
// @Produce json
// @Param id path string true "审批流ID"
// @Param request body dto.UpdateApprovalFlowStatusRequest true "更新状态请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "审批流不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /approval/flows/{id}/status [put]
func (c *ApprovalController) UpdateApprovalFlowStatus(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的审批流ID",
		})
		return
	}

	var req dto.UpdateApprovalFlowStatusRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	if err := c.approvalService.UpdateApprovalFlowStatus(id, &req, userID.(uuid.UUID)); err != nil {
		logger.Error("Failed to update approval flow status:", err)
		if err.Error() == "审批流不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "更新审批流状态失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
	})
}
