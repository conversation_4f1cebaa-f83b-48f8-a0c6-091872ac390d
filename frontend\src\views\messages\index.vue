<template>
  <div class="messages-page">
    <a-card :bordered="false">
      <template #title>
        <span>消息中心</span>
      </template>

      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleSend">
            <template #icon>
              <PlusOutlined />
            </template>
            发送消息
          </a-button>
          <a-button @click="handleRefresh">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 消息统计卡片 -->
      <div class="message-stats">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card size="small">
              <a-statistic title="总消息数" :value="statistics.total_messages" :value-style="{ color: '#1890ff' }" />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic title="未读消息" :value="statistics.unread_messages" :value-style="{ color: '#f5222d' }" />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic title="今日消息" :value="statistics.today_messages" :value-style="{ color: '#52c41a' }" />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic title="待办任务" :value="unreadCount.todo" :value-style="{ color: '#fa8c16' }" />
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 搜索区域 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="消息类型">
            <a-select v-model:value="searchForm.type" placeholder="请选择类型" allow-clear style="width: 120px">
              <a-select-option value="todo">待办任务</a-select-option>
              <a-select-option value="notification">通知消息</a-select-option>
              <a-select-option value="announcement">公告消息</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="消息状态">
            <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear style="width: 120px">
              <a-select-option value="unread">未读</a-select-option>
              <a-select-option value="read">已读</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="优先级">
            <a-select v-model:value="searchForm.priority" placeholder="请选择优先级" allow-clear style="width: 120px">
              <a-select-option value="high">高</a-select-option>
              <a-select-option value="medium">中</a-select-option>
              <a-select-option value="low">低</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="关键词">
            <a-input v-model:value="searchForm.keyword" placeholder="消息标题/内容" allow-clear style="width: 200px" />
          </a-form-item>

          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon>
                  <SearchOutlined />
                </template>
                搜索
              </a-button>
              <a-button @click="handleReset">
                <template #icon>
                  <ReloadOutlined />
                </template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedRowKeys.length > 0">
        <a-space>
          <span>已选择 {{ selectedRowKeys.length }} 条消息</span>
          <a-button @click="handleBatchRead">批量标记已读</a-button>
          <a-button danger @click="handleBatchDelete">批量删除</a-button>
          <a-button @click="selectedRowKeys = []">取消选择</a-button>
        </a-space>
      </div>

      <!-- 消息列表 -->
      <a-table :columns="columns" :data-source="messageList" :loading="loading" :pagination="pagination"
        :row-selection="rowSelection" row-key="id" size="middle" @change="handleTableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'title'">
            <div class="message-title" @click="handleView(record)">
              <a-badge :dot="record.status === 'unread'" :offset="[-5, 0]">
                <span :class="{ 'unread-title': record.status === 'unread' }">
                  {{ record.title }}
                </span>
              </a-badge>
            </div>
          </template>

          <template v-else-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeText(record.type) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'priority'">
            <a-tag :color="getPriorityColor(record.priority)">
              {{ getPriorityText(record.priority) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleView(record)">
                查看
              </a-button>
              <a-button type="link" size="small" @click="handleMarkRead(record)" v-if="record.status === 'unread'">
                标记已读
              </a-button>
              <a-popconfirm title="确定要删除这条消息吗？" @confirm="handleDelete(record)">
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 发送消息弹窗 -->
    <a-modal v-model:open="sendModalVisible" title="发送消息" :confirm-loading="sendLoading" width="800px"
      @ok="handleSendSubmit" @cancel="sendModalVisible = false">
      <a-form ref="sendFormRef" :model="sendForm" :rules="sendRules" :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }">
        <a-form-item label="消息标题" name="title">
          <a-input v-model:value="sendForm.title" placeholder="请输入消息标题" />
        </a-form-item>

        <a-form-item label="消息类型" name="type">
          <a-radio-group v-model:value="sendForm.type">
            <a-radio value="todo">待办任务</a-radio>
            <a-radio value="notification">通知消息</a-radio>
            <a-radio value="announcement">公告消息</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="优先级" name="priority">
          <a-radio-group v-model:value="sendForm.priority">
            <a-radio value="low">低</a-radio>
            <a-radio value="medium">中</a-radio>
            <a-radio value="high">高</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="接收人" name="receiver_ids">
          <a-select v-model:value="sendForm.receiver_ids" mode="multiple" placeholder="请选择接收人" show-search
            :filter-option="filterUserOption" :options="userOptions" />
        </a-form-item>

        <a-form-item label="消息内容" name="content">
          <a-textarea v-model:value="sendForm.content" placeholder="请输入消息内容" :rows="6" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 查看详情弹窗 -->
    <a-modal v-model:open="viewModalVisible" title="消息详情" :footer="null" width="800px">
      <div v-if="currentMessage">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="消息标题" :span="2">
            {{ currentMessage.title }}
          </a-descriptions-item>
          <a-descriptions-item label="消息类型">
            <a-tag :color="getTypeColor(currentMessage.type)">
              {{ getTypeText(currentMessage.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="优先级">
            <a-tag :color="getPriorityColor(currentMessage.priority)">
              {{ getPriorityText(currentMessage.priority) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="发送人" v-if="currentMessage.sender_name">
            {{ currentMessage.sender_name }}
          </a-descriptions-item>
          <a-descriptions-item label="消息状态">
            <a-tag :color="getStatusColor(currentMessage.status)">
              {{ getStatusText(currentMessage.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="发送时间">
            {{ currentMessage.created_at }}
          </a-descriptions-item>
          <a-descriptions-item label="阅读时间" v-if="currentMessage.read_at">
            {{ currentMessage.read_at }}
          </a-descriptions-item>
        </a-descriptions>

        <a-divider>消息内容</a-divider>

        <div class="message-content">
          {{ currentMessage.content }}
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'
import {
  getMessages,
  sendMessage,
  markMessageAsRead,
  batchMarkAsRead,
  deleteMessage,
  batchDeleteMessages,
  getUnreadCount,
  getMessageStatistics,
  type Message,
  type MessageForm,
  type MessageQuery
} from '@/api/message'
import { getUsers, type User } from '@/api/system'
import type { Rule } from 'ant-design-vue/es/form'
import type { FormInstance } from 'ant-design-vue'

const loading = ref(false)
const sendModalVisible = ref(false)
const sendLoading = ref(false)
const viewModalVisible = ref(false)
const sendFormRef = ref<FormInstance>()
const selectedRowKeys = ref<number[]>([])

const messageList = ref<Message[]>([])
const userList = ref<User[]>([])
const currentMessage = ref<Message | null>(null)
const statistics = ref({
  total_messages: 0,
  unread_messages: 0,
  today_messages: 0,
  type_statistics: [],
  priority_statistics: []
})
const unreadCount = ref({
  total: 0,
  todo: 0,
  notification: 0,
  announcement: 0
})

const searchForm = reactive<MessageQuery>({
  type: undefined,
  status: undefined,
  priority: undefined,
  keyword: '',
  page: 1,
  page_size: 10
})

const sendForm = reactive<MessageForm>({
  title: '',
  content: '',
  type: 'notification',
  priority: 'medium',
  receiver_ids: []
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '消息标题',
    key: 'title',
    width: 300,
    ellipsis: true
  },
  {
    title: '消息类型',
    key: 'type',
    width: 100
  },
  {
    title: '优先级',
    key: 'priority',
    width: 80
  },
  {
    title: '发送人',
    dataIndex: 'sender_name',
    key: 'sender_name',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 80
  },
  {
    title: '发送时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right'
  }
]

// 表单验证规则
const sendRules: Record<string, Rule[]> = {
  title: [
    { required: true, message: '请输入消息标题', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择消息类型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  receiver_ids: [
    { required: true, message: '请选择接收人', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入消息内容', trigger: 'blur' }
  ]
}

// 行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys
  }
}))

// 用户选项
const userOptions = computed(() => {
  return userList.value.map(user => ({
    label: `${user.name} (${user.department_name})`,
    value: user.id
  }))
})

// 获取消息类型颜色
const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'todo': 'orange',
    'notification': 'blue',
    'announcement': 'green'
  }
  return colorMap[type] || 'default'
}

// 获取消息类型文本
const getTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    'todo': '待办任务',
    'notification': '通知消息',
    'announcement': '公告消息'
  }
  return textMap[type] || type
}

// 获取优先级颜色
const getPriorityColor = (priority: string) => {
  const colorMap: Record<string, string> = {
    'high': 'red',
    'medium': 'orange',
    'low': 'green'
  }
  return colorMap[priority] || 'default'
}

// 获取优先级文本
const getPriorityText = (priority: string) => {
  const textMap: Record<string, string> = {
    'high': '高',
    'medium': '中',
    'low': '低'
  }
  return textMap[priority] || priority
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'unread': 'red',
    'read': 'green'
  }
  return colorMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'unread': '未读',
    'read': '已读'
  }
  return textMap[status] || status
}

// 用户筛选
const filterUserOption = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase())
}
</script>

<style scoped>
.messages-page {
  padding: 24px;
}

.message-stats {
  margin-bottom: 16px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.batch-actions {
  margin-bottom: 16px;
  padding: 12px;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 6px;
}

.message-title {
  cursor: pointer;
}

.unread-title {
  font-weight: bold;
  color: #1890ff;
}

.message-content {
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  white-space: pre-wrap;
  line-height: 1.6;
}
</style>
