package models

import (
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Department 科室/部门表 - 对应 tbl_departments
type Department struct {
	BaseModel
	ParentID     *uuid.UUID `json:"parent_id,omitempty" gorm:"type:uuid;index"`
	Name         string     `json:"name" gorm:"type:varchar(100);not null" validate:"required,max=100"`
	DeptName     string     `json:"dept_name" gorm:"-"` // 虚拟字段，映射到Name
	Code         *string    `json:"code,omitempty" gorm:"type:varchar(50);unique"`
	DeptCode     *string    `json:"dept_code" gorm:"-"` // 虚拟字段，映射到Code
	DeptType     *string    `json:"dept_type,omitempty" gorm:"type:varchar(50)"`
	Level        *int64     `json:"level,omitempty" gorm:"default:1"`
	SortOrder    *int64     `json:"sort_order,omitempty" gorm:"default:0"`
	Description  *string    `json:"description,omitempty" gorm:"type:text"`
	Status       *int64     `json:"status,omitempty" gorm:"default:1"`
	IsActive     *bool      `json:"is_active,omitempty" gorm:"default:true"`
	ManagerID    *uuid.UUID `json:"manager_id,omitempty" gorm:"type:uuid"`
	ContactPhone *string    `json:"contact_phone,omitempty" gorm:"type:varchar(20)"`
	ContactEmail *string    `json:"contact_email,omitempty" gorm:"type:varchar(255)"`

	// 关联关系
	Parent   *Department  `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	Children []Department `json:"children,omitempty" gorm:"foreignKey:ParentID"`
	Manager  *User        `json:"manager,omitempty" gorm:"foreignKey:ManagerID"`
	Users    []User       `json:"users,omitempty" gorm:"foreignKey:DepartmentID"`
}

// TableName 指定表名
func (Department) TableName() string {
	return "tbl_departments"
}

// AfterFind GORM钩子，用于填充虚拟字段
func (d *Department) AfterFind(tx *gorm.DB) error {
	d.DeptName = d.Name
	d.DeptCode = d.Code
	return nil
}

// BeforeSave GORM钩子，用于同步虚拟字段
func (d *Department) BeforeSave(tx *gorm.DB) error {
	if d.DeptName != "" {
		d.Name = d.DeptName
	}
	if d.DeptCode != nil {
		d.Code = d.DeptCode
	}
	return nil
}
