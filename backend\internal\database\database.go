package database

import (
	"fmt"
	"hospital-management/internal/config"
	"hospital-management/internal/models"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func Init(cfg *config.Config) (*gorm.DB, error) {
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%d sslmode=%s TimeZone=Asia/Shanghai",
		cfg.Database.Host, cfg.Database.User, cfg.Database.Password, cfg.Database.DBName, cfg.Database.Port, cfg.Database.SSLMode)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger:                                   logger.Default.LogMode(logger.Info),
		DisableForeignKeyConstraintWhenMigrating: true, // 禁用自动外键约束
	})

	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	return db, nil
}

func AutoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		// 基础模型 - 按依赖顺序排列
		&models.Department{},
		&models.User{},
		&models.Role{},
		&models.UserRole{},
		&models.File{},

		// 审批流模型
		&models.ApprovalFlow{},
		&models.ApprovalNode{},

		// 预算管理模型
		&models.BudgetScheme{},
		&models.BudgetSubject{},
		&models.BudgetItem{},

		// 支出控制模型
		&models.PreApplication{},
		&models.ExpenseApplication{},
		&models.ExpenseDetail{},
		&models.Payment{},

		// 采购管理模型
		&models.Supplier{},
		&models.PurchaseRequisition{},

		// 合同管理模型
		&models.Contract{},
		&models.ContractPaymentSchedule{},

		// 资产管理模型
		&models.AssetCategory{},
		&models.Asset{},
		&models.AssetDepreciationRecord{},
		&models.AssetMaintenanceRecord{},
		&models.AssetChangeRecord{},

		// 消息管理模型
		&models.Message{},
		&models.MessageReceiver{},

		// 迁移管理表
		&models.Migration{},
	)
}

// SmartMigrate 智能迁移 - 只在需要时运行完整迁移
func SmartMigrate(db *gorm.DB) error {
	migrator := NewMigrator(db)
	return migrator.RunInitialMigration()
}
