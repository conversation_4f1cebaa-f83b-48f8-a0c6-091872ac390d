<template>
  <div class="files-page">
    <a-card :bordered="false">
      <template #title>
        <span>文件管理</span>
      </template>

      <template #extra>
        <a-space>
          <a-upload :before-upload="beforeUpload" :show-upload-list="false" multiple @change="handleUpload">
            <a-button type="primary">
              <template #icon>
                <UploadOutlined />
              </template>
              上传文件
            </a-button>
          </a-upload>
          <a-button @click="handleRefresh">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 文件统计 -->
      <div class="file-stats">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card size="small">
              <a-statistic title="文件总数" :value="statistics.total_files" :value-style="{ color: '#1890ff' }" />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic title="存储大小" :value="formatFileSize(statistics.total_size)"
                :value-style="{ color: '#52c41a' }" />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic title="本地存储" :value="formatFileSize(statistics.storage_usage?.local || 0)"
                :value-style="{ color: '#fa8c16' }" />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic title="云端存储" :value="formatFileSize(statistics.storage_usage?.cloud || 0)"
                :value-style="{ color: '#722ed1' }" />
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 搜索区域 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="关键词">
            <a-input v-model:value="searchForm.keyword" placeholder="文件名称" allow-clear style="width: 200px" />
          </a-form-item>

          <a-form-item label="业务类型">
            <a-select v-model:value="searchForm.business_type" placeholder="请选择业务类型" allow-clear style="width: 150px">
              <a-select-option value="contract">合同附件</a-select-option>
              <a-select-option value="expense">报销凭证</a-select-option>
              <a-select-option value="asset">资产文档</a-select-option>
              <a-select-option value="procurement">采购文档</a-select-option>
              <a-select-option value="other">其他</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="文件类型">
            <a-select v-model:value="searchForm.mime_type" placeholder="请选择文件类型" allow-clear style="width: 150px">
              <a-select-option value="image/">图片</a-select-option>
              <a-select-option value="application/pdf">PDF</a-select-option>
              <a-select-option value="application/vnd.ms-excel">Excel</a-select-option>
              <a-select-option value="application/msword">Word</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="上传时间">
            <a-range-picker v-model:value="dateRange" style="width: 240px" @change="handleDateChange" />
          </a-form-item>

          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon>
                  <SearchOutlined />
                </template>
                搜索
              </a-button>
              <a-button @click="handleReset">
                <template #icon>
                  <ReloadOutlined />
                </template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedRowKeys.length > 0">
        <a-space>
          <span>已选择 {{ selectedRowKeys.length }} 个文件</span>
          <a-button danger @click="handleBatchDelete">批量删除</a-button>
          <a-button @click="selectedRowKeys = []">取消选择</a-button>
        </a-space>
      </div>

      <!-- 文件列表 -->
      <a-table :columns="columns" :data-source="fileList" :loading="loading" :pagination="pagination"
        :row-selection="rowSelection" row-key="id" size="middle" @change="handleTableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'file_name'">
            <div class="file-info">
              <a-avatar :src="getFileIcon(record.mime_type)" shape="square" size="small" />
              <a @click="handlePreview(record)" style="margin-left: 8px;">
                {{ record.file_name }}
              </a>
            </div>
          </template>

          <template v-else-if="column.key === 'file_size'">
            <span>{{ formatFileSize(record.file_size || 0) }}</span>
          </template>

          <template v-else-if="column.key === 'business_type'">
            <a-tag :color="getBusinessTypeColor(record.business_type)">
              {{ getBusinessTypeText(record.business_type) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'storage_type'">
            <a-tag :color="record.storage_type === 'local' ? 'blue' : 'green'">
              {{ record.storage_type === 'local' ? '本地' : '云端' }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handlePreview(record)">
                预览
              </a-button>
              <a-button type="link" size="small" @click="handleDownload(record)">
                下载
              </a-button>
              <a-button type="link" size="small" @click="handleShare(record)">
                分享
              </a-button>
              <a-popconfirm title="确定要删除这个文件吗？" @confirm="handleDelete(record)">
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 文件预览弹窗 -->
    <a-modal v-model:open="previewModalVisible" :title="currentFile?.file_name" :footer="null" width="800px" centered>
      <div v-if="currentFile" class="file-preview">
        <div v-if="isImage(currentFile.mime_type)">
          <img :src="getPreviewUrl(currentFile.id)" style="width: 100%; max-height: 500px; object-fit: contain;" />
        </div>
        <div v-else-if="isPdf(currentFile.mime_type)">
          <iframe :src="getPreviewUrl(currentFile.id)" style="width: 100%; height: 500px; border: none;"></iframe>
        </div>
        <div v-else class="file-info-detail">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="文件名称" :span="2">
              {{ currentFile.file_name }}
            </a-descriptions-item>
            <a-descriptions-item label="文件大小">
              {{ formatFileSize(currentFile.file_size || 0) }}
            </a-descriptions-item>
            <a-descriptions-item label="文件类型">
              {{ currentFile.mime_type }}
            </a-descriptions-item>
            <a-descriptions-item label="存储类型">
              {{ currentFile.storage_type === 'local' ? '本地存储' : '云端存储' }}
            </a-descriptions-item>
            <a-descriptions-item label="业务类型" v-if="currentFile.business_type">
              {{ getBusinessTypeText(currentFile.business_type) }}
            </a-descriptions-item>
            <a-descriptions-item label="上传时间">
              {{ currentFile.created_at }}
            </a-descriptions-item>
            <a-descriptions-item label="更新时间">
              {{ currentFile.updated_at }}
            </a-descriptions-item>
          </a-descriptions>

          <div style="margin-top: 16px; text-align: center;">
            <a-button type="primary" @click="handleDownload(currentFile)">
              <template #icon>
                <DownloadOutlined />
              </template>
              下载文件
            </a-button>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 文件分享弹窗 -->
    <a-modal v-model:open="shareModalVisible" title="文件分享" :confirm-loading="shareLoading" @ok="handleShareSubmit"
      @cancel="shareModalVisible = false">
      <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="分享文件">
          <span>{{ shareFile?.file_name }}</span>
        </a-form-item>

        <a-form-item label="过期时间">
          <a-date-picker v-model:value="shareForm.expire_time" show-time placeholder="请选择过期时间（不选择则永不过期）"
            style="width: 100%" />
        </a-form-item>

        <a-form-item label="下载次数限制">
          <a-input-number v-model:value="shareForm.download_limit" :min="1" placeholder="不限制请留空" style="width: 100%" />
        </a-form-item>

        <a-form-item label="访问密码">
          <a-input v-model:value="shareForm.password" placeholder="不设置密码请留空" type="password" />
        </a-form-item>
      </a-form>

      <div v-if="shareResult" class="share-result">
        <a-divider>分享链接</a-divider>
        <a-input :value="shareResult.share_url" readonly addon-after="复制" @click="copyShareUrl" />
        <p style="margin-top: 8px; color: #666; font-size: 12px;">
          分享码：{{ shareResult.share_token }}
        </p>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  UploadOutlined,
  ReloadOutlined,
  SearchOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'
import dayjs, { type Dayjs } from 'dayjs'
import {
  getFiles,
  uploadFile,
  deleteFile,
  batchDeleteFiles,
  downloadFile,
  getFileStatistics,
  createFileShare,
  getFilePreviewUrl,
  type FileInfo,
  type FileQuery,
  type FileShareForm
} from '@/api/file'
import type { UploadFile } from 'ant-design-vue'

const loading = ref(false)
const previewModalVisible = ref(false)
const shareModalVisible = ref(false)
const shareLoading = ref(false)
const selectedRowKeys = ref<string[]>([])

const fileList = ref<FileInfo[]>([])
const currentFile = ref<FileInfo | null>(null)
const shareFile = ref<FileInfo | null>(null)
const shareResult = ref<any>(null)
const dateRange = ref<[Dayjs, Dayjs] | null>(null)

const statistics = ref({
  total_files: 0,
  total_size: 0,
  storage_usage: {
    local: 0,
    cloud: 0
  },
  file_types: [],
  business_types: [],
  monthly_uploads: []
})

const searchForm = reactive<FileQuery>({
  keyword: '',
  business_type: undefined,
  mime_type: undefined,
  start_date: undefined,
  end_date: undefined,
  page: 1,
  page_size: 10
})

const shareForm = reactive<FileShareForm & {
  expire_time: Dayjs | null
}>({
  file_id: '',
  expire_time: null,
  download_limit: undefined,
  password: ''
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '文件名称',
    key: 'file_name',
    width: 300,
    ellipsis: true
  },
  {
    title: '文件大小',
    key: 'file_size',
    width: 100,
    align: 'right'
  },
  {
    title: '文件类型',
    dataIndex: 'mime_type',
    key: 'mime_type',
    width: 150,
    ellipsis: true
  },
  {
    title: '业务类型',
    key: 'business_type',
    width: 120
  },
  {
    title: '存储类型',
    key: 'storage_type',
    width: 100
  },
  {
    title: '上传时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys
  }
}))

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(size) / Math.log(k))
  return parseFloat((size / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取文件图标
const getFileIcon = (mimeType?: string) => {
  if (!mimeType) return '/icons/file-default.png'
  if (mimeType.startsWith('image/')) return '/icons/file-image.png'
  if (mimeType === 'application/pdf') return '/icons/file-pdf.png'
  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return '/icons/file-excel.png'
  if (mimeType.includes('word') || mimeType.includes('document')) return '/icons/file-word.png'
  return '/icons/file-default.png'
}

// 获取业务类型颜色
const getBusinessTypeColor = (type?: string) => {
  const colorMap: Record<string, string> = {
    'contract': 'blue',
    'expense': 'green',
    'asset': 'orange',
    'procurement': 'purple',
    'other': 'default'
  }
  return colorMap[type || 'other'] || 'default'
}

// 获取业务类型文本
const getBusinessTypeText = (type?: string) => {
  const textMap: Record<string, string> = {
    'contract': '合同附件',
    'expense': '报销凭证',
    'asset': '资产文档',
    'procurement': '采购文档',
    'other': '其他'
  }
  return textMap[type || 'other'] || '其他'
}

// 判断是否为图片
const isImage = (mimeType?: string) => {
  return mimeType?.startsWith('image/')
}

// 判断是否为PDF
const isPdf = (mimeType?: string) => {
  return mimeType === 'application/pdf'
}

// 获取预览URL
const getPreviewUrl = (fileId: string) => {
  return getFilePreviewUrl(fileId)
}
</script>

<style scoped>
.files-page {
  padding: 24px;
}

.file-stats {
  margin-bottom: 24px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.batch-actions {
  margin-bottom: 16px;
  padding: 12px;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 6px;
}

.file-info {
  display: flex;
  align-items: center;
}

.file-preview {
  text-align: center;
}

.file-info-detail {
  padding: 16px;
}

.share-result {
  margin-top: 16px;
}
</style>
