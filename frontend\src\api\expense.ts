import request from './request'

// 事前申请相关接口
export interface PreApplication {
  id: number
  type: 'TRAVEL' | 'MEETING' | 'TRAINING'
  title: string
  applicant_id: number
  applicant_name: string
  department_id: number
  department_name: string
  estimated_amount: number
  budget_item_id?: number
  start_date: string
  end_date: string
  destination?: string
  purpose: string
  participants?: string
  status: 'DRAFT' | 'PENDING' | 'APPROVED' | 'REJECTED' | 'CLOSED'
  created_at: string
  updated_at: string
}

export interface PreApplicationForm {
  type: 'travel' | 'meeting' | 'training' | 'other'
  title: string
  department_id: number
  estimated_amount: number
  budget_item_id?: number
  start_date: string
  end_date: string
  destination?: string
  purpose: string
  participants?: string
}

export interface PreApplicationQuery {
  type?: string
  status?: string
  applicant_id?: number
  department_id?: number
  start_date?: string
  end_date?: string
  page?: number
  page_size?: number
}

// 获取事前申请列表
export const getPreApplications = (params: PreApplicationQuery): Promise<{
  list: PreApplication[]
  total: number
  page: number
  page_size: number
}> => {
  return request.get('/api/v1/pre-applications', { params })
}

// 获取事前申请详情
export const getPreApplicationDetail = (id: number): Promise<PreApplication> => {
  return request.get(`/api/v1/pre-applications/${id}`)
}

// 创建事前申请
export const createPreApplication = (data: PreApplicationForm): Promise<PreApplication> => {
  return request.post('/api/v1/pre-applications', data)
}

// 更新事前申请
export const updatePreApplication = (id: number, data: PreApplicationForm): Promise<PreApplication> => {
  return request.put(`/api/v1/pre-applications/${id}`, data)
}

// 提交事前申请
export const submitPreApplication = (id: number): Promise<void> => {
  return request.post(`/api/v1/pre-applications/${id}/submit`)
}

// 费用报销相关接口
export interface ExpenseApplication {
  id: number
  title: string
  applicant_id: number
  applicant_name: string
  department_id: number
  department_name: string
  total_amount: number
  pre_application_id?: number
  pre_application_title?: string
  status: 'DRAFT' | 'PENDING' | 'APPROVED' | 'REJECTED' | 'PAID'
  items: ExpenseItem[]
  created_at: string
  updated_at: string
}

export interface ExpenseItem {
  id?: number
  expense_type: string
  expense_date: string
  amount: number
  description: string
  invoice_number?: string
  invoice_amount?: number
}

export interface ExpenseApplicationForm {
  title: string
  department_id: number
  pre_application_id?: number
  items: ExpenseItem[]
}

export interface ExpenseApplicationQuery {
  status?: string
  applicant_id?: number
  department_id?: number
  start_date?: string
  end_date?: string
  page?: number
  page_size?: number
}

// 获取报销申请列表
export const getExpenseApplications = (params: ExpenseApplicationQuery): Promise<{
  list: ExpenseApplication[]
  total: number
  page: number
  page_size: number
}> => {
  return request.get('/api/v1/expense/applications', { params })
}

// 获取报销申请详情
export const getExpenseApplicationDetail = (id: number): Promise<ExpenseApplication> => {
  return request.get(`/api/v1/expense/applications/${id}`)
}

// 创建报销申请
export const createExpenseApplication = (data: ExpenseApplicationForm): Promise<ExpenseApplication> => {
  return request.post('/api/v1/expense/applications', data)
}

// 更新报销申请
export const updateExpenseApplication = (id: number, data: ExpenseApplicationForm): Promise<ExpenseApplication> => {
  return request.put(`/api/v1/expense/applications/${id}`, data)
}

// 提交报销申请
export const submitExpenseApplication = (id: number): Promise<void> => {
  return request.post(`/api/v1/expense/applications/${id}/submit`)
}

// 获取我的报销记录
export const getMyExpenseApplications = (params: ExpenseApplicationQuery): Promise<{
  list: ExpenseApplication[]
  total: number
  page: number
  page_size: number
}> => {
  return request.get('/api/v1/expense/applications/my', { params })
}

// 审批相关接口
export interface ApprovalTask {
  id: number
  business_type: string
  business_id: number
  business_title: string
  applicant_name: string
  department_name: string
  amount?: number
  current_node: string
  status: 'PENDING' | 'APPROVED' | 'REJECTED'
  created_at: string
  updated_at: string
}

export interface ApprovalAction {
  action: 'approve' | 'reject' | 'transfer'
  comment?: string
  transfer_to?: number
}

// 获取待办任务
export const getTodoTasks = (): Promise<ApprovalTask[]> => {
  return request.get('/api/v1/approval/todo').then(response => {
    // 响应拦截器返回的是 data 部分，需要获取 list
    return response?.list || []
  })
}

// 审批操作
export const approveTask = (nodeId: number, data: ApprovalAction): Promise<void> => {
  return request.post(`/api/v1/approval/approve/${nodeId}`, data)
}

// 驳回操作
export const rejectTask = (nodeId: number, data: ApprovalAction): Promise<void> => {
  return request.post(`/api/v1/approval/reject/${nodeId}`, data)
}

// 转办操作
export const transferTask = (nodeId: number, data: ApprovalAction): Promise<void> => {
  return request.post(`/api/v1/approval/transfer/${nodeId}`, data)
}

// 付款管理相关接口
export interface Payment {
  id: number
  payment_no: string
  payee_type: 'employee' | 'supplier'
  payee_id: number
  payee_name: string
  bank_account?: string
  bank_name?: string
  amount: number
  payment_method: 'bank_transfer' | 'cash' | 'check'
  business_type: string
  business_id: number
  business_title: string
  status: 'PENDING' | 'PAID' | 'FAILED'
  payment_date?: string
  remark?: string
  created_at: string
  updated_at: string
}

export interface PaymentForm {
  payee_type: 'employee' | 'supplier'
  payee_id: number
  bank_account?: string
  bank_name?: string
  amount: number
  payment_method: 'bank_transfer' | 'cash' | 'check'
  business_type: string
  business_id: number
  payment_date?: string
  remark?: string
}

export interface PaymentQuery {
  status?: string
  payee_type?: string
  payment_method?: string
  start_date?: string
  end_date?: string
  page?: number
  page_size?: number
}

// 获取付款单列表
export const getPayments = (params: PaymentQuery): Promise<{
  list: Payment[]
  total: number
  page: number
  page_size: number
}> => {
  return request.get('/api/v1/payments', { params })
}

// 创建付款单
export const createPayment = (data: PaymentForm): Promise<Payment> => {
  return request.post('/api/v1/payments', data)
}

// 更新付款状态
export const updatePaymentStatus = (id: number, status: string, remark?: string): Promise<void> => {
  return request.put(`/api/v1/payments/${id}/status`, { status, remark })
}

// 批量付款
export const batchPayment = (paymentIds: number[]): Promise<void> => {
  return request.post('/api/v1/payments/batch', { payment_ids: paymentIds })
}
