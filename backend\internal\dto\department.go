package dto

import (
	"github.com/google/uuid"
)

// CreateDepartmentRequest 创建部门请求
type CreateDepartmentRequest struct {
	ParentID     *string `json:"parent_id,omitempty" validate:"omitempty,uuid"`
	DeptName     string  `json:"dept_name" validate:"required,max=100"`
	DeptCode     *string `json:"dept_code,omitempty" validate:"omitempty,max=50"`
	DeptType     string  `json:"dept_type" validate:"required,oneof=clinical administrative logistics"`
	Description  *string `json:"description,omitempty" validate:"omitempty,max=500"`
	ManagerID    *string `json:"manager_id,omitempty" validate:"omitempty,uuid"`
	ContactPhone *string `json:"contact_phone,omitempty" validate:"omitempty,max=20"`
	ContactEmail *string `json:"contact_email,omitempty" validate:"omitempty,email"`
	IsActive     bool    `json:"is_active"`
}

// UpdateDepartmentRequest 更新部门请求
type UpdateDepartmentRequest struct {
	ParentID     *string `json:"parent_id,omitempty" validate:"omitempty,uuid"`
	DeptName     *string `json:"dept_name,omitempty" validate:"omitempty,max=100"`
	DeptCode     *string `json:"dept_code,omitempty" validate:"omitempty,max=50"`
	DeptType     *string `json:"dept_type,omitempty" validate:"omitempty,oneof=clinical administrative logistics"`
	Description  *string `json:"description,omitempty" validate:"omitempty,max=500"`
	ManagerID    *string `json:"manager_id,omitempty" validate:"omitempty,uuid"`
	ContactPhone *string `json:"contact_phone,omitempty" validate:"omitempty,max=20"`
	ContactEmail *string `json:"contact_email,omitempty" validate:"omitempty,email"`
	IsActive     *bool   `json:"is_active,omitempty"`
}

// DepartmentResponse 部门响应
type DepartmentResponse struct {
	ID           uuid.UUID            `json:"id"`
	ParentID     *uuid.UUID           `json:"parent_id,omitempty"`
	DeptName     string               `json:"dept_name"`
	DeptCode     *string              `json:"dept_code,omitempty"`
	DeptType     string               `json:"dept_type"`
	Description  string               `json:"description"`
	ManagerID    *uuid.UUID           `json:"manager_id,omitempty"`
	ContactPhone string               `json:"contact_phone"`
	ContactEmail string               `json:"contact_email"`
	IsActive     bool                 `json:"is_active"`
	CreatedAt    string               `json:"created_at"`
	UpdatedAt    string               `json:"updated_at"`
	Manager      *UserSimpleResponse  `json:"manager,omitempty"`
	Children     []DepartmentResponse `json:"children,omitempty"`
}

// DepartmentTreeResponse 部门树形响应
type DepartmentTreeResponse struct {
	ID       uuid.UUID                `json:"id"`
	DeptName string                   `json:"dept_name"`
	DeptCode *string                  `json:"dept_code,omitempty"`
	DeptType string                   `json:"dept_type"`
	IsActive bool                     `json:"is_active"`
	Children []DepartmentTreeResponse `json:"children,omitempty"`
}

// DepartmentListRequest 部门列表请求
type DepartmentListRequest struct {
	Page     int    `form:"page" validate:"min=1"`
	PageSize int    `form:"page_size" validate:"min=1,max=100"`
	Keyword  string `form:"keyword"`
	DeptType string `form:"dept_type" validate:"omitempty,oneof=clinical administrative logistics"`
	IsActive *bool  `form:"is_active"`
}

// UserSimpleResponse 已移至 common.go
