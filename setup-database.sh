#!/bin/bash

# 数据库初始化脚本

set -e

echo "开始设置数据库..."

# 配置变量
DB_NAME="hospital_management"
DB_USER="hospital_user"
DB_PASSWORD="your_secure_password_here"

# 检查 PostgreSQL 是否安装
if ! command -v psql &> /dev/null; then
    echo "PostgreSQL 未安装，正在安装..."
    sudo apt update
    sudo apt install -y postgresql postgresql-contrib
fi

# 启动 PostgreSQL 服务
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 创建数据库和用户
echo "创建数据库和用户..."
sudo -u postgres psql <<EOF
-- 创建用户
CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';

-- 创建数据库
CREATE DATABASE $DB_NAME OWNER $DB_USER;

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;

-- 授予创建权限（用于扩展）
ALTER USER $DB_USER CREATEDB;

\q
EOF

# 连接到新数据库并创建扩展
echo "创建数据库扩展..."
sudo -u postgres psql -d $DB_NAME <<EOF
-- 创建 UUID 扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建 pgcrypto 扩展（用于密码加密）
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- 授予用户对扩展的使用权限
GRANT USAGE ON SCHEMA public TO $DB_USER;
GRANT CREATE ON SCHEMA public TO $DB_USER;

\q
EOF

echo "数据库设置完成！"
echo ""
echo "数据库信息："
echo "  数据库名: $DB_NAME"
echo "  用户名: $DB_USER"
echo "  密码: $DB_PASSWORD"
echo ""
echo "请更新配置文件中的数据库连接信息。"
