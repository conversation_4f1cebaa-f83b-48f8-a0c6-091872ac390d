package models

import (
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// PurchaseRequisition 采购需求单 - 对应 tbl_purchase_requisitions
type PurchaseRequisition struct {
	BaseModel
	RequisitionCode string           `json:"requisition_code" gorm:"type:varchar(50);unique;not null" validate:"required,max=50"`
	ApplicantID     uuid.UUID        `json:"applicant_id" gorm:"type:uuid;not null" validate:"required"`
	DepartmentID    uuid.UUID        `json:"department_id" gorm:"type:uuid;not null" validate:"required"`
	Title           string           `json:"title" gorm:"type:varchar(255);not null" validate:"required,max=255"`
	Details         *JSONMap         `json:"details,omitempty" gorm:"type:jsonb"`
	TotalAmount     *decimal.Decimal `json:"total_amount,omitempty" gorm:"type:decimal(18,2)"`
	Status          string           `json:"status" gorm:"type:varchar(30);not null" validate:"required,max=30"`

	// 关联关系
	Applicant  *User       `json:"applicant,omitempty" gorm:"foreignKey:ApplicantID"`
	Department *Department `json:"department,omitempty" gorm:"foreignKey:DepartmentID"`
}

// TableName 指定表名
func (PurchaseRequisition) TableName() string {
	return "tbl_purchase_requisitions"
}
