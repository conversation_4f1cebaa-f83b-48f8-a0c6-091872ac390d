<template>
  <div class="procurement-requisitions-page">
    <a-card :bordered="false">
      <template #title>
        <span>采购申请管理</span>
      </template>

      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <PlusOutlined />
            </template>
            新建申请
          </a-button>
          <a-button @click="handleRefresh">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 搜索区域 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="申请状态">
            <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear style="width: 120px">
              <a-select-option value="draft">草稿</a-select-option>
              <a-select-option value="submitted">已提交</a-select-option>
              <a-select-option value="approved">已批准</a-select-option>
              <a-select-option value="rejected">已驳回</a-select-option>
              <a-select-option value="completed">已完成</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="紧急程度">
            <a-select v-model:value="searchForm.urgency" placeholder="请选择紧急程度" allow-clear style="width: 120px">
              <a-select-option value="low">低</a-select-option>
              <a-select-option value="medium">中</a-select-option>
              <a-select-option value="high">高</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="供应商">
            <a-select v-model:value="searchForm.supplier_id" placeholder="请选择供应商" allow-clear style="width: 200px"
              show-search :filter-option="filterSupplierOption" :options="supplierOptions" />
          </a-form-item>

          <a-form-item label="申请时间">
            <a-range-picker v-model:value="dateRange" style="width: 240px" @change="handleDateChange" />
          </a-form-item>

          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon>
                  <SearchOutlined />
                </template>
                搜索
              </a-button>
              <a-button @click="handleReset">
                <template #icon>
                  <ReloadOutlined />
                </template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 采购申请列表 -->
      <a-table :columns="columns" :data-source="requisitionList" :loading="loading" :pagination="pagination"
        row-key="id" size="middle" @change="handleTableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'requisition_no'">
            <a @click="handleView(record)">{{ record.requisition_no }}</a>
          </template>

          <template v-else-if="column.key === 'urgency'">
            <a-tag :color="getUrgencyColor(record.urgency)">
              {{ getUrgencyText(record.urgency) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'total_amount'">
            <span class="amount-text">{{ formatAmount(record.total_amount) }}</span>
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleView(record)">
                查看
              </a-button>
              <a-button type="link" size="small" @click="handleEdit(record)" v-if="record.status === 'draft'">
                编辑
              </a-button>
              <a-button type="link" size="small" @click="handleSubmit(record)" v-if="record.status === 'draft'">
                提交
              </a-button>
              <a-popconfirm title="确定要删除这个采购申请吗？" @confirm="handleDelete(record)" v-if="record.status === 'draft'">
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑采购申请弹窗 -->
    <a-modal v-model:open="modalVisible" :title="modalTitle" :confirm-loading="modalLoading" width="1200px"
      @ok="handleModalSubmit" @cancel="handleCancel">
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="申请标题" name="title">
          <a-input v-model:value="formData.title" placeholder="请输入申请标题" />
        </a-form-item>

        <a-form-item label="所属部门" name="department_id">
          <a-tree-select v-model:value="formData.department_id" :tree-data="departmentTreeOptions" placeholder="请选择部门"
            tree-default-expand-all />
        </a-form-item>

        <a-form-item label="供应商" name="supplier_id">
          <a-select v-model:value="formData.supplier_id" placeholder="请选择供应商（可选）" allow-clear show-search
            :filter-option="filterSupplierOption" :options="supplierOptions" />
        </a-form-item>

        <a-form-item label="紧急程度" name="urgency">
          <a-radio-group v-model:value="formData.urgency">
            <a-radio value="low">低</a-radio>
            <a-radio value="medium">中</a-radio>
            <a-radio value="high">高</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="期望交付日期" name="expected_date">
          <a-date-picker v-model:value="formData.expected_date" placeholder="请选择期望交付日期" style="width: 100%" />
        </a-form-item>

        <a-form-item label="采购用途" name="purpose">
          <a-textarea v-model:value="formData.purpose" placeholder="请输入采购用途" :rows="3" />
        </a-form-item>

        <a-form-item label="采购明细" name="items">
          <div class="procurement-items">
            <a-table :columns="itemColumns" :data-source="formData.items" :pagination="false" row-key="key"
              size="small">
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'name'">
                  <a-input v-model:value="record.name" placeholder="商品/服务名称" />
                </template>

                <template v-else-if="column.key === 'specification'">
                  <a-input v-model:value="record.specification" placeholder="规格型号" />
                </template>

                <template v-else-if="column.key === 'unit'">
                  <a-input v-model:value="record.unit" placeholder="单位" />
                </template>

                <template v-else-if="column.key === 'quantity'">
                  <a-input-number v-model:value="record.quantity" :min="0" placeholder="数量" style="width: 100%"
                    @change="calculateItemTotal(record)" />
                </template>

                <template v-else-if="column.key === 'unit_price'">
                  <a-input-number v-model:value="record.unit_price" :min="0" :precision="2" placeholder="单价"
                    style="width: 100%" @change="calculateItemTotal(record)" />
                </template>

                <template v-else-if="column.key === 'total_price'">
                  <span class="amount-text">{{ formatAmount(record.total_price || 0) }}</span>
                </template>

                <template v-else-if="column.key === 'remark'">
                  <a-input v-model:value="record.remark" placeholder="备注" />
                </template>

                <template v-else-if="column.key === 'action'">
                  <a-button type="link" size="small" danger @click="removeItem(index)">
                    删除
                  </a-button>
                </template>
              </template>
            </a-table>

            <div class="procurement-actions">
              <a-button type="dashed" @click="addItem" block>
                <template #icon>
                  <PlusOutlined />
                </template>
                添加采购明细
              </a-button>
            </div>

            <div class="total-amount">
              <span>总金额：<strong class="amount-text">{{ formatAmount(totalAmount) }}</strong></span>
            </div>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 查看详情弹窗 -->
    <a-modal v-model:open="viewModalVisible" title="采购申请详情" :footer="null" width="1000px">
      <div v-if="currentRecord">
        <a-descriptions :column="2" bordered class="detail-descriptions">
          <a-descriptions-item label="申请单号" :span="2">
            {{ currentRecord.requisition_no }}
          </a-descriptions-item>
          <a-descriptions-item label="申请标题" :span="2">
            {{ currentRecord.title }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人">
            {{ currentRecord.applicant_name }}
          </a-descriptions-item>
          <a-descriptions-item label="所属部门">
            {{ currentRecord.department_name }}
          </a-descriptions-item>
          <a-descriptions-item label="供应商" v-if="currentRecord.supplier_name">
            {{ currentRecord.supplier_name }}
          </a-descriptions-item>
          <a-descriptions-item label="紧急程度">
            <a-tag :color="getUrgencyColor(currentRecord.urgency)">
              {{ getUrgencyText(currentRecord.urgency) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="总金额">
            <span class="amount-text">{{ formatAmount(currentRecord.total_amount) }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="申请状态">
            <a-tag :color="getStatusColor(currentRecord.status)">
              {{ getStatusText(currentRecord.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="期望交付日期">
            {{ currentRecord.expected_date }}
          </a-descriptions-item>
          <a-descriptions-item label="申请时间">
            {{ currentRecord.created_at }}
          </a-descriptions-item>
          <a-descriptions-item label="采购用途" :span="2">
            {{ currentRecord.purpose }}
          </a-descriptions-item>
        </a-descriptions>

        <a-divider>采购明细</a-divider>

        <a-table :columns="viewItemColumns" :data-source="currentRecord.items" :pagination="false" row-key="id"
          size="small" />
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'
import dayjs, { type Dayjs } from 'dayjs'
import {
  getProcurementRequisitions,
  createProcurementRequisition,
  updateProcurementRequisition,
  submitProcurementRequisition,
  deleteProcurementRequisition,
  getSuppliers,
  type ProcurementRequisition,
  type ProcurementRequisitionForm,
  type ProcurementRequisitionQuery,
  type ProcurementItem,
  type Supplier
} from '@/api/procurement'
import { getDepartmentTree, type Department } from '@/api/system'
import type { Rule } from 'ant-design-vue/es/form'
import type { FormInstance } from 'ant-design-vue'

const loading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const viewModalVisible = ref(false)
const formRef = ref<FormInstance>()
const editingId = ref<number | null>(null)

const requisitionList = ref<ProcurementRequisition[]>([])
const supplierList = ref<Supplier[]>([])
const departmentList = ref<Department[]>([])
const currentRecord = ref<ProcurementRequisition | null>(null)
const dateRange = ref<[Dayjs, Dayjs] | null>(null)

const searchForm = reactive<ProcurementRequisitionQuery>({
  status: undefined,
  urgency: undefined,
  supplier_id: undefined,
  start_date: undefined,
  end_date: undefined,
  page: 1,
  page_size: 10
})

const formData = reactive<ProcurementRequisitionForm & {
  expected_date: Dayjs | null
  items: (ProcurementItem & { key: number })[]
}>({
  title: '',
  department_id: 0,
  supplier_id: undefined,
  urgency: 'medium',
  expected_date: null,
  purpose: '',
  items: []
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '申请单号',
    key: 'requisition_no',
    width: 150
  },
  {
    title: '申请标题',
    dataIndex: 'title',
    key: 'title',
    width: 200,
    ellipsis: true
  },
  {
    title: '申请人',
    dataIndex: 'applicant_name',
    key: 'applicant_name',
    width: 100
  },
  {
    title: '所属部门',
    dataIndex: 'department_name',
    key: 'department_name',
    width: 150
  },
  {
    title: '供应商',
    dataIndex: 'supplier_name',
    key: 'supplier_name',
    width: 150,
    ellipsis: true
  },
  {
    title: '总金额',
    key: 'total_amount',
    width: 120,
    align: 'right'
  },
  {
    title: '紧急程度',
    key: 'urgency',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '期望交付日期',
    dataIndex: 'expected_date',
    key: 'expected_date',
    width: 120
  },
  {
    title: '申请时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 采购明细表格列配置
const itemColumns = [
  {
    title: '商品/服务名称',
    key: 'name',
    width: 150
  },
  {
    title: '规格型号',
    key: 'specification',
    width: 120
  },
  {
    title: '单位',
    key: 'unit',
    width: 80
  },
  {
    title: '数量',
    key: 'quantity',
    width: 100
  },
  {
    title: '单价',
    key: 'unit_price',
    width: 100
  },
  {
    title: '总价',
    key: 'total_price',
    width: 100
  },
  {
    title: '备注',
    key: 'remark',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 80
  }
]

// 查看详情时的明细列配置
const viewItemColumns = [
  {
    title: '商品/服务名称',
    dataIndex: 'name',
    key: 'name',
    width: 150
  },
  {
    title: '规格型号',
    dataIndex: 'specification',
    key: 'specification',
    width: 120
  },
  {
    title: '单位',
    dataIndex: 'unit',
    key: 'unit',
    width: 80
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    key: 'quantity',
    width: 100
  },
  {
    title: '单价',
    dataIndex: 'unit_price',
    key: 'unit_price',
    width: 100,
    align: 'right',
    customRender: ({ text }: any) => formatAmount(text)
  },
  {
    title: '总价',
    dataIndex: 'total_price',
    key: 'total_price',
    width: 100,
    align: 'right',
    customRender: ({ text }: any) => formatAmount(text)
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark'
  }
]

// 表单验证规则
const rules: Record<string, Rule[]> = {
  title: [
    { required: true, message: '请输入申请标题', trigger: 'blur' }
  ],
  department_id: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ],
  urgency: [
    { required: true, message: '请选择紧急程度', trigger: 'change' }
  ],
  expected_date: [
    { required: true, message: '请选择期望交付日期', trigger: 'change' }
  ],
  purpose: [
    { required: true, message: '请输入采购用途', trigger: 'blur' }
  ],
  items: [
    { required: true, message: '请添加采购明细', trigger: 'change' }
  ]
}

// 弹窗标题
const modalTitle = computed(() => {
  return editingId.value ? '编辑采购申请' : '新建采购申请'
})

// 供应商选项
const supplierOptions = computed(() => {
  return supplierList.value
    .filter(supplier => supplier.status === 'active')
    .map(supplier => ({
      label: supplier.name,
      value: supplier.id
    }))
})

// 部门树形选择器选项
const departmentTreeOptions = computed(() => {
  const convertToTreeData = (departments: Department[]): any[] => {
    return departments.map(dept => ({
      title: dept.name,
      value: dept.id,
      key: dept.id,
      children: dept.children ? convertToTreeData(dept.children) : undefined
    }))
  }
  return convertToTreeData(departmentList.value)
})

// 计算总金额
const totalAmount = computed(() => {
  return formData.items.reduce((sum, item) => sum + (item.total_price || 0), 0)
})

// 获取紧急程度颜色
const getUrgencyColor = (urgency: string) => {
  const colorMap: Record<string, string> = {
    'low': 'green',
    'medium': 'orange',
    'high': 'red'
  }
  return colorMap[urgency] || 'default'
}

// 获取紧急程度文本
const getUrgencyText = (urgency: string) => {
  const textMap: Record<string, string> = {
    'low': '低',
    'medium': '中',
    'high': '高'
  }
  return textMap[urgency] || urgency
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'draft': 'default',
    'submitted': 'blue',
    'approved': 'green',
    'rejected': 'red',
    'completed': 'purple'
  }
  return colorMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'draft': '草稿',
    'submitted': '已提交',
    'approved': '已批准',
    'rejected': '已驳回',
    'completed': '已完成'
  }
  return textMap[status] || status
}

// 格式化金额
const formatAmount = (amount: number) => {
  return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`
}

// 供应商筛选
const filterSupplierOption = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase())
}

// 加载采购申请数据
const loadRequisitions = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.current,
      page_size: pagination.pageSize
    }
    const response = await getProcurementRequisitions(params)
    requisitionList.value = response.list
    pagination.total = response.total
  } catch (error) {
    message.error('加载采购申请数据失败')
  } finally {
    loading.value = false
  }
}

// 加载基础数据
const loadBaseData = async () => {
  try {
    const [suppliers, departments] = await Promise.all([
      getSuppliers({ page: 1, page_size: 100 }),
      getDepartmentTree()
    ])
    supplierList.value = suppliers.list
    departmentList.value = departments
  } catch (error) {
    message.error('加载基础数据失败')
  }
}

// 日期范围变化处理
const handleDateChange = (dates: [Dayjs, Dayjs] | null) => {
  if (dates) {
    searchForm.start_date = dates[0].format('YYYY-MM-DD')
    searchForm.end_date = dates[1].format('YYYY-MM-DD')
  } else {
    searchForm.start_date = undefined
    searchForm.end_date = undefined
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadRequisitions()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    status: undefined,
    urgency: undefined,
    supplier_id: undefined,
    start_date: undefined,
    end_date: undefined
  })
  dateRange.value = null
  pagination.current = 1
  loadRequisitions()
}

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadRequisitions()
}

// 新建申请
const handleAdd = () => {
  editingId.value = null
  resetForm()
  addItem() // 默认添加一行明细
  modalVisible.value = true
}

// 编辑申请
const handleEdit = (record: ProcurementRequisition) => {
  editingId.value = record.id
  Object.assign(formData, {
    title: record.title,
    department_id: record.department_id,
    supplier_id: record.supplier_id,
    urgency: record.urgency,
    expected_date: dayjs(record.expected_date),
    purpose: record.purpose,
    items: record.items.map((item, index) => ({
      ...item,
      key: index
    }))
  })
  modalVisible.value = true
}

// 查看详情
const handleView = (record: ProcurementRequisition) => {
  currentRecord.value = record
  viewModalVisible.value = true
}

// 提交申请
const handleSubmit = (record: ProcurementRequisition) => {
  Modal.confirm({
    title: '确认提交',
    content: '提交后将无法修改，确定要提交这个采购申请吗？',
    onOk: async () => {
      try {
        await submitProcurementRequisition(record.id)
        message.success('提交成功')
        loadRequisitions()
      } catch (error) {
        message.error('提交失败')
      }
    }
  })
}

// 删除申请
const handleDelete = async (record: ProcurementRequisition) => {
  try {
    await deleteProcurementRequisition(record.id)
    message.success('删除成功')
    loadRequisitions()
  } catch (error) {
    message.error('删除失败')
  }
}

// 刷新数据
const handleRefresh = () => {
  loadRequisitions()
}

// 添加采购明细
const addItem = () => {
  const newItem = {
    key: Date.now(),
    name: '',
    specification: '',
    unit: '',
    quantity: 0,
    unit_price: 0,
    total_price: 0,
    remark: ''
  }
  formData.items.push(newItem)
}

// 移除采购明细
const removeItem = (index: number) => {
  formData.items.splice(index, 1)
}

// 计算明细总价
const calculateItemTotal = (item: any) => {
  item.total_price = (item.quantity || 0) * (item.unit_price || 0)
}

// 提交表单
const handleModalSubmit = async () => {
  try {
    await formRef.value?.validate()

    if (formData.items.length === 0) {
      message.error('请添加采购明细')
      return
    }

    modalLoading.value = true

    const submitData = {
      title: formData.title,
      department_id: formData.department_id,
      supplier_id: formData.supplier_id,
      urgency: formData.urgency,
      expected_date: formData.expected_date?.format('YYYY-MM-DD') || '',
      purpose: formData.purpose,
      items: formData.items.map(item => ({
        name: item.name,
        specification: item.specification,
        unit: item.unit,
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: item.total_price,
        remark: item.remark
      }))
    }

    if (editingId.value) {
      await updateProcurementRequisition(editingId.value, submitData)
      message.success('更新成功')
    } else {
      await createProcurementRequisition(submitData)
      message.success('创建成功')
    }

    modalVisible.value = false
    loadRequisitions()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    modalLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    title: '',
    department_id: 0,
    supplier_id: undefined,
    urgency: 'medium',
    expected_date: null,
    purpose: '',
    items: []
  })
  formRef.value?.resetFields()
}

onMounted(() => {
  loadBaseData()
  loadRequisitions()
})
</script>

<style scoped>
.procurement-requisitions-page {
  padding: 24px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.amount-text {
  font-weight: 500;
  color: #1890ff;
}

.procurement-items {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
}

.procurement-actions {
  margin-top: 16px;
}

.total-amount {
  margin-top: 16px;
  text-align: right;
  font-size: 16px;
}

.detail-descriptions {
  margin-bottom: 16px;
}
</style>
