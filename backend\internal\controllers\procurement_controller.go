package controllers

import (
	"hospital-management/internal/dto"
	"hospital-management/internal/services"
	"hospital-management/pkg/logger"
	customValidator "hospital-management/pkg/validator"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
)

type ProcurementController struct {
	procurementService *services.ProcurementService
	validator          *validator.Validate
}

func NewProcurementController(procurementService *services.ProcurementService) *ProcurementController {
	return &ProcurementController{
		procurementService: procurementService,
		validator:          customValidator.NewValidator(),
	}
}

// ===== 供应商管理 =====

// GetSuppliers 获取供应商列表
// @Summary 获取供应商列表
// @Description 获取供应商列表，支持分页、搜索和筛选
// @Tags 采购管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param keyword query string false "搜索关键词"
// @Param status query int false "状态" Enums(0, 1)
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /suppliers [get]
func (c *ProcurementController) GetSuppliers(ctx *gin.Context) {
	var req dto.SupplierListRequest

	// 设置默认值
	req.Page = 1
	req.PageSize = 10

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	suppliers, total, err := c.procurementService.GetSuppliers(&req)
	if err != nil {
		logger.Error("Failed to get suppliers:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取供应商列表失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      suppliers,
			"total":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	})
}

// CreateSupplier 创建供应商
// @Summary 创建供应商
// @Description 创建新的供应商
// @Tags 采购管理
// @Accept json
// @Produce json
// @Param request body dto.CreateSupplierRequest true "创建供应商请求"
// @Success 201 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /suppliers [post]
func (c *ProcurementController) CreateSupplier(ctx *gin.Context) {
	var req dto.CreateSupplierRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	supplier, err := c.procurementService.CreateSupplier(&req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to create supplier:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建供应商失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "创建成功",
		"data":    supplier,
	})
}

// UpdateSupplier 更新供应商信息
// @Summary 更新供应商信息
// @Description 更新供应商的基本信息
// @Tags 采购管理
// @Accept json
// @Produce json
// @Param id path string true "供应商ID"
// @Param request body dto.UpdateSupplierRequest true "更新供应商请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "供应商不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /suppliers/{id} [put]
func (c *ProcurementController) UpdateSupplier(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的供应商ID",
		})
		return
	}

	var req dto.UpdateSupplierRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	supplier, err := c.procurementService.UpdateSupplier(id, &req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to update supplier:", err)
		if err.Error() == "供应商不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "更新供应商失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    supplier,
	})
}

// UpdateSupplierStatus 更新供应商状态
// @Summary 更新供应商状态
// @Description 更新供应商的状态（启用/禁用）
// @Tags 采购管理
// @Accept json
// @Produce json
// @Param id path string true "供应商ID"
// @Param request body dto.UpdateSupplierStatusRequest true "更新供应商状态请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "供应商不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /suppliers/{id}/status [put]
func (c *ProcurementController) UpdateSupplierStatus(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的供应商ID",
		})
		return
	}

	var req dto.UpdateSupplierStatusRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	supplier, err := c.procurementService.UpdateSupplierStatus(id, &req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to update supplier status:", err)
		if err.Error() == "供应商不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "更新供应商状态失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    supplier,
	})
}

// ===== 采购申请管理 =====

// GetPurchaseRequisitions 获取采购申请列表
// @Summary 获取采购申请列表
// @Description 获取采购申请列表，支持分页、搜索和筛选
// @Tags 采购管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param keyword query string false "搜索关键词"
// @Param status query string false "状态" Enums(DRAFT, PENDING, APPROVED, REJECTED, CLOSED)
// @Param applicant_id query string false "申请人ID"
// @Param department_id query string false "部门ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /purchase/requisitions [get]
func (c *ProcurementController) GetPurchaseRequisitions(ctx *gin.Context) {
	var req dto.PurchaseRequisitionListRequest

	// 设置默认值
	req.Page = 1
	req.PageSize = 10

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	requisitions, total, err := c.procurementService.GetPurchaseRequisitions(&req)
	if err != nil {
		logger.Error("Failed to get purchase requisitions:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取采购申请列表失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      requisitions,
			"total":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	})
}

// GetPurchaseRequisition 获取采购申请详情
// @Summary 获取采购申请详情
// @Description 获取指定采购申请的详细信息
// @Tags 采购管理
// @Accept json
// @Produce json
// @Param id path string true "采购申请ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "采购申请不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /purchase/requisitions/{id} [get]
func (c *ProcurementController) GetPurchaseRequisition(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的采购申请ID",
		})
		return
	}

	// 调用服务
	requisition, err := c.procurementService.GetPurchaseRequisition(id)
	if err != nil {
		logger.Error("Failed to get purchase requisition:", err)
		if err.Error() == "采购申请不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "获取采购申请详情失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    requisition,
	})
}

// CreatePurchaseRequisition 创建采购申请
// @Summary 创建采购申请
// @Description 创建新的采购申请
// @Tags 采购管理
// @Accept json
// @Produce json
// @Param request body dto.CreatePurchaseRequisitionRequest true "创建采购申请请求"
// @Success 201 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /purchase/requisitions [post]
func (c *ProcurementController) CreatePurchaseRequisition(ctx *gin.Context) {
	var req dto.CreatePurchaseRequisitionRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	requisition, err := c.procurementService.CreatePurchaseRequisition(&req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to create purchase requisition:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建采购申请失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "创建成功",
		"data":    requisition,
	})
}

// UpdatePurchaseRequisition 更新采购申请
// @Summary 更新采购申请
// @Description 更新采购申请信息
// @Tags 采购管理
// @Accept json
// @Produce json
// @Param id path string true "采购申请ID"
// @Param request body dto.UpdatePurchaseRequisitionRequest true "更新采购申请请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "采购申请不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /purchase/requisitions/{id} [put]
func (c *ProcurementController) UpdatePurchaseRequisition(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的采购申请ID",
		})
		return
	}

	var req dto.UpdatePurchaseRequisitionRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	requisition, err := c.procurementService.UpdatePurchaseRequisition(id, &req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to update purchase requisition:", err)
		if err.Error() == "采购申请不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "更新采购申请失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    requisition,
	})
}

// SubmitPurchaseRequisition 提交采购申请
// @Summary 提交采购申请
// @Description 提交采购申请进入审批流程
// @Tags 采购管理
// @Accept json
// @Produce json
// @Param id path string true "采购申请ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "采购申请不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /purchase/requisitions/{id}/submit [post]
func (c *ProcurementController) SubmitPurchaseRequisition(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的采购申请ID",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	if err := c.procurementService.SubmitPurchaseRequisition(id, userID.(uuid.UUID)); err != nil {
		logger.Error("Failed to submit purchase requisition:", err)
		if err.Error() == "采购申请不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "提交采购申请失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "提交成功",
	})
}
