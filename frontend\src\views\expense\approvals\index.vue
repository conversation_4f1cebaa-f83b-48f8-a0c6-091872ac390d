<template>
  <div class="approvals-page">
    <a-card :bordered="false">
      <template #title>
        <span>审批工作台</span>
      </template>

      <template #extra>
        <a-space>
          <a-badge :count="todoCount" :offset="[10, 0]">
            <a-button @click="handleRefresh">
              <template #icon>
                <ReloadOutlined />
              </template>
              刷新
            </a-button>
          </a-badge>
        </a-space>
      </template>

      <!-- 统计卡片 -->
      <a-row :gutter="16" class="stats-row">
        <a-col :span="6">
          <a-card size="small">
            <a-statistic title="待办任务" :value="todoCount" :value-style="{ color: '#1890ff' }"
              :prefix="h(ClockCircleOutlined)" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic title="今日已办" :value="todayApprovedCount" :value-style="{ color: '#52c41a' }"
              :prefix="h(CheckCircleOutlined)" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic title="本周已办" :value="weekApprovedCount" :value-style="{ color: '#722ed1' }"
              :prefix="h(BarChartOutlined)" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic title="本月已办" :value="monthApprovedCount" :value-style="{ color: '#fa8c16' }"
              :prefix="h(LineChartOutlined)" />
          </a-card>
        </a-col>
      </a-row>

      <!-- 待办任务列表 -->
      <a-table :columns="columns" :data-source="taskList" :loading="loading" :pagination="false" row-key="id"
        size="middle" class="approval-table">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'business_type'">
            <a-tag :color="getBusinessTypeColor(record.business_type)">
              {{ getBusinessTypeText(record.business_type) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'amount'">
            <span class="amount-text" v-if="record.amount">
              {{ formatAmount(record.amount) }}
            </span>
            <span v-else>-</span>
          </template>

          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="primary" size="small" @click="handleApprove(record)">
                审批
              </a-button>
              <a-button size="small" @click="handleView(record)">
                查看
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 审批弹窗 -->
    <a-modal v-model:open="approvalModalVisible" title="审批处理" :confirm-loading="approvalLoading"
      @ok="handleApprovalSubmit" @cancel="approvalModalVisible = false">
      <div v-if="currentTask">
        <a-descriptions :column="1" bordered class="task-info">
          <a-descriptions-item label="业务类型">
            <a-tag :color="getBusinessTypeColor(currentTask.business_type)">
              {{ getBusinessTypeText(currentTask.business_type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="申请标题">
            {{ currentTask.business_title }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人">
            {{ currentTask.applicant_name }}
          </a-descriptions-item>
          <a-descriptions-item label="所属部门">
            {{ currentTask.department_name }}
          </a-descriptions-item>
          <a-descriptions-item label="申请金额" v-if="currentTask.amount">
            <span class="amount-text">{{ formatAmount(currentTask.amount) }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="当前节点">
            {{ currentTask.current_node }}
          </a-descriptions-item>
          <a-descriptions-item label="申请时间">
            {{ currentTask.created_at }}
          </a-descriptions-item>
        </a-descriptions>

        <a-divider />

        <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
          <a-form-item label="审批操作" required>
            <a-radio-group v-model:value="approvalForm.action">
              <a-radio value="approve">同意</a-radio>
              <a-radio value="reject">驳回</a-radio>
              <a-radio value="transfer">转办</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="转办人员" v-if="approvalForm.action === 'transfer'">
            <a-select v-model:value="approvalForm.transfer_to" placeholder="请选择转办人员" :options="userOptions" show-search
              :filter-option="filterUserOption" />
          </a-form-item>

          <a-form-item label="审批意见">
            <a-textarea v-model:value="approvalForm.comment" placeholder="请输入审批意见" :rows="4" />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 查看详情弹窗 -->
    <a-modal v-model:open="viewModalVisible" title="申请详情" :footer="null" width="800px">
      <div v-if="currentTask">
        <!-- 这里可以根据业务类型动态加载不同的详情组件 -->
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="业务类型">
            <a-tag :color="getBusinessTypeColor(currentTask.business_type)">
              {{ getBusinessTypeText(currentTask.business_type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="申请状态">
            <a-tag :color="getStatusColor(currentTask.status)">
              {{ getStatusText(currentTask.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="申请标题" :span="2">
            {{ currentTask.business_title }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人">
            {{ currentTask.applicant_name }}
          </a-descriptions-item>
          <a-descriptions-item label="所属部门">
            {{ currentTask.department_name }}
          </a-descriptions-item>
          <a-descriptions-item label="申请金额" v-if="currentTask.amount">
            <span class="amount-text">{{ formatAmount(currentTask.amount) }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="当前节点">
            {{ currentTask.current_node }}
          </a-descriptions-item>
          <a-descriptions-item label="申请时间" :span="2">
            {{ currentTask.created_at }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, h } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  BarChartOutlined,
  LineChartOutlined
} from '@ant-design/icons-vue'
import {
  getTodoTasks,
  approveTask,
  rejectTask,
  transferTask,
  type ApprovalTask,
  type ApprovalAction
} from '@/api/expense'
import { getUsers, type User } from '@/api/system'

const loading = ref(false)
const approvalModalVisible = ref(false)
const viewModalVisible = ref(false)
const approvalLoading = ref(false)

const taskList = ref<ApprovalTask[]>([])
const userList = ref<User[]>([])
const currentTask = ref<ApprovalTask | null>(null)

// 统计数据
const todoCount = ref(0)
const todayApprovedCount = ref(0)
const weekApprovedCount = ref(0)
const monthApprovedCount = ref(0)

const approvalForm = reactive<ApprovalAction>({
  action: 'approve',
  comment: '',
  transfer_to: undefined
})

// 表格列配置
const columns = [
  {
    title: '业务类型',
    key: 'business_type',
    width: 120
  },
  {
    title: '申请标题',
    dataIndex: 'business_title',
    key: 'business_title',
    width: 200,
    ellipsis: true
  },
  {
    title: '申请人',
    dataIndex: 'applicant_name',
    key: 'applicant_name',
    width: 100
  },
  {
    title: '所属部门',
    dataIndex: 'department_name',
    key: 'department_name',
    width: 150
  },
  {
    title: '申请金额',
    key: 'amount',
    width: 120,
    align: 'right'
  },
  {
    title: '当前节点',
    dataIndex: 'current_node',
    key: 'current_node',
    width: 120
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '申请时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right'
  }
]

// 用户选项
const userOptions = computed(() => {
  return userList.value.map(user => ({
    label: `${user.name} (${user.department_name})`,
    value: user.id
  }))
})

// 获取业务类型颜色
const getBusinessTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'pre_application': 'blue',
    'expense_application': 'green',
    'procurement': 'orange',
    'contract': 'purple'
  }
  return colorMap[type] || 'default'
}

// 获取业务类型文本
const getBusinessTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    'pre_application': '事前申请',
    'expense_application': '费用报销',
    'procurement': '采购申请',
    'contract': '合同审批'
  }
  return textMap[type] || type
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'pending': 'orange',
    'approved': 'green',
    'rejected': 'red'
  }
  return colorMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'pending': '待审批',
    'approved': '已同意',
    'rejected': '已驳回'
  }
  return textMap[status] || status
}

// 格式化金额
const formatAmount = (amount: number) => {
  return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`
}

// 用户筛选
const filterUserOption = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase())
}

// 加载待办任务
const loadTodoTasks = async () => {
  loading.value = true
  try {
    const tasks = await getTodoTasks()
    // 确保 tasks 是数组，如果不是则使用空数组
    const taskArray = Array.isArray(tasks) ? tasks : []
    taskList.value = taskArray
    todoCount.value = taskArray.filter(task => task.status === 'PENDING').length
  } catch (error) {
    console.error('加载待办任务失败:', error)
    message.error('加载待办任务失败')
    // 出错时设置为空数组，避免后续操作出错
    taskList.value = []
    todoCount.value = 0
  } finally {
    loading.value = false
  }
}

// 加载用户列表
const loadUsers = async () => {
  try {
    const response = await getUsers({ page: 1, page_size: 100 })
    userList.value = response.list || []
  } catch (error) {
    console.error('加载用户列表失败:', error)
    message.error('加载用户列表失败')
    userList.value = []
  }
}

// 刷新数据
const handleRefresh = () => {
  loadTodoTasks()
}

// 审批操作
const handleApprove = (task: ApprovalTask) => {
  currentTask.value = task
  Object.assign(approvalForm, {
    action: 'approve',
    comment: '',
    transfer_to: undefined
  })
  approvalModalVisible.value = true
}

// 查看详情
const handleView = (task: ApprovalTask) => {
  currentTask.value = task
  viewModalVisible.value = true
}

// 提交审批
const handleApprovalSubmit = async () => {
  if (!currentTask.value) return

  if (!approvalForm.comment && approvalForm.action !== 'approve') {
    message.error('请输入审批意见')
    return
  }

  if (approvalForm.action === 'transfer' && !approvalForm.transfer_to) {
    message.error('请选择转办人员')
    return
  }

  approvalLoading.value = true
  try {
    const taskId = currentTask.value.id

    switch (approvalForm.action) {
      case 'approve':
        await approveTask(taskId, approvalForm)
        message.success('审批通过')
        break
      case 'reject':
        await rejectTask(taskId, approvalForm)
        message.success('审批驳回')
        break
      case 'transfer':
        await transferTask(taskId, approvalForm)
        message.success('转办成功')
        break
    }

    approvalModalVisible.value = false
    loadTodoTasks()
  } catch (error) {
    message.error('审批操作失败')
  } finally {
    approvalLoading.value = false
  }
}

// 加载审批统计数据
const loadApprovalStatistics = async () => {
  try {
    // 暂时使用待办任务数据来计算统计信息
    // 实际项目中应该有专门的统计接口
    todayApprovedCount.value = 0  // 今日已办数量
    weekApprovedCount.value = 0   // 本周已办数量
    monthApprovedCount.value = 0  // 本月已办数量
  } catch (error) {
    console.error('加载审批统计数据失败:', error)
  }
}

onMounted(() => {
  loadTodoTasks()
  loadUsers()
  loadApprovalStatistics()
})
</script>

<style scoped>
.approvals-page {
  padding: 24px;
}

.stats-row {
  margin-bottom: 24px;
}

.approval-table {
  margin-top: 16px;
}

.amount-text {
  font-weight: 500;
  color: #1890ff;
}

.task-info {
  margin-bottom: 16px;
}
</style>
