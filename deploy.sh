#!/bin/bash

# 医院管理系统部署脚本

set -e

echo "开始部署医院管理系统..."

# 配置变量
APP_NAME="hospital-management"
APP_DIR="/opt/$APP_NAME"
FRONTEND_DIR="/var/www/$APP_NAME/frontend"
NGINX_CONF="/etc/nginx/sites-available/$APP_NAME"
NGINX_ENABLED="/etc/nginx/sites-enabled/$APP_NAME"
SERVICE_FILE="/etc/systemd/system/$APP_NAME.service"

# 创建应用目录
echo "创建应用目录..."
sudo mkdir -p $APP_DIR
sudo mkdir -p $FRONTEND_DIR

# 复制后端文件
echo "部署后端..."
sudo cp backend/hospital-management-linux $APP_DIR/hospital-management
sudo cp backend/migrate-linux $APP_DIR/migrate
sudo chmod +x $APP_DIR/hospital-management
sudo chmod +x $APP_DIR/migrate

# 复制前端文件
echo "部署前端..."
sudo cp -r frontend/dist/* $FRONTEND_DIR/

# 创建配置文件
echo "创建配置文件..."
sudo tee $APP_DIR/config.yaml > /dev/null <<EOF
server:
  port: 8080
  host: "0.0.0.0"

database:
  host: "localhost"
  port: 5432
  user: "hospital_user"
  password: "your_password"
  dbname: "hospital_management"
  sslmode: "disable"

jwt:
  secret: "your-jwt-secret-key-change-this-in-production"
  expire_hours: 24

cors:
  allowed_origins:
    - "http://localhost:3000"
    - "http://your-domain.com"
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allowed_headers:
    - "Content-Type"
    - "Authorization"
    - "X-Requested-With"

upload:
  max_size: 104857600  # 100MB
  allowed_types:
    - "image/jpeg"
    - "image/png"
    - "image/gif"
    - "application/pdf"
    - "application/msword"
    - "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
  upload_dir: "/opt/$APP_NAME/uploads"

log:
  level: "info"
  file: "/var/log/$APP_NAME.log"
EOF

# 创建上传目录
sudo mkdir -p /opt/$APP_NAME/uploads
sudo chown -R www-data:www-data /opt/$APP_NAME/uploads

# 创建 systemd 服务
echo "创建系统服务..."
sudo tee $SERVICE_FILE > /dev/null <<EOF
[Unit]
Description=Hospital Management System
After=network.target postgresql.service

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=$APP_DIR
ExecStart=$APP_DIR/hospital-management
Restart=always
RestartSec=5
Environment=CONFIG_FILE=$APP_DIR/config.yaml

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$APP_DIR /var/log

[Install]
WantedBy=multi-user.target
EOF

# 配置 Nginx
echo "配置 Nginx..."
sudo cp nginx.conf $NGINX_CONF
sudo ln -sf $NGINX_CONF $NGINX_ENABLED

# 测试 Nginx 配置
sudo nginx -t

# 重新加载服务
echo "重新加载服务..."
sudo systemctl daemon-reload
sudo systemctl enable $APP_NAME
sudo systemctl reload nginx

echo "部署完成！"
echo ""
echo "下一步操作："
echo "1. 配置数据库连接: sudo nano $APP_DIR/config.yaml"
echo "2. 运行数据库迁移: sudo -u www-data $APP_DIR/migrate -action=migrate"
echo "3. 启动服务: sudo systemctl start $APP_NAME"
echo "4. 检查服务状态: sudo systemctl status $APP_NAME"
echo "5. 查看日志: sudo journalctl -u $APP_NAME -f"
