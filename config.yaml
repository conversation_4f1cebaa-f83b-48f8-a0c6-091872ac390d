server:
  port: 8080
  host: "0.0.0.0"

database:
  host: "*************"  # 修改为您的数据库主机
  port: 5432
  user: "zhikong"   # 修改为您的数据库用户名
  password: "196717myh"  # 修改为您的数据库密码
  dbname: "postgres"  # 修改为您的数据库名
  sslmode: "disable"

jwt:
  secret: "your-jwt-secret-change-this-in-production"
  expire_hours: 24

cors:
  allowed_origins:
    - "*"
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allowed_headers:
    - "Content-Type"
    - "Authorization"
    - "X-Requested-With"

upload:
  max_size: 104857600  # 100MB
  upload_dir: "/opt/hospital/uploads"

log:
  level: "info"
  file: "/var/log/hospital.log"
