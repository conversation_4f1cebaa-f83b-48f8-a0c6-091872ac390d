package services

import (
	"errors"
	"fmt"
	"hospital-management/internal/dto"
	"hospital-management/internal/models"
	"hospital-management/pkg/logger"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type ProcurementService struct {
	db *gorm.DB
}

func NewProcurementService(db *gorm.DB) *ProcurementService {
	return &ProcurementService{db: db}
}

// ===== 供应商管理 =====

// GetSuppliers 获取供应商列表
func (s *ProcurementService) GetSuppliers(req *dto.SupplierListRequest) ([]dto.SupplierResponse, int64, error) {
	var suppliers []models.Supplier
	var total int64

	query := s.db.Model(&models.Supplier{})

	// 搜索条件
	if req.Keyword != "" {
		query = query.Where("name ILIKE ? OR credit_code ILIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count suppliers:", err)
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&suppliers).Error; err != nil {
		logger.Error("Failed to get suppliers:", err)
		return nil, 0, err
	}

	// 转换响应
	responses := make([]dto.SupplierResponse, len(suppliers))
	for i, supplier := range suppliers {
		responses[i] = s.convertSupplierToResponse(supplier)
	}

	return responses, total, nil
}

// GetSupplier 获取供应商详情
func (s *ProcurementService) GetSupplier(id uuid.UUID) (*dto.SupplierResponse, error) {
	var supplier models.Supplier
	if err := s.db.First(&supplier, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("供应商不存在")
		}
		return nil, err
	}

	response := s.convertSupplierToResponse(supplier)
	return &response, nil
}

// CreateSupplier 创建供应商
func (s *ProcurementService) CreateSupplier(req *dto.CreateSupplierRequest, userID uuid.UUID) (*dto.SupplierResponse, error) {
	// 检查供应商名称是否重复
	var count int64
	if err := s.db.Model(&models.Supplier{}).Where("name = ?", req.Name).Count(&count).Error; err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.New("供应商名称已存在")
	}

	// 检查信用代码是否重复
	if req.CreditCode != nil && *req.CreditCode != "" {
		if err := s.db.Model(&models.Supplier{}).Where("credit_code = ?", *req.CreditCode).Count(&count).Error; err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, errors.New("统一社会信用代码已存在")
		}
	}

	supplier := models.Supplier{
		Name:        req.Name,
		CreditCode:  req.CreditCode,
		Status:      req.Status,
		ContactInfo: (*models.JSONMap)(&req.ContactInfo),
	}
	supplier.CreatedBy = &userID

	if err := s.db.Create(&supplier).Error; err != nil {
		logger.Error("Failed to create supplier:", err)
		return nil, err
	}

	response := s.convertSupplierToResponse(supplier)
	return &response, nil
}

// UpdateSupplier 更新供应商
func (s *ProcurementService) UpdateSupplier(id uuid.UUID, req *dto.UpdateSupplierRequest, userID uuid.UUID) (*dto.SupplierResponse, error) {
	var supplier models.Supplier
	if err := s.db.First(&supplier, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("供应商不存在")
		}
		return nil, err
	}

	// 检查供应商名称是否重复
	if req.Name != nil {
		var count int64
		if err := s.db.Model(&models.Supplier{}).Where("name = ? AND id != ?", *req.Name, id).Count(&count).Error; err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, errors.New("供应商名称已存在")
		}
		supplier.Name = *req.Name
	}

	// 检查信用代码是否重复
	if req.CreditCode != nil {
		if *req.CreditCode != "" {
			var count int64
			if err := s.db.Model(&models.Supplier{}).Where("credit_code = ? AND id != ?", *req.CreditCode, id).Count(&count).Error; err != nil {
				return nil, err
			}
			if count > 0 {
				return nil, errors.New("统一社会信用代码已存在")
			}
		}
		supplier.CreditCode = req.CreditCode
	}

	if req.ContactInfo != nil {
		supplier.ContactInfo = (*models.JSONMap)(&req.ContactInfo)
	}
	supplier.UpdatedBy = &userID

	if err := s.db.Save(&supplier).Error; err != nil {
		logger.Error("Failed to update supplier:", err)
		return nil, err
	}

	response := s.convertSupplierToResponse(supplier)
	return &response, nil
}

// UpdateSupplierStatus 更新供应商状态
func (s *ProcurementService) UpdateSupplierStatus(id uuid.UUID, req *dto.UpdateSupplierStatusRequest, userID uuid.UUID) (*dto.SupplierResponse, error) {
	var supplier models.Supplier
	if err := s.db.First(&supplier, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("供应商不存在")
		}
		return nil, err
	}

	supplier.Status = req.Status
	supplier.UpdatedBy = &userID

	if err := s.db.Save(&supplier).Error; err != nil {
		logger.Error("Failed to update supplier status:", err)
		return nil, err
	}

	response := s.convertSupplierToResponse(supplier)
	return &response, nil
}

// ===== 采购申请管理 =====

// GetPurchaseRequisitions 获取采购申请列表
func (s *ProcurementService) GetPurchaseRequisitions(req *dto.PurchaseRequisitionListRequest) ([]dto.PurchaseRequisitionResponse, int64, error) {
	var requisitions []models.PurchaseRequisition
	var total int64

	query := s.db.Model(&models.PurchaseRequisition{}).
		Preload("Applicant").
		Preload("Department")

	// 搜索条件
	if req.Keyword != "" {
		query = query.Where("title ILIKE ? OR requisition_code ILIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.ApplicantID != nil {
		query = query.Where("applicant_id = ?", *req.ApplicantID)
	}
	if req.DepartmentID != nil {
		query = query.Where("department_id = ?", *req.DepartmentID)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count purchase requisitions:", err)
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&requisitions).Error; err != nil {
		logger.Error("Failed to get purchase requisitions:", err)
		return nil, 0, err
	}

	// 转换响应
	responses := make([]dto.PurchaseRequisitionResponse, len(requisitions))
	for i, requisition := range requisitions {
		responses[i] = s.convertRequisitionToResponse(requisition)
	}

	return responses, total, nil
}

// GetPurchaseRequisition 获取采购申请详情
func (s *ProcurementService) GetPurchaseRequisition(id uuid.UUID) (*dto.PurchaseRequisitionResponse, error) {
	var requisition models.PurchaseRequisition
	if err := s.db.Preload("Applicant").Preload("Department").First(&requisition, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("采购申请不存在")
		}
		return nil, err
	}

	response := s.convertRequisitionToResponse(requisition)
	return &response, nil
}

// CreatePurchaseRequisition 创建采购申请
func (s *ProcurementService) CreatePurchaseRequisition(req *dto.CreatePurchaseRequisitionRequest, userID uuid.UUID) (*dto.PurchaseRequisitionResponse, error) {
	// 获取用户信息以获取部门ID
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return nil, errors.New("用户不存在")
	}

	// 计算总金额
	totalAmount := decimal.Zero
	for _, item := range req.Items {
		if item.EstimatedAmount != nil {
			totalAmount = totalAmount.Add(*item.EstimatedAmount)
		} else if item.EstimatedPrice != nil {
			totalAmount = totalAmount.Add(item.EstimatedPrice.Mul(item.Quantity))
		}
	}

	// 生成申请单号
	requisitionCode, err := s.generateRequisitionCode()
	if err != nil {
		return nil, err
	}

	// 构建明细数据
	details := map[string]interface{}{
		"items": req.Items,
	}

	requisition := models.PurchaseRequisition{
		RequisitionCode: requisitionCode,
		ApplicantID:     userID,
		DepartmentID:    user.DepartmentID,
		Title:           req.Title,
		Details:         (*models.JSONMap)(&details),
		TotalAmount:     &totalAmount,
		Status:          "DRAFT",
	}
	requisition.CreatedBy = &userID

	if err := s.db.Create(&requisition).Error; err != nil {
		logger.Error("Failed to create purchase requisition:", err)
		return nil, err
	}

	// 预加载关联数据
	if err := s.db.Preload("Applicant").Preload("Department").First(&requisition, requisition.ID).Error; err != nil {
		logger.Error("Failed to preload purchase requisition data:", err)
		return nil, err
	}

	response := s.convertRequisitionToResponse(requisition)
	return &response, nil
}

// UpdatePurchaseRequisition 更新采购申请
func (s *ProcurementService) UpdatePurchaseRequisition(id uuid.UUID, req *dto.UpdatePurchaseRequisitionRequest, userID uuid.UUID) (*dto.PurchaseRequisitionResponse, error) {
	var requisition models.PurchaseRequisition
	if err := s.db.First(&requisition, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("采购申请不存在")
		}
		return nil, err
	}

	// 检查是否可以修改
	if requisition.Status != "DRAFT" {
		return nil, errors.New("只能修改草稿状态的申请")
	}

	// 检查权限
	if requisition.ApplicantID != userID {
		return nil, errors.New("只能修改自己的申请")
	}

	// 更新字段
	if req.Title != nil {
		requisition.Title = *req.Title
	}

	if req.Items != nil {
		// 计算新的总金额
		totalAmount := decimal.Zero
		for _, item := range req.Items {
			if item.EstimatedAmount != nil {
				totalAmount = totalAmount.Add(*item.EstimatedAmount)
			} else if item.EstimatedPrice != nil {
				totalAmount = totalAmount.Add(item.EstimatedPrice.Mul(item.Quantity))
			}
		}

		// 构建明细数据
		details := map[string]interface{}{
			"items": req.Items,
		}

		requisition.Details = (*models.JSONMap)(&details)
		requisition.TotalAmount = &totalAmount
	}

	requisition.UpdatedBy = &userID

	if err := s.db.Save(&requisition).Error; err != nil {
		logger.Error("Failed to update purchase requisition:", err)
		return nil, err
	}

	// 预加载关联数据
	if err := s.db.Preload("Applicant").Preload("Department").First(&requisition, requisition.ID).Error; err != nil {
		logger.Error("Failed to preload purchase requisition data:", err)
		return nil, err
	}

	response := s.convertRequisitionToResponse(requisition)
	return &response, nil
}

// SubmitPurchaseRequisition 提交采购申请
func (s *ProcurementService) SubmitPurchaseRequisition(id uuid.UUID, userID uuid.UUID) error {
	var requisition models.PurchaseRequisition
	if err := s.db.First(&requisition, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("采购申请不存在")
		}
		return err
	}

	// 检查状态
	if requisition.Status != "DRAFT" {
		return errors.New("只能提交草稿状态的申请")
	}

	// 检查权限
	if requisition.ApplicantID != userID {
		return errors.New("只能提交自己的申请")
	}

	// 更新状态
	requisition.Status = "PENDING"
	requisition.UpdatedBy = &userID

	if err := s.db.Save(&requisition).Error; err != nil {
		logger.Error("Failed to submit purchase requisition:", err)
		return err
	}

	// TODO: 这里应该启动审批流程
	logger.Info("Purchase requisition submitted:", requisition.ID)

	return nil
}

// ===== 辅助方法 =====

// generateRequisitionCode 生成采购申请单号
func (s *ProcurementService) generateRequisitionCode() (string, error) {
	now := time.Now()
	prefix := fmt.Sprintf("CG%04d%02d%02d", now.Year(), now.Month(), now.Day())

	var count int64
	if err := s.db.Model(&models.PurchaseRequisition{}).
		Where("requisition_code LIKE ?", prefix+"%").
		Count(&count).Error; err != nil {
		return "", err
	}

	return fmt.Sprintf("%s%04d", prefix, count+1), nil
}

// convertSupplierToResponse 转换供应商为响应格式
func (s *ProcurementService) convertSupplierToResponse(supplier models.Supplier) dto.SupplierResponse {
	response := dto.SupplierResponse{
		ID:         supplier.ID,
		Name:       supplier.Name,
		CreditCode: supplier.CreditCode,
		Status:     supplier.Status,
		CreatedAt:  supplier.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:  supplier.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	if supplier.ContactInfo != nil {
		response.ContactInfo = map[string]interface{}(*supplier.ContactInfo)
	}

	return response
}

// convertRequisitionToResponse 转换采购申请为响应格式
func (s *ProcurementService) convertRequisitionToResponse(requisition models.PurchaseRequisition) dto.PurchaseRequisitionResponse {
	response := dto.PurchaseRequisitionResponse{
		ID:              requisition.ID,
		RequisitionCode: requisition.RequisitionCode,
		ApplicantID:     requisition.ApplicantID,
		DepartmentID:    requisition.DepartmentID,
		Title:           requisition.Title,
		TotalAmount:     requisition.TotalAmount,
		Status:          requisition.Status,
		CreatedAt:       requisition.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:       requisition.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	// 关联数据
	if requisition.Applicant != nil {
		response.Applicant = &dto.UserSimpleResponse{
			ID:       requisition.Applicant.ID,
			UserName: requisition.Applicant.UserName,
			JobTitle: requisition.Applicant.JobTitle,
		}
	}

	if requisition.Department != nil {
		response.Department = &dto.DepartmentSimpleResponse{
			ID:       requisition.Department.ID,
			DeptName: requisition.Department.Name,
			DeptCode: requisition.Department.Code,
		}
	}

	// 解析明细数据
	if requisition.Details != nil {
		details := map[string]interface{}(*requisition.Details)
		if items, ok := details["items"].([]interface{}); ok {
			itemResponses := make([]dto.PurchaseItemResponse, 0, len(items))
			for _, item := range items {
				if itemMap, ok := item.(map[string]interface{}); ok {
					itemResponse := dto.PurchaseItemResponse{}

					if name, ok := itemMap["item_name"].(string); ok {
						itemResponse.ItemName = name
					}
					if spec, ok := itemMap["specification"].(string); ok {
						itemResponse.Specification = &spec
					}
					if unit, ok := itemMap["unit"].(string); ok {
						itemResponse.Unit = unit
					}
					if qty, ok := itemMap["quantity"].(float64); ok {
						itemResponse.Quantity = decimal.NewFromFloat(qty)
					}
					if price, ok := itemMap["estimated_price"].(float64); ok {
						priceDecimal := decimal.NewFromFloat(price)
						itemResponse.EstimatedPrice = &priceDecimal
					}
					if amount, ok := itemMap["estimated_amount"].(float64); ok {
						amountDecimal := decimal.NewFromFloat(amount)
						itemResponse.EstimatedAmount = &amountDecimal
					}
					if remark, ok := itemMap["remark"].(string); ok {
						itemResponse.Remark = &remark
					}

					itemResponses = append(itemResponses, itemResponse)
				}
			}
			response.Items = itemResponses
		}
	}

	return response
}
