package models

import (
	"github.com/google/uuid"
)

// File 文件管理表 - 对应 tbl_files
type File struct {
	BaseModel
	FileName      string     `json:"file_name" gorm:"type:varchar(255);not null" validate:"required,max=255"`
	OriginalName  string     `json:"original_name" gorm:"type:varchar(255);not null" validate:"required,max=255"` // 原始文件名
	FilePath      string     `json:"file_path" gorm:"type:varchar(500);not null" validate:"required,max=500"`
	FileSize      *int64     `json:"file_size,omitempty"`
	FileType      *string    `json:"file_type,omitempty" gorm:"type:varchar(50)"` // 文件类型
	MimeType      *string    `json:"mime_type,omitempty" gorm:"type:varchar(100)"`
	StorageType   string     `json:"storage_type" gorm:"type:varchar(20);not null;default:'LOCAL'" validate:"required"`
	BusinessID    *uuid.UUID `json:"business_id,omitempty" gorm:"type:uuid"`
	BusinessType  *string    `json:"business_type,omitempty" gorm:"type:varchar(50)"`
	Description   *string    `json:"description,omitempty" gorm:"type:text"`    // 文件描述
	IsPublic      *bool      `json:"is_public,omitempty" gorm:"default:false"`  // 是否公开
	DownloadCount *int64     `json:"download_count,omitempty" gorm:"default:0"` // 下载次数

	// 关联关系
	Uploader *User `json:"uploader,omitempty" gorm:"foreignKey:CreatedBy"` // 上传者
}

// TableName 指定表名
func (File) TableName() string {
	return "tbl_files"
}
