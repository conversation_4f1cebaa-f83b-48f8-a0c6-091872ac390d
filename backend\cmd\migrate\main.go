package main

import (
	"flag"
	"fmt"
	"log"
	"os"

	"hospital-management/internal/config"
	"hospital-management/internal/database"

	"github.com/joho/godotenv"
	"gorm.io/gorm"
)

func main() {
	// 解析命令行参数
	var (
		action = flag.String("action", "migrate", "操作类型: migrate(迁移), reset(重置)")
		env    = flag.String("env", ".env", "环境配置文件路径")
	)
	flag.Parse()

	// 加载环境变量
	if err := godotenv.Load(*env); err != nil {
		log.Printf("警告: 无法加载环境配置文件 %s: %v", *env, err)
	}

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 连接数据库
	db, err := database.Init(cfg)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	// 获取底层数据库连接
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("获取数据库连接失败: %v", err)
	}
	defer sqlDB.Close()

	// 创建迁移管理器
	migrationManager := database.NewMigrationManager(db)

	// 根据操作类型执行相应操作
	switch *action {
	case "migrate":
		fmt.Println("=== 开始数据库迁移 ===")
		if err := migrationManager.RunMigrations(); err != nil {
			log.Fatalf("数据库迁移失败: %v", err)
		}
		fmt.Println("=== 数据库迁移完成 ===")

	case "reset":
		fmt.Println("=== 开始重置数据库 ===")
		if err := resetDatabase(db); err != nil {
			log.Fatalf("重置数据库失败: %v", err)
		}
		fmt.Println("=== 数据库重置完成 ===")

		fmt.Println("=== 开始重新迁移 ===")
		if err := migrationManager.RunMigrations(); err != nil {
			log.Fatalf("重新迁移失败: %v", err)
		}
		fmt.Println("=== 重新迁移完成 ===")

	default:
		fmt.Printf("未知操作: %s\n", *action)
		fmt.Println("支持的操作:")
		fmt.Println("  migrate - 执行数据库迁移")
		fmt.Println("  reset   - 重置数据库并重新迁移")
		os.Exit(1)
	}
}

// resetDatabase 重置数据库（删除所有表）
func resetDatabase(db *gorm.DB) error {
	log.Println("正在删除所有表...")

	// 按依赖关系逆序删除表
	tables := []string{
		// 资产相关表
		"tbl_asset_change_records",
		"tbl_asset_maintenance_records",
		"tbl_asset_depreciation_records",
		"tbl_assets",
		"tbl_asset_categories",

		// 合同相关表
		"tbl_contract_payment_schedules",
		"tbl_contracts",

		// 采购相关表
		"tbl_purchase_requisitions",
		"tbl_suppliers",

		// 支出控制相关表
		"tbl_payments",
		"tbl_expense_details",
		"tbl_expense_applications",
		"tbl_pre_applications",

		// 预算相关表
		"tbl_budget_items",
		"tbl_budget_subjects",
		"tbl_budget_schemes",

		// 审批流相关表
		"tbl_approval_nodes",
		"tbl_approval_flows",

		// 文件表
		"tbl_files",

		// 用户角色相关表
		"tbl_user_roles",
		"tbl_roles",
		"tbl_users",
		"tbl_departments",
	}

	for _, table := range tables {
		if err := db.Exec(fmt.Sprintf("DROP TABLE IF EXISTS %s CASCADE", table)).Error; err != nil {
			log.Printf("⚠ 删除表 %s 失败: %v", table, err)
		} else {
			log.Printf("✓ 已删除表: %s", table)
		}
	}

	return nil
}
