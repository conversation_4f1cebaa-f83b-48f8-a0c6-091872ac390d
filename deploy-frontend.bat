@echo off
echo 正在部署前端文件到服务器...

echo 1. 打包前端项目...
cd frontend
call npm run build
cd ..

echo 2. 创建部署包...
if exist frontend-deploy.zip del frontend-deploy.zip
powershell -Command "Compress-Archive -Path 'frontend\dist\*' -DestinationPath 'frontend-deploy.zip' -Force"

echo 前端打包完成！
echo 请将 frontend-deploy.zip 上传到服务器并解压到 /var/www/hospital/dist/ 目录
echo.
echo 服务器操作命令：
echo cd /var/www/hospital/
echo rm -rf dist/*
echo unzip frontend-deploy.zip -d dist/
echo systemctl restart nginx
echo.
pause
