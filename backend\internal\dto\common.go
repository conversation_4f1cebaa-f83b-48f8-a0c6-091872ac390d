package dto

import (
	"github.com/google/uuid"
)

// ===== 通用响应结构 =====

// UserSimpleResponse 用户简单响应（用于关联显示）
type UserSimpleResponse struct {
	ID       uuid.UUID `json:"id"`
	UserName string    `json:"user_name"`
	JobTitle *string   `json:"job_title,omitempty"`
}

// DepartmentSimpleResponse 部门简单响应（用于关联显示）
type DepartmentSimpleResponse struct {
	ID       uuid.UUID `json:"id"`
	DeptName string    `json:"dept_name"`
	DeptCode *string   `json:"dept_code,omitempty"`
}

// RoleSimpleResponse 角色简单响应（用于关联显示）
type RoleSimpleResponse struct {
	ID       uuid.UUID `json:"id"`
	RoleName string    `json:"role_name"`
}

// ===== 通用请求结构 =====

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" validate:"required"`
	NewPassword string `json:"new_password" validate:"required,min=6,max=50"`
}

// ===== 通用分页结构 =====

// PaginationRequest 分页请求
type PaginationRequest struct {
	Page     int `form:"page" validate:"min=1"`
	PageSize int `form:"page_size" validate:"min=1,max=100"`
}

// PaginationResponse 分页响应
type PaginationResponse struct {
	Total       int64 `json:"total"`
	Page        int   `json:"page"`
	PageSize    int   `json:"page_size"`
	TotalPages  int   `json:"total_pages"`
	HasNext     bool  `json:"has_next"`
	HasPrevious bool  `json:"has_previous"`
}
