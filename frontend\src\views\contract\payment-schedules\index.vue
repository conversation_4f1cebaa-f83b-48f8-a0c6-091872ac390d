<template>
  <div class="payment-schedules-page">
    <a-card :bordered="false">
      <template #title>
        <span>付款计划管理</span>
      </template>

      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <PlusOutlined />
            </template>
            新增计划
          </a-button>
          <a-button @click="handleBatchAdd">
            <template #icon>
              <AppstoreAddOutlined />
            </template>
            批量生成
          </a-button>
          <a-button @click="handleRefresh">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 搜索区域 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="合同">
            <a-select v-model:value="searchForm.contract_id" placeholder="请选择合同" allow-clear style="width: 200px"
              show-search :filter-option="filterContractOption" :options="contractOptions" />
          </a-form-item>

          <a-form-item label="付款状态">
            <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear style="width: 120px">
              <a-select-option value="pending">待付款</a-select-option>
              <a-select-option value="paid">已付款</a-select-option>
              <a-select-option value="overdue">已逾期</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="到期时间">
            <a-range-picker v-model:value="dateRange" style="width: 240px" @change="handleDateChange" />
          </a-form-item>

          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon>
                  <SearchOutlined />
                </template>
                搜索
              </a-button>
              <a-button @click="handleReset">
                <template #icon>
                  <ReloadOutlined />
                </template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 付款计划列表 -->
      <a-table :columns="columns" :data-source="scheduleList" :loading="loading" :pagination="pagination" row-key="id"
        size="middle" @change="handleTableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'contract_title'">
            <a @click="handleViewContract(record)">{{ record.contract_title }}</a>
          </template>

          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'amount'">
            <span class="amount-text">{{ formatAmount(record.amount) }}</span>
          </template>

          <template v-else-if="column.key === 'actual_amount'">
            <span class="amount-text" v-if="record.actual_amount">
              {{ formatAmount(record.actual_amount) }}
            </span>
            <span v-else>-</span>
          </template>

          <template v-else-if="column.key === 'due_date'">
            <span :class="getDueDateClass(record)">{{ record.due_date }}</span>
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)">
                编辑
              </a-button>
              <a-button type="link" size="small" @click="handleMarkPaid(record)" v-if="record.status === 'pending'">
                标记已付
              </a-button>
              <a-popconfirm title="确定要删除这个付款计划吗？" @confirm="handleDelete(record)">
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑付款计划弹窗 -->
    <a-modal v-model:open="modalVisible" :title="modalTitle" :confirm-loading="modalLoading" @ok="handleSubmit"
      @cancel="handleCancel">
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="关联合同" name="contract_id">
          <a-select v-model:value="formData.contract_id" placeholder="请选择合同" show-search
            :filter-option="filterContractOption" :options="contractOptions" />
        </a-form-item>

        <a-form-item label="付款期数" name="phase">
          <a-input-number v-model:value="formData.phase" :min="1" placeholder="请输入付款期数" style="width: 100%" />
        </a-form-item>

        <a-form-item label="期数名称" name="phase_name">
          <a-input v-model:value="formData.phase_name" placeholder="请输入期数名称" />
        </a-form-item>

        <a-form-item label="付款金额" name="amount">
          <a-input-number v-model:value="formData.amount" :min="0" :precision="2" placeholder="请输入付款金额"
            style="width: 100%" :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/¥\s?|(,*)/g, '')" />
        </a-form-item>

        <a-form-item label="到期日期" name="due_date">
          <a-date-picker v-model:value="formData.due_date" placeholder="请选择到期日期" style="width: 100%" />
        </a-form-item>

        <a-form-item label="备注说明" name="remark">
          <a-textarea v-model:value="formData.remark" placeholder="请输入备注说明" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 批量生成付款计划弹窗 -->
    <a-modal v-model:open="batchModalVisible" title="批量生成付款计划" :confirm-loading="batchLoading" width="800px"
      @ok="handleBatchSubmit" @cancel="batchModalVisible = false">
      <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="关联合同">
          <a-select v-model:value="batchForm.contract_id" placeholder="请选择合同" show-search
            :filter-option="filterContractOption" :options="contractOptions" />
        </a-form-item>

        <a-form-item label="付款方式">
          <a-radio-group v-model:value="batchForm.payment_type">
            <a-radio value="equal">等额分期</a-radio>
            <a-radio value="custom">自定义</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="分期数量" v-if="batchForm.payment_type === 'equal'">
          <a-input-number v-model:value="batchForm.phase_count" :min="2" :max="12" placeholder="请输入分期数量"
            style="width: 100%" />
        </a-form-item>

        <a-form-item label="开始日期">
          <a-date-picker v-model:value="batchForm.start_date" placeholder="请选择开始日期" style="width: 100%" />
        </a-form-item>

        <a-form-item label="付款间隔">
          <a-select v-model:value="batchForm.interval_type" placeholder="请选择间隔类型">
            <a-select-option value="month">按月</a-select-option>
            <a-select-option value="quarter">按季度</a-select-option>
            <a-select-option value="year">按年</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>

      <a-divider>预览付款计划</a-divider>

      <a-table :columns="previewColumns" :data-source="previewSchedules" :pagination="false" row-key="phase"
        size="small" />
    </a-modal>

    <!-- 标记已付弹窗 -->
    <a-modal v-model:open="paidModalVisible" title="标记付款完成" :confirm-loading="paidLoading" @ok="handlePaidSubmit"
      @cancel="paidModalVisible = false">
      <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="实际付款日期">
          <a-date-picker v-model:value="paidForm.actual_pay_date" placeholder="请选择实际付款日期" style="width: 100%" />
        </a-form-item>

        <a-form-item label="实际付款金额">
          <a-input-number v-model:value="paidForm.actual_amount" :min="0" :precision="2" placeholder="请输入实际付款金额"
            style="width: 100%" :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/¥\s?|(,*)/g, '')" />
        </a-form-item>

        <a-form-item label="付款备注">
          <a-textarea v-model:value="paidForm.remark" placeholder="请输入付款备注" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  AppstoreAddOutlined,
  ReloadOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'
import dayjs, { type Dayjs } from 'dayjs'
import {
  getPaymentSchedules,
  createPaymentSchedule,
  updatePaymentSchedule,
  deletePaymentSchedule,
  markPaymentCompleted,
  batchCreatePaymentSchedules,
  getContracts,
  type PaymentSchedule,
  type PaymentScheduleForm,
  type PaymentScheduleQuery,
  type Contract
} from '@/api/contract'
import type { Rule } from 'ant-design-vue/es/form'
import type { FormInstance } from 'ant-design-vue'

const loading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const batchModalVisible = ref(false)
const batchLoading = ref(false)
const paidModalVisible = ref(false)
const paidLoading = ref(false)
const formRef = ref<FormInstance>()
const editingId = ref<number | null>(null)
const currentSchedule = ref<PaymentSchedule | null>(null)

const scheduleList = ref<PaymentSchedule[]>([])
const contractList = ref<Contract[]>([])
const previewSchedules = ref<any[]>([])
const dateRange = ref<[Dayjs, Dayjs] | null>(null)

const searchForm = reactive<PaymentScheduleQuery>({
  contract_id: undefined,
  status: undefined,
  start_date: undefined,
  end_date: undefined,
  page: 1,
  page_size: 10
})

const formData = reactive<PaymentScheduleForm & {
  due_date: Dayjs | null
}>({
  contract_id: 0,
  phase: 1,
  phase_name: '',
  amount: 0,
  due_date: null,
  remark: ''
})

const batchForm = reactive({
  contract_id: undefined,
  payment_type: 'equal',
  phase_count: 3,
  start_date: null as Dayjs | null,
  interval_type: 'month'
})

const paidForm = reactive({
  actual_pay_date: null as Dayjs | null,
  actual_amount: 0,
  remark: ''
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '关联合同',
    key: 'contract_title',
    width: 200,
    ellipsis: true
  },
  {
    title: '付款期数',
    dataIndex: 'phase',
    key: 'phase',
    width: 100
  },
  {
    title: '期数名称',
    dataIndex: 'phase_name',
    key: 'phase_name',
    width: 150
  },
  {
    title: '应付金额',
    key: 'amount',
    width: 120,
    align: 'right'
  },
  {
    title: '实付金额',
    key: 'actual_amount',
    width: 120,
    align: 'right'
  },
  {
    title: '到期日期',
    key: 'due_date',
    width: 120
  },
  {
    title: '实际付款日期',
    dataIndex: 'actual_pay_date',
    key: 'actual_pay_date',
    width: 120
  },
  {
    title: '付款状态',
    key: 'status',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right'
  }
]

// 预览表格列配置
const previewColumns = [
  {
    title: '期数',
    dataIndex: 'phase',
    key: 'phase',
    width: 80
  },
  {
    title: '期数名称',
    dataIndex: 'phase_name',
    key: 'phase_name'
  },
  {
    title: '付款金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 120,
    align: 'right',
    customRender: ({ text }: any) => formatAmount(text)
  },
  {
    title: '到期日期',
    dataIndex: 'due_date',
    key: 'due_date',
    width: 120
  }
]

// 表单验证规则
const rules: Record<string, Rule[]> = {
  contract_id: [
    { required: true, message: '请选择关联合同', trigger: 'change' }
  ],
  phase: [
    { required: true, message: '请输入付款期数', trigger: 'blur' }
  ],
  phase_name: [
    { required: true, message: '请输入期数名称', trigger: 'blur' }
  ],
  amount: [
    { required: true, message: '请输入付款金额', trigger: 'blur' }
  ],
  due_date: [
    { required: true, message: '请选择到期日期', trigger: 'change' }
  ]
}

// 弹窗标题
const modalTitle = computed(() => {
  return editingId.value ? '编辑付款计划' : '新增付款计划'
})

// 合同选项
const contractOptions = computed(() => {
  return contractList.value
    .filter(contract => ['signed', 'executing'].includes(contract.status))
    .map(contract => ({
      label: `${contract.contract_no} - ${contract.title}`,
      value: contract.id
    }))
})
</script>
