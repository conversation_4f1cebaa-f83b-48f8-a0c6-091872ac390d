package models

import (
	"github.com/google/uuid"
)

// User 员工/用户表 - 对应 tbl_users
type User struct {
	BaseModel
	DepartmentID uuid.UUID `json:"department_id" gorm:"type:uuid;not null" validate:"required"`
	UserName     string    `json:"user_name" gorm:"type:varchar(100);not null" validate:"required,max=100"`
	EmployeeID   *string   `json:"employee_id,omitempty" gorm:"type:varchar(50);unique"`
	Email        *string   `json:"email,omitempty" gorm:"type:varchar(255);unique" validate:"omitempty,email"`
	PhoneNumber  *string   `json:"phone_number,omitempty" gorm:"type:varchar(20);unique"`
	PasswordHash string    `json:"-" gorm:"type:varchar(255);not null"`
	JobTitle     *string   `json:"job_title,omitempty" gorm:"type:varchar(100)"`
	IsActive     bool      `json:"is_active" gorm:"not null;default:true"`

	// 关联关系
	Department *Department `json:"department,omitempty" gorm:"foreignKey:DepartmentID"`
	Roles      []Role      `json:"roles,omitempty" gorm:"many2many:tbl_user_roles;"`
}

// TableName 指定表名
func (User) TableName() string {
	return "tbl_users"
}
