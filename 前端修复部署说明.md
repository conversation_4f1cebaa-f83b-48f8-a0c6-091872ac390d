# 前端API地址修复部署说明

## 问题描述
前端在访问API时使用的是 `localhost:8080`，但实际后端部署在服务器 `*************:8080`，导致网络连接错误。

## 解决方案
已经修复了前端配置，将API地址更改为 `http://*************/api`，并重新打包。

## 部署步骤

### 1. 上传文件
将 `frontend-deploy.zip` 上传到服务器

### 2. 在服务器上执行以下命令

```bash
# 备份当前前端文件（可选）
cd /var/www/hospital/
cp -r dist dist_backup_$(date +%Y%m%d_%H%M%S)

# 清空当前前端文件
rm -rf dist/*

# 解压新的前端文件
unzip /path/to/frontend-deploy.zip -d dist/

# 设置正确的权限
chown -R www-data:www-data dist/
chmod -R 755 dist/

# 重启nginx
systemctl restart nginx
```

### 3. 验证部署
访问 `http://*************/hospital/` 检查是否能正常加载角色数据。

## 修改内容
- 创建了 `frontend/.env` 和 `frontend/.env.production` 文件
- 设置 `VITE_API_BASE_URL=http://*************/api`
- 重新打包前端项目

## 注意事项
- 确保后端服务正在运行（已确认运行在 0.0.0.0:8080）
- 确保nginx配置正确代理 `/api/` 到后端
- 如果还有问题，检查服务器防火墙设置
