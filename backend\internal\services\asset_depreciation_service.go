package services

import (
	"errors"
	"fmt"
	"hospital-management/internal/dto"
	"hospital-management/internal/models"
	"hospital-management/pkg/logger"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type AssetDepreciationService struct {
	db *gorm.DB
}

// 使用通用辅助函数 utils.go

func NewAssetDepreciationService(db *gorm.DB) *AssetDepreciationService {
	return &AssetDepreciationService{db: db}
}

// CreateDepreciationRecord 创建折旧记录
func (s *AssetDepreciationService) CreateDepreciationRecord(req *dto.CreateAssetDepreciationRequest, userID uuid.UUID) (*dto.AssetDepreciationResponse, error) {
	// 检查资产是否存在
	var asset models.Asset
	if err := s.db.First(&asset, req.AssetID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("资产不存在")
		}
		return nil, err
	}

	// 检查是否已存在该期间的折旧记录
	var existingRecord models.AssetDepreciationRecord
	if err := s.db.Where("asset_id = ? AND period = ?", req.AssetID, req.Period).First(&existingRecord).Error; err == nil {
		return nil, errors.New("该期间的折旧记录已存在")
	}

	// 创建折旧记录
	record := models.AssetDepreciationRecord{
		AssetID:                 req.AssetID,
		Period:                  req.Period,
		DepreciationAmount:      req.DepreciationAmount,
		AccumulatedDepreciation: req.AccumulatedDepreciation,
		BookValue:               req.BookValue,
		DepreciationMethod:      req.DepreciationMethod,
		CalculationBasis:        req.CalculationBasis,
		Remark:                  req.Remark,
	}
	record.CreatedBy = &userID

	if err := s.db.Create(&record).Error; err != nil {
		logger.Error("Failed to create depreciation record:", err)
		return nil, err
	}

	// 更新资产的累计折旧和现值
	if err := s.db.Model(&asset).Updates(map[string]interface{}{
		"accumulated_depreciation": req.AccumulatedDepreciation,
		"current_value":            req.BookValue,
	}).Error; err != nil {
		logger.Error("Failed to update asset depreciation:", err)
	}

	return s.GetDepreciationRecordByID(record.ID, userID)
}

// GetDepreciationRecords 获取折旧记录列表
func (s *AssetDepreciationService) GetDepreciationRecords(req *dto.AssetDepreciationListRequest) ([]dto.AssetDepreciationResponse, int64, error) {
	var records []models.AssetDepreciationRecord
	var total int64

	query := s.db.Model(&models.AssetDepreciationRecord{}).
		Preload("Asset").
		Preload("Creator")

	// 搜索条件
	if req.AssetID != nil {
		query = query.Where("asset_id = ?", *req.AssetID)
	}
	if req.Period != "" {
		query = query.Where("period = ?", req.Period)
	}
	if req.StartDate != "" {
		query = query.Where("created_at >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		query = query.Where("created_at <= ?", req.EndDate+" 23:59:59")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).
		Order("created_at DESC").Find(&records).Error; err != nil {
		return nil, 0, err
	}

	// 转换为响应格式
	responses := make([]dto.AssetDepreciationResponse, len(records))
	for i, record := range records {
		responses[i] = s.convertToDepreciationResponse(record)
	}

	return responses, total, nil
}

// GetDepreciationRecordByID 根据ID获取折旧记录
func (s *AssetDepreciationService) GetDepreciationRecordByID(id uuid.UUID, userID uuid.UUID) (*dto.AssetDepreciationResponse, error) {
	var record models.AssetDepreciationRecord
	if err := s.db.Preload("Asset").Preload("Creator").First(&record, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("折旧记录不存在")
		}
		return nil, err
	}

	response := s.convertToDepreciationResponse(record)
	return &response, nil
}

// BatchCalculateDepreciation 批量计算折旧
func (s *AssetDepreciationService) BatchCalculateDepreciation(req *dto.BatchDepreciationRequest, userID uuid.UUID) error {
	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, assetID := range req.AssetIDs {
		// 获取资产信息
		var asset models.Asset
		if err := tx.Preload("Category").First(&asset, assetID).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("资产 %s 不存在", assetID)
		}

		// 检查是否已存在该期间的折旧记录
		if !req.ForceRecalc {
			var existingRecord models.AssetDepreciationRecord
			if err := tx.Where("asset_id = ? AND period = ?", assetID, req.Period).First(&existingRecord).Error; err == nil {
				continue // 跳过已存在的记录
			}
		}

		// 计算折旧
		depreciationAmount, accumulatedDepreciation, bookValue, err := s.calculateDepreciation(asset, req.Period)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("计算资产 %s 折旧失败: %v", asset.AssetName, err)
		}

		// 创建或更新折旧记录
		record := models.AssetDepreciationRecord{
			AssetID:                 assetID,
			Period:                  req.Period,
			DepreciationAmount:      depreciationAmount,
			AccumulatedDepreciation: accumulatedDepreciation,
			BookValue:               bookValue,
			DepreciationMethod:      StringValue(asset.Category.DepreciationMethod),
			CalculationBasis:        nil,
		}
		record.CreatedBy = &userID

		if req.ForceRecalc {
			// 删除现有记录
			tx.Where("asset_id = ? AND period = ?", assetID, req.Period).Delete(&models.AssetDepreciationRecord{})
		}

		if err := tx.Create(&record).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("创建折旧记录失败: %v", err)
		}

		// 更新资产的累计折旧和现值
		if err := tx.Model(&asset).Updates(map[string]interface{}{
			"accumulated_depreciation": accumulatedDepreciation,
			"current_value":            bookValue,
		}).Error; err != nil {
			logger.Error("Failed to update asset depreciation:", err)
		}
	}

	return tx.Commit().Error
}

// calculateDepreciation 计算折旧
func (s *AssetDepreciationService) calculateDepreciation(asset models.Asset, period string) (decimal.Decimal, decimal.Decimal, decimal.Decimal, error) {
	if asset.OriginalValue == nil || asset.UsefulLife == nil {
		return decimal.Zero, decimal.Zero, decimal.Zero, errors.New("资产原值或使用年限未设置")
	}

	originalValue := *asset.OriginalValue
	usefulLife := *asset.UsefulLife

	// 解析期间
	parts := strings.Split(period, "-")
	if len(parts) != 2 {
		return decimal.Zero, decimal.Zero, decimal.Zero, errors.New("期间格式错误")
	}

	year, err := strconv.Atoi(parts[0])
	if err != nil {
		return decimal.Zero, decimal.Zero, decimal.Zero, errors.New("期间年份格式错误")
	}

	month, err := strconv.Atoi(parts[1])
	if err != nil {
		return decimal.Zero, decimal.Zero, decimal.Zero, errors.New("期间月份格式错误")
	}

	// 计算已使用月数
	purchaseDate := asset.PurchaseDate
	if purchaseDate == nil {
		return decimal.Zero, decimal.Zero, decimal.Zero, errors.New("资产购置日期未设置")
	}

	periodDate := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.UTC)
	usedMonths := int(periodDate.Sub(*purchaseDate).Hours() / 24 / 30)

	if usedMonths < 0 {
		return decimal.Zero, decimal.Zero, decimal.Zero, errors.New("折旧期间早于购置日期")
	}

	// 根据折旧方法计算
	method := "straight_line"
	if asset.Category != nil && asset.Category.DepreciationMethod != nil {
		method = *asset.Category.DepreciationMethod
	}

	var monthlyDepreciation decimal.Decimal
	var accumulatedDepreciation decimal.Decimal

	switch method {
	case "straight_line":
		// 直线法
		totalMonths := usefulLife * 12
		monthlyDepreciation = originalValue.Div(decimal.NewFromInt(int64(totalMonths)))
		accumulatedDepreciation = monthlyDepreciation.Mul(decimal.NewFromInt(int64(usedMonths)))

	case "declining_balance":
		// 余额递减法（双倍余额递减法）
		rate := decimal.NewFromFloat(2.0).Div(decimal.NewFromInt(int64(usefulLife)))
		currentValue := originalValue
		accumulatedDepreciation = decimal.Zero

		for i := 0; i < usedMonths; i++ {
			monthlyRate := rate.Div(decimal.NewFromInt(12))
			monthlyDep := currentValue.Mul(monthlyRate)
			accumulatedDepreciation = accumulatedDepreciation.Add(monthlyDep)
			currentValue = currentValue.Sub(monthlyDep)
		}
		monthlyDepreciation = currentValue.Mul(rate.Div(decimal.NewFromInt(12)))

	case "sum_of_years":
		// 年数总和法
		totalYears := usefulLife
		sumOfYears := totalYears * (totalYears + 1) / 2

		currentYear := (usedMonths / 12) + 1
		if currentYear > totalYears {
			currentYear = totalYears
		}

		yearlyRate := decimal.NewFromInt(int64(totalYears - currentYear + 1)).Div(decimal.NewFromInt(int64(sumOfYears)))
		monthlyDepreciation = originalValue.Mul(yearlyRate).Div(decimal.NewFromInt(12))

		// 计算累计折旧
		accumulatedDepreciation = decimal.Zero
		for y := 1; y <= currentYear-1; y++ {
			yearRate := decimal.NewFromInt(int64(totalYears - y + 1)).Div(decimal.NewFromInt(int64(sumOfYears)))
			yearlyDep := originalValue.Mul(yearRate)
			accumulatedDepreciation = accumulatedDepreciation.Add(yearlyDep)
		}

		// 加上当年已折旧的月份
		monthsInCurrentYear := usedMonths % 12
		if monthsInCurrentYear > 0 {
			accumulatedDepreciation = accumulatedDepreciation.Add(monthlyDepreciation.Mul(decimal.NewFromInt(int64(monthsInCurrentYear))))
		}

	default:
		return decimal.Zero, decimal.Zero, decimal.Zero, errors.New("不支持的折旧方法")
	}

	// 确保累计折旧不超过原值
	if accumulatedDepreciation.GreaterThan(originalValue) {
		accumulatedDepreciation = originalValue
		monthlyDepreciation = decimal.Zero
	}

	bookValue := originalValue.Sub(accumulatedDepreciation)
	if bookValue.LessThan(decimal.Zero) {
		bookValue = decimal.Zero
	}

	return monthlyDepreciation, accumulatedDepreciation, bookValue, nil
}

// convertToDepreciationResponse 转换为折旧响应格式
func (s *AssetDepreciationService) convertToDepreciationResponse(record models.AssetDepreciationRecord) dto.AssetDepreciationResponse {
	response := dto.AssetDepreciationResponse{
		ID:                      record.ID,
		AssetID:                 record.AssetID,
		Period:                  record.Period,
		DepreciationAmount:      record.DepreciationAmount,
		AccumulatedDepreciation: record.AccumulatedDepreciation,
		BookValue:               record.BookValue,
		DepreciationMethod:      record.DepreciationMethod,
		CalculationBasis:        record.CalculationBasis,
		Remark:                  record.Remark,
		CreatedAt:               record.CreatedAt.Format(time.RFC3339),
		CreatedBy:               *record.CreatedBy,
	}

	if record.Asset != nil {
		// 这里可以添加资产信息的转换
		// response.Asset = convertToAssetResponse(*record.Asset)
	}

	if record.Creator != nil {
		response.Creator = &dto.UserSimpleResponse{
			ID:       record.Creator.ID,
			UserName: record.Creator.UserName,
			JobTitle: record.Creator.JobTitle,
		}
	}

	return response
}
