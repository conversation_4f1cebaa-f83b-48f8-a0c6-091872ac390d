package services

import (
	"errors"
	"hospital-management/internal/dto"
	"hospital-management/internal/models"
	"hospital-management/pkg/logger"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type DepartmentService struct {
	db *gorm.DB
}

func NewDepartmentService(db *gorm.DB) *DepartmentService {
	return &DepartmentService{db: db}
}

// GetDepartments 获取部门列表
func (s *DepartmentService) GetDepartments(req *dto.DepartmentListRequest) ([]dto.DepartmentResponse, int64, error) {
	var departments []models.Department
	var total int64

	query := s.db.Model(&models.Department{}).Preload("Manager")

	// 搜索条件
	if req.Keyword != "" {
		query = query.Where("dept_name ILIKE ? OR dept_code ILIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}
	if req.DeptType != "" {
		query = query.Where("dept_type = ?", req.DeptType)
	}
	if req.IsActive != nil {
		query = query.Where("is_active = ?", *req.IsActive)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count departments:", err)
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Find(&departments).Error; err != nil {
		logger.Error("Failed to get departments:", err)
		return nil, 0, err
	}

	// 转换为响应格式
	responses := make([]dto.DepartmentResponse, len(departments))
	for i, dept := range departments {
		responses[i] = s.convertToResponse(dept)
	}

	return responses, total, nil
}

// GetDepartmentTree 获取部门树形结构
func (s *DepartmentService) GetDepartmentTree() ([]dto.DepartmentTreeResponse, error) {
	var departments []models.Department

	if err := s.db.Where("is_active = ?", true).Find(&departments).Error; err != nil {
		logger.Error("Failed to get departments for tree:", err)
		return nil, err
	}

	// 构建树形结构
	return s.buildDepartmentTree(departments, nil), nil
}

// CreateDepartment 创建部门
func (s *DepartmentService) CreateDepartment(req *dto.CreateDepartmentRequest, userID uuid.UUID) (*dto.DepartmentResponse, error) {
	// 检查部门代码是否重复
	if req.DeptCode != nil {
		var count int64
		if err := s.db.Model(&models.Department{}).Where("code = ?", *req.DeptCode).Count(&count).Error; err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, errors.New("部门代码已存在")
		}
	}

	// 转换并检查父部门是否存在
	var parentID *uuid.UUID
	if req.ParentID != nil && *req.ParentID != "" {
		parsedID, err := uuid.Parse(*req.ParentID)
		if err != nil {
			return nil, errors.New("无效的父部门ID格式")
		}
		parentID = &parsedID

		var parent models.Department
		if err := s.db.First(&parent, parsedID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errors.New("父部门不存在")
			}
			return nil, err
		}
	}

	// 转换管理员ID
	var managerID *uuid.UUID
	if req.ManagerID != nil && *req.ManagerID != "" {
		parsedID, err := uuid.Parse(*req.ManagerID)
		if err != nil {
			return nil, errors.New("无效的管理员ID格式")
		}
		managerID = &parsedID
	}

	// 创建部门
	department := models.Department{
		ParentID:     parentID,
		DeptName:     req.DeptName,
		DeptCode:     req.DeptCode,
		DeptType:     StringPtr(req.DeptType),
		Description:  req.Description,
		ManagerID:    managerID,
		ContactPhone: req.ContactPhone,
		ContactEmail: req.ContactEmail,
		IsActive:     BoolPtr(req.IsActive),
	}
	department.CreatedBy = &userID

	if err := s.db.Create(&department).Error; err != nil {
		logger.Error("Failed to create department:", err)
		return nil, err
	}

	// 预加载关联数据
	if err := s.db.Preload("Manager").First(&department, department.ID).Error; err != nil {
		logger.Error("Failed to preload department data:", err)
		return nil, err
	}

	response := s.convertToResponse(department)
	return &response, nil
}

// UpdateDepartment 更新部门
func (s *DepartmentService) UpdateDepartment(id uuid.UUID, req *dto.UpdateDepartmentRequest, userID uuid.UUID) (*dto.DepartmentResponse, error) {
	var department models.Department
	if err := s.db.First(&department, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("部门不存在")
		}
		return nil, err
	}

	// 检查部门代码是否重复
	if req.DeptCode != nil {
		var count int64
		if err := s.db.Model(&models.Department{}).Where("code = ? AND id != ?", *req.DeptCode, id).Count(&count).Error; err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, errors.New("部门代码已存在")
		}
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.ParentID != nil {
		if *req.ParentID == "" {
			updates["parent_id"] = nil
		} else {
			parsedID, err := uuid.Parse(*req.ParentID)
			if err != nil {
				return nil, errors.New("无效的父部门ID格式")
			}
			updates["parent_id"] = parsedID
		}
	}
	if req.DeptName != nil {
		updates["name"] = *req.DeptName
	}
	if req.DeptCode != nil {
		updates["code"] = *req.DeptCode
	}
	if req.DeptType != nil {
		updates["dept_type"] = *req.DeptType
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.ManagerID != nil {
		if *req.ManagerID == "" {
			updates["manager_id"] = nil
		} else {
			parsedID, err := uuid.Parse(*req.ManagerID)
			if err != nil {
				return nil, errors.New("无效的管理员ID格式")
			}
			updates["manager_id"] = parsedID
		}
	}
	if req.ContactPhone != nil {
		updates["contact_phone"] = *req.ContactPhone
	}
	if req.ContactEmail != nil {
		updates["contact_email"] = *req.ContactEmail
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}
	updates["updated_by"] = userID

	if err := s.db.Model(&department).Updates(updates).Error; err != nil {
		logger.Error("Failed to update department:", err)
		return nil, err
	}

	// 预加载关联数据
	if err := s.db.Preload("Manager").First(&department, id).Error; err != nil {
		logger.Error("Failed to preload department data:", err)
		return nil, err
	}

	response := s.convertToResponse(department)
	return &response, nil
}

// DeleteDepartment 删除部门
func (s *DepartmentService) DeleteDepartment(id uuid.UUID) error {
	// 检查是否有子部门
	var childCount int64
	if err := s.db.Model(&models.Department{}).Where("parent_id = ?", id).Count(&childCount).Error; err != nil {
		return err
	}
	if childCount > 0 {
		return errors.New("存在子部门，无法删除")
	}

	// 检查是否有员工
	var userCount int64
	if err := s.db.Model(&models.User{}).Where("department_id = ?", id).Count(&userCount).Error; err != nil {
		return err
	}
	if userCount > 0 {
		return errors.New("部门下存在员工，无法删除")
	}

	// 软删除部门
	if err := s.db.Delete(&models.Department{}, id).Error; err != nil {
		logger.Error("Failed to delete department:", err)
		return err
	}

	return nil
}

// convertToResponse 转换为响应格式
func (s *DepartmentService) convertToResponse(dept models.Department) dto.DepartmentResponse {
	response := dto.DepartmentResponse{
		ID:           dept.ID,
		ParentID:     dept.ParentID,
		DeptName:     dept.DeptName,
		DeptCode:     dept.DeptCode,
		DeptType:     StringValue(dept.DeptType),
		Description:  StringValue(dept.Description),
		ManagerID:    dept.ManagerID,
		ContactPhone: StringValue(dept.ContactPhone),
		ContactEmail: StringValue(dept.ContactEmail),
		IsActive:     BoolValue(dept.IsActive),
		CreatedAt:    dept.CreatedAt.Format(time.RFC3339),
		UpdatedAt:    dept.UpdatedAt.Format(time.RFC3339),
	}

	if dept.Manager != nil {
		response.Manager = &dto.UserSimpleResponse{
			ID:       dept.Manager.ID,
			UserName: dept.Manager.UserName,
			JobTitle: dept.Manager.JobTitle,
		}
	}

	return response
}

// buildDepartmentTree 构建部门树形结构
func (s *DepartmentService) buildDepartmentTree(departments []models.Department, parentID *uuid.UUID) []dto.DepartmentTreeResponse {
	var result []dto.DepartmentTreeResponse

	for _, dept := range departments {
		if (parentID == nil && dept.ParentID == nil) || (parentID != nil && dept.ParentID != nil && *dept.ParentID == *parentID) {
			node := dto.DepartmentTreeResponse{
				ID:       dept.ID,
				DeptName: dept.DeptName,
				DeptCode: dept.DeptCode,
				DeptType: StringValue(dept.DeptType),
				IsActive: BoolValue(dept.IsActive),
				Children: s.buildDepartmentTree(departments, &dept.ID),
			}
			result = append(result, node)
		}
	}

	return result
}
