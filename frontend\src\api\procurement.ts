import request from './request'

// 供应商管理相关接口
export interface Supplier {
  id: string
  name: string
  credit_code?: string
  status: number  // 0: 黑名单, 1: 合作中
  contact_info?: {
    contact_person?: string
    contact_phone?: string
    contact_email?: string
    address?: string
    bank_account?: string
    bank_name?: string
    tax_number?: string
    business_license?: string
    qualification_level?: string
    type?: 'goods' | 'service' | 'both'
  }
  created_at: string
  updated_at: string
}

export interface SupplierForm {
  name: string
  credit_code?: string
  status: number  // 0: 黑名单, 1: 合作中
  contact_info?: {
    contact_person?: string
    contact_phone?: string
    contact_email?: string
    address?: string
    bank_account?: string
    bank_name?: string
    tax_number?: string
    business_license?: string
    qualification_level?: string
    type?: 'goods' | 'service' | 'both'
  }
}

export interface SupplierQuery {
  keyword?: string
  status?: number  // 0: 黑名单, 1: 合作中
  page?: number
  page_size?: number
}

// 获取供应商列表
export const getSuppliers = (params: SupplierQuery): Promise<{
  list: Supplier[]
  total: number
  page: number
  page_size: number
}> => {
  return request.get('/api/v1/suppliers', { params })
}

// 获取供应商详情
export const getSupplierDetail = (id: number): Promise<Supplier> => {
  return request.get(`/api/v1/suppliers/${id}`)
}

// 创建供应商
export const createSupplier = (data: SupplierForm): Promise<Supplier> => {
  return request.post('/api/v1/suppliers', data)
}

// 更新供应商
export const updateSupplier = (id: number, data: SupplierForm): Promise<Supplier> => {
  return request.put(`/api/v1/suppliers/${id}`, data)
}

// 删除供应商
export const deleteSupplier = (id: number): Promise<void> => {
  return request.delete(`/api/v1/suppliers/${id}`)
}

// 采购申请相关接口
export interface ProcurementRequisition {
  id: number
  requisition_no: string
  title: string
  applicant_id: number
  applicant_name: string
  department_id: number
  department_name: string
  supplier_id?: number
  supplier_name?: string
  total_amount: number
  urgency: 'low' | 'medium' | 'high'
  expected_date: string
  purpose: string
  status: 'draft' | 'submitted' | 'approved' | 'rejected' | 'completed'
  items: ProcurementItem[]
  created_at: string
  updated_at: string
}

export interface ProcurementItem {
  id?: number
  name: string
  specification: string
  unit: string
  quantity: number
  unit_price: number
  total_price: number
  remark?: string
}

export interface ProcurementRequisitionForm {
  title: string
  department_id: number
  supplier_id?: number
  urgency: 'low' | 'medium' | 'high'
  expected_date: string
  purpose: string
  items: ProcurementItem[]
}

export interface ProcurementRequisitionQuery {
  status?: string
  urgency?: string
  applicant_id?: number
  department_id?: number
  supplier_id?: number
  start_date?: string
  end_date?: string
  page?: number
  page_size?: number
}

// 获取采购申请列表
export const getProcurementRequisitions = (params: ProcurementRequisitionQuery): Promise<{
  list: ProcurementRequisition[]
  total: number
  page: number
  page_size: number
}> => {
  return request.get('/api/v1/purchase/requisitions', { params })
}

// 获取采购申请详情
export const getProcurementRequisitionDetail = (id: number): Promise<ProcurementRequisition> => {
  return request.get(`/api/v1/purchase/requisitions/${id}`)
}

// 创建采购申请
export const createProcurementRequisition = (data: ProcurementRequisitionForm): Promise<ProcurementRequisition> => {
  return request.post('/api/v1/purchase/requisitions', data)
}

// 更新采购申请
export const updateProcurementRequisition = (id: number, data: ProcurementRequisitionForm): Promise<ProcurementRequisition> => {
  return request.put(`/api/v1/purchase/requisitions/${id}`, data)
}

// 提交采购申请
export const submitProcurementRequisition = (id: number): Promise<void> => {
  return request.post(`/api/v1/purchase/requisitions/${id}/submit`)
}

// 删除采购申请
export const deleteProcurementRequisition = (id: number): Promise<void> => {
  return request.delete(`/api/v1/purchase/requisitions/${id}`)
}

// 获取我的采购申请
export const getMyProcurementRequisitions = (params: ProcurementRequisitionQuery): Promise<{
  list: ProcurementRequisition[]
  total: number
  page: number
  page_size: number
}> => {
  return request.get('/api/v1/procurement/requisitions/my', { params })
}

// 采购统计相关接口
export interface ProcurementStatistics {
  total_requisitions: number
  pending_requisitions: number
  approved_requisitions: number
  total_amount: number
  monthly_amount: number
  top_suppliers: Array<{
    supplier_name: string
    amount: number
    count: number
  }>
  category_statistics: Array<{
    category: string
    amount: number
    count: number
  }>
}

// 获取采购统计数据
export const getProcurementStatistics = (): Promise<ProcurementStatistics> => {
  return request.get('/api/v1/procurement/statistics')
}

// 采购分析相关接口
export interface ProcurementAnalysisQuery {
  start_date?: string
  end_date?: string
  department_id?: number
  supplier_id?: number
  category?: string
}

export interface ProcurementAnalysisData {
  period: string
  amount: number
  count: number
  avg_amount: number
}

// 获取采购趋势分析
export const getProcurementTrendAnalysis = (params: ProcurementAnalysisQuery): Promise<ProcurementAnalysisData[]> => {
  return request.get('/api/v1/procurement/analysis/trend', { params })
}

// 获取供应商分析
export const getSupplierAnalysis = (params: ProcurementAnalysisQuery): Promise<Array<{
  supplier_name: string
  amount: number
  count: number
  avg_amount: number
  performance_score: number
}>> => {
  return request.get('/api/v1/procurement/analysis/supplier', { params })
}

// 获取部门采购分析
export const getDepartmentProcurementAnalysis = (params: ProcurementAnalysisQuery): Promise<Array<{
  department_name: string
  amount: number
  count: number
  avg_amount: number
}>> => {
  return request.get('/api/v1/procurement/analysis/department', { params })
}
