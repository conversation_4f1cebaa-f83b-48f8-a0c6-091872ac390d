package services

import (
	"errors"
	"hospital-management/internal/dto"
	"hospital-management/internal/models"
	"hospital-management/pkg/logger"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// 使用通用辅助函数 utils.go

type ApprovalService struct {
	db *gorm.DB
}

func NewApprovalService(db *gorm.DB) *ApprovalService {
	return &ApprovalService{db: db}
}

// CreateApprovalFlow 创建审批流
func (s *ApprovalService) CreateApprovalFlow(req *dto.CreateApprovalFlowRequest, userID uuid.UUID) (*dto.ApprovalFlowResponse, error) {
	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建审批流
	flow := models.ApprovalFlow{
		BusinessType: req.BusinessType,
		BusinessID:   req.BusinessID,
		Title:        &req.Title,
		Content:      req.Content,
		Status:       "pending",
	}
	flow.CreatedBy = &userID

	if err := tx.Create(&flow).Error; err != nil {
		tx.Rollback()
		logger.Error("Failed to create approval flow:", err)
		return nil, err
	}

	// 创建审批节点
	for _, nodeReq := range req.ApprovalNodes {
		node := models.ApprovalNode{
			FlowID:       flow.ID,
			NodeOrder:    &nodeReq.NodeOrder,
			NodeName:     &nodeReq.NodeName,
			ApproverType: &nodeReq.ApproverType,
			ApproverID:   nodeReq.ApproverID,
			IsRequired:   &nodeReq.IsRequired,
			Status:       "pending",
			Description:  nodeReq.Description,
		}
		node.CreatedBy = &userID

		if err := tx.Create(&node).Error; err != nil {
			tx.Rollback()
			logger.Error("Failed to create approval node:", err)
			return nil, err
		}

		// 设置第一个节点为当前节点
		if nodeReq.NodeOrder == 1 {
			flow.CurrentNodeID = &node.ID
		}
	}

	// 更新审批流的当前节点
	if flow.CurrentNodeID != nil {
		if err := tx.Model(&flow).Update("current_node_id", *flow.CurrentNodeID).Error; err != nil {
			tx.Rollback()
			logger.Error("Failed to update current node:", err)
			return nil, err
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logger.Error("Failed to commit transaction:", err)
		return nil, err
	}

	// 获取完整的审批流信息
	return s.GetApprovalFlowByID(flow.ID)
}

// GetApprovalFlowByID 根据ID获取审批流
func (s *ApprovalService) GetApprovalFlowByID(id uuid.UUID) (*dto.ApprovalFlowResponse, error) {
	var flow models.ApprovalFlow
	if err := s.db.Preload("Creator").Preload("ApprovalNodes").First(&flow, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("审批流不存在")
		}
		logger.Error("Failed to get approval flow:", err)
		return nil, err
	}

	return s.convertToResponse(flow), nil
}

// GetApprovalFlows 获取审批流列表
func (s *ApprovalService) GetApprovalFlows(req *dto.ApprovalFlowListRequest) ([]dto.ApprovalFlowResponse, int64, error) {
	var flows []models.ApprovalFlow
	var total int64

	query := s.db.Model(&models.ApprovalFlow{}).Preload("Creator")

	// 搜索条件
	if req.BusinessType != "" {
		query = query.Where("business_type = ?", req.BusinessType)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.CreatedBy != nil {
		query = query.Where("created_by = ?", *req.CreatedBy)
	}
	if req.Keyword != "" {
		query = query.Where("title ILIKE ?", "%"+req.Keyword+"%")
	}
	if req.StartDate != "" {
		query = query.Where("created_at >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		query = query.Where("created_at <= ?", req.EndDate+" 23:59:59")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count approval flows:", err)
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&flows).Error; err != nil {
		logger.Error("Failed to get approval flows:", err)
		return nil, 0, err
	}

	// 转换为响应格式
	responses := make([]dto.ApprovalFlowResponse, len(flows))
	for i, flow := range flows {
		responses[i] = *s.convertToResponse(flow)
	}

	return responses, total, nil
}

// GetTodoApprovals 获取待办审批列表
func (s *ApprovalService) GetTodoApprovals(userID uuid.UUID, req *dto.TodoApprovalRequest) ([]dto.TodoApprovalResponse, int64, error) {
	var nodes []models.ApprovalNode
	var total int64

	query := s.db.Model(&models.ApprovalNode{}).
		Preload("Flow").
		Preload("Flow.Creator").
		Joins("JOIN tbl_approval_flows ON tbl_approval_nodes.flow_id = tbl_approval_flows.id").
		Where("tbl_approval_nodes.approver_id = ? AND tbl_approval_nodes.status = ?", userID, "pending").
		Where("tbl_approval_flows.status = ?", "pending")

	// 搜索条件
	if req.BusinessType != "" {
		query = query.Where("tbl_approval_flows.business_type = ?", req.BusinessType)
	}
	if req.Keyword != "" {
		query = query.Where("tbl_approval_flows.title ILIKE ?", "%"+req.Keyword+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count todo approvals:", err)
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("tbl_approval_nodes.created_at DESC").Find(&nodes).Error; err != nil {
		logger.Error("Failed to get todo approvals:", err)
		return nil, 0, err
	}

	// 转换为响应格式
	responses := make([]dto.TodoApprovalResponse, len(nodes))
	for i, node := range nodes {
		responses[i] = dto.TodoApprovalResponse{
			ID:           node.ID,
			FlowID:       node.FlowID,
			NodeID:       node.ID,
			BusinessType: node.Flow.BusinessType,
			Title:        StringValue(node.Flow.Title),
			NodeName:     StringValue(node.NodeName),
			Status:       node.Status,
			IsRequired:   BoolValue(node.IsRequired),
			CreatedAt:    node.CreatedAt.Format(time.RFC3339),
		}

		if node.Flow.Creator != nil {
			responses[i].Creator = &dto.UserSimpleResponse{
				ID:       node.Flow.Creator.ID,
				UserName: node.Flow.Creator.UserName,
				JobTitle: node.Flow.Creator.JobTitle,
			}
		}

		responses[i].Flow = &dto.ApprovalFlowSimple{
			ID:           node.Flow.ID,
			BusinessType: node.Flow.BusinessType,
			BusinessID:   node.Flow.BusinessID,
			Title:        StringValue(node.Flow.Title),
			Status:       node.Flow.Status,
		}
	}

	return responses, total, nil
}

// ApproveNode 审批节点
func (s *ApprovalService) ApproveNode(nodeID uuid.UUID, req *dto.ApprovalActionRequest, userID uuid.UUID) error {
	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 获取审批节点
	var node models.ApprovalNode
	if err := tx.Preload("Flow").First(&node, nodeID).Error; err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("审批节点不存在")
		}
		return err
	}

	// 检查节点状态
	if node.Status != "pending" {
		tx.Rollback()
		return errors.New("该节点已处理")
	}

	// 检查审批权限
	if node.ApproverID != userID {
		tx.Rollback()
		return errors.New("无权限审批该节点")
	}

	// 更新节点状态
	now := time.Now()
	updates := map[string]interface{}{
		"status":      req.Action + "d", // approved, rejected
		"approved_at": &now,
		"approved_by": userID,
		"comments":    req.Comments,
		"updated_by":  userID,
	}

	if err := tx.Model(&node).Updates(updates).Error; err != nil {
		tx.Rollback()
		logger.Error("Failed to update approval node:", err)
		return err
	}

	// 处理审批流状态
	if req.Action == "approve" {
		// 查找下一个节点
		var nextNode models.ApprovalNode
		err := tx.Where("flow_id = ? AND node_order > ? AND status = ?",
			node.FlowID, node.NodeOrder, "pending").
			Order("node_order ASC").First(&nextNode).Error

		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// 没有下一个节点，审批流完成
				if err := tx.Model(&node.Flow).Updates(map[string]interface{}{
					"status":          "approved",
					"current_node_id": nil,
					"updated_by":      userID,
				}).Error; err != nil {
					tx.Rollback()
					return err
				}
			} else {
				tx.Rollback()
				return err
			}
		} else {
			// 更新当前节点为下一个节点
			if err := tx.Model(&node.Flow).Updates(map[string]interface{}{
				"current_node_id": nextNode.ID,
				"updated_by":      userID,
			}).Error; err != nil {
				tx.Rollback()
				return err
			}
		}
	} else if req.Action == "reject" {
		// 拒绝，整个审批流被拒绝
		if err := tx.Model(&node.Flow).Updates(map[string]interface{}{
			"status":          "rejected",
			"current_node_id": nil,
			"updated_by":      userID,
		}).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logger.Error("Failed to commit transaction:", err)
		return err
	}

	return nil
}

// UpdateApprovalFlowStatus 更新审批流状态
func (s *ApprovalService) UpdateApprovalFlowStatus(id uuid.UUID, req *dto.UpdateApprovalFlowStatusRequest, userID uuid.UUID) error {
	var flow models.ApprovalFlow
	if err := s.db.First(&flow, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("审批流不存在")
		}
		return err
	}

	// 更新状态
	updates := map[string]interface{}{
		"status":     req.Status,
		"updated_by": userID,
	}

	if req.Status == "cancelled" {
		updates["current_node_id"] = nil
	}

	if err := s.db.Model(&flow).Updates(updates).Error; err != nil {
		logger.Error("Failed to update approval flow status:", err)
		return err
	}

	return nil
}

// convertToResponse 转换为响应格式
func (s *ApprovalService) convertToResponse(flow models.ApprovalFlow) *dto.ApprovalFlowResponse {
	response := &dto.ApprovalFlowResponse{
		ID:            flow.ID,
		BusinessType:  flow.BusinessType,
		BusinessID:    flow.BusinessID,
		Title:         StringValue(flow.Title),
		Content:       flow.Content,
		Status:        flow.Status,
		CurrentNodeID: flow.CurrentNodeID,
		CreatedAt:     flow.CreatedAt.Format(time.RFC3339),
		UpdatedAt:     flow.UpdatedAt.Format(time.RFC3339),
		CreatedBy:     *flow.CreatedBy,
	}

	if flow.Creator != nil {
		response.Creator = &dto.UserSimpleResponse{
			ID:       flow.Creator.ID,
			UserName: flow.Creator.UserName,
			JobTitle: flow.Creator.JobTitle,
		}
	}

	if len(flow.Nodes) > 0 {
		response.ApprovalNodes = make([]dto.ApprovalNodeResponse, len(flow.Nodes))
		for i, node := range flow.Nodes {
			response.ApprovalNodes[i] = dto.ApprovalNodeResponse{
				ID:           node.ID,
				FlowID:       node.FlowID,
				NodeOrder:    IntValue(node.NodeOrder),
				NodeName:     StringValue(node.NodeName),
				ApproverType: StringValue(node.ApproverType),
				ApproverID:   node.ApproverID,
				IsRequired:   BoolValue(node.IsRequired),
				Status:       node.Status,
				Comments:     node.Comments,
				Description:  node.Description,
			}

			if node.ApprovedAt != nil {
				approvedAtStr := node.ApprovedAt.Format(time.RFC3339)
				response.ApprovalNodes[i].ApprovedAt = &approvedAtStr
			}
			if node.ApprovedBy != nil {
				response.ApprovalNodes[i].ApprovedBy = node.ApprovedBy
			}
		}
	}

	return response
}
