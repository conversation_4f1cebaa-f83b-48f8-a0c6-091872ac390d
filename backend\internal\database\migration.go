package database

import (
	"fmt"
	"log"
	"time"

	"hospital-management/internal/models"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// MigrationManager 数据库迁移管理器
type MigrationManager struct {
	db *gorm.DB
}

// NewMigrationManager 创建迁移管理器
func NewMigrationManager(db *gorm.DB) *MigrationManager {
	return &MigrationManager{db: db}
}

// RunMigrations 执行所有迁移
func (m *MigrationManager) RunMigrations() error {
	log.Println("开始执行数据库迁移...")

	// 1. 创建所有表结构
	if err := m.createTables(); err != nil {
		return fmt.Errorf("创建表结构失败: %w", err)
	}

	// 2. 创建索引
	if err := m.createIndexes(); err != nil {
		return fmt.Errorf("创建索引失败: %w", err)
	}

	// 3. 插入初始数据
	if err := m.seedData(); err != nil {
		return fmt.Errorf("插入初始数据失败: %w", err)
	}

	log.Println("数据库迁移完成!")
	return nil
}

// createTables 创建所有表结构
func (m *MigrationManager) createTables() error {
	log.Println("正在创建表结构...")

	// 使用原始的AutoMigrate，让GORM处理依赖关系
	return m.db.AutoMigrate(
		// 基础模型 - 按依赖顺序排列
		&models.Department{},
		&models.User{},
		&models.Role{},
		&models.UserRole{},
		&models.File{},

		// 审批流模型
		&models.ApprovalFlow{},
		&models.ApprovalNode{},

		// 预算管理模型
		&models.BudgetScheme{},
		&models.BudgetSubject{},
		&models.BudgetItem{},

		// 支出控制模型
		&models.PreApplication{},
		&models.ExpenseApplication{},
		&models.ExpenseDetail{},
		&models.Payment{},

		// 采购管理模型
		&models.Supplier{},
		&models.PurchaseRequisition{},

		// 合同管理模型
		&models.Contract{},
		&models.ContractPaymentSchedule{},

		// 资产管理模型
		&models.AssetCategory{},
		&models.Asset{},
		&models.AssetDepreciationRecord{},
		&models.AssetMaintenanceRecord{},
		&models.AssetChangeRecord{},

		// 消息管理模型
		&models.Message{},
		&models.MessageReceiver{},
	)
}

// createDepartmentTable 手动创建部门表（不包含manager外键）
func (m *MigrationManager) createDepartmentTable() error {
	log.Println("正在手动创建部门表...")

	// 检查表是否已存在
	var count int64
	if err := m.db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'tbl_departments'").Scan(&count).Error; err != nil {
		return err
	}

	if count > 0 {
		log.Println("✓ 部门表已存在")
		return nil
	}

	// 手动创建部门表SQL（不包含manager外键约束）
	createTableSQL := `
		CREATE TABLE tbl_departments (
			id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
			created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			deleted_at TIMESTAMPTZ,
			created_by UUID,
			updated_by UUID,
			parent_id UUID,
			name VARCHAR(100) NOT NULL,
			code VARCHAR(50) UNIQUE,
			level BIGINT DEFAULT 1,
			sort_order BIGINT DEFAULT 0,
			description TEXT,
			status BIGINT DEFAULT 1,
			manager_id UUID,
			CONSTRAINT fk_tbl_departments_children FOREIGN KEY (parent_id) REFERENCES tbl_departments(id)
		)`

	if err := m.db.Exec(createTableSQL).Error; err != nil {
		return fmt.Errorf("创建部门表失败: %w", err)
	}

	// 创建删除索引
	if err := m.db.Exec("CREATE INDEX IF NOT EXISTS idx_tbl_departments_deleted_at ON tbl_departments(deleted_at)").Error; err != nil {
		log.Printf("⚠ 创建部门表删除索引失败: %v", err)
	}

	log.Println("✓ 已手动创建部门表")
	return nil
}

// addDeferredConstraints 添加延迟的外键约束
func (m *MigrationManager) addDeferredConstraints() error {
	log.Println("正在添加延迟的外键约束...")

	// 检查约束是否已存在
	var count int64
	checkSQL := `SELECT COUNT(*) FROM information_schema.table_constraints
				 WHERE constraint_name = 'fk_departments_manager'
				 AND table_name = 'tbl_departments'`

	if err := m.db.Raw(checkSQL).Scan(&count).Error; err != nil {
		log.Printf("⚠ 检查约束失败: %v", err)
		return nil
	}

	if count == 0 {
		// 添加部门管理员外键约束
		sql := `ALTER TABLE tbl_departments ADD CONSTRAINT fk_departments_manager
				FOREIGN KEY (manager_id) REFERENCES tbl_users(id) ON DELETE SET NULL`

		if err := m.db.Exec(sql).Error; err != nil {
			log.Printf("⚠ 添加部门管理员外键约束失败: %v", err)
		} else {
			log.Println("✓ 已添加部门管理员外键约束")
		}
	} else {
		log.Println("✓ 部门管理员外键约束已存在")
	}

	return nil
}

// createIndexes 创建索引
func (m *MigrationManager) createIndexes() error {
	log.Println("正在创建索引...")

	indexes := []struct {
		table string
		sql   string
		desc  string
	}{
		// 部门表索引
		{"tbl_departments", "CREATE INDEX IF NOT EXISTS idx_departments_parent_id ON tbl_departments(parent_id)", "部门父级索引"},
		{"tbl_departments", "CREATE INDEX IF NOT EXISTS idx_departments_code ON tbl_departments(code)", "部门代码索引"},

		// 用户表索引
		{"tbl_users", "CREATE INDEX IF NOT EXISTS idx_users_department_id ON tbl_users(department_id)", "用户部门索引"},
		{"tbl_users", "CREATE INDEX IF NOT EXISTS idx_users_employee_id ON tbl_users(employee_id)", "员工编号索引"},

		// 文件表索引
		{"tbl_files", "CREATE INDEX IF NOT EXISTS idx_files_business ON tbl_files(business_id, business_type)", "文件业务索引"},

		// 审批流索引
		{"tbl_approval_flows", "CREATE UNIQUE INDEX IF NOT EXISTS idx_flows_business ON tbl_approval_flows(business_id, business_type) WHERE deleted_at IS NULL", "审批流业务唯一索引"},
		{"tbl_approval_nodes", "CREATE INDEX IF NOT EXISTS idx_nodes_approver_status ON tbl_approval_nodes(approver_id, status)", "审批节点状态索引"},

		// 预算表索引
		{"tbl_budget_items", "CREATE INDEX IF NOT EXISTS idx_budget_items_scheme_dept ON tbl_budget_items(scheme_id, department_id)", "预算项目索引"},

		// 支出控制索引
		{"tbl_expense_applications", "CREATE INDEX IF NOT EXISTS idx_expense_applicant ON tbl_expense_applications(applicant_id)", "报销申请人索引"},
		{"tbl_expense_details", "CREATE INDEX IF NOT EXISTS idx_expense_details_app_id ON tbl_expense_details(application_id)", "报销明细索引"},

		// 付款单索引
		{"tbl_payments", "CREATE INDEX IF NOT EXISTS idx_payments_source ON tbl_payments(source_id, source_type)", "付款来源索引"},

		// 合同付款计划索引
		{"tbl_contract_payment_schedules", "CREATE INDEX IF NOT EXISTS idx_schedules_contract_id ON tbl_contract_payment_schedules(contract_id)", "合同付款计划索引"},

		// 资产相关索引
		{"tbl_assets", "CREATE INDEX IF NOT EXISTS idx_assets_category_id ON tbl_assets(category_id)", "资产分类索引"},
		{"tbl_assets", "CREATE INDEX IF NOT EXISTS idx_assets_owner_dept ON tbl_assets(owner_dept_id)", "资产所属部门索引"},
		{"tbl_asset_depreciation_records", "CREATE INDEX IF NOT EXISTS idx_depreciation_asset_id ON tbl_asset_depreciation_records(asset_id)", "折旧记录资产索引"},
		{"tbl_asset_depreciation_records", "CREATE INDEX IF NOT EXISTS idx_depreciation_period ON tbl_asset_depreciation_records(period)", "折旧记录期间索引"},
		{"tbl_asset_maintenance_records", "CREATE INDEX IF NOT EXISTS idx_maintenance_asset_id ON tbl_asset_maintenance_records(asset_id)", "维护记录资产索引"},
		{"tbl_asset_maintenance_records", "CREATE INDEX IF NOT EXISTS idx_maintenance_date ON tbl_asset_maintenance_records(maintenance_date)", "维护记录日期索引"},
		{"tbl_asset_change_records", "CREATE INDEX IF NOT EXISTS idx_change_asset_id ON tbl_asset_change_records(asset_id)", "变更记录资产索引"},
		{"tbl_asset_change_records", "CREATE INDEX IF NOT EXISTS idx_change_type ON tbl_asset_change_records(change_type)", "变更记录类型索引"},
	}

	for _, idx := range indexes {
		if err := m.db.Exec(idx.sql).Error; err != nil {
			log.Printf("⚠ 创建索引失败 (%s): %v", idx.desc, err)
		} else {
			log.Printf("✓ 已创建索引: %s", idx.desc)
		}
	}

	return nil
}

// seedData 插入初始数据
func (m *MigrationManager) seedData() error {
	log.Println("正在插入初始数据...")

	// 检查是否已有数据
	var count int64
	if err := m.db.Model(&models.User{}).Count(&count).Error; err != nil {
		return err
	}

	if count > 0 {
		log.Println("数据库已有数据，更新部门字段...")
		return m.updateDepartmentFields()
	}

	return m.db.Transaction(func(tx *gorm.DB) error {
		// 1. 创建部门
		departments := []models.Department{
			{
				BaseModel:    models.BaseModel{ID: uuid.New()},
				Name:         "院办",
				Code:         stringPtr("D_ADMIN"),
				DeptType:     stringPtr("administrative"),
				Description:  stringPtr("负责医院行政管理工作"),
				ContactPhone: stringPtr("010-12345678"),
				ContactEmail: stringPtr("<EMAIL>"),
				Level:        int64Ptr(1),
				Status:       int64Ptr(1),
				IsActive:     boolPtr(true),
			},
			{
				BaseModel:    models.BaseModel{ID: uuid.New()},
				Name:         "信息科",
				Code:         stringPtr("D_INFO"),
				DeptType:     stringPtr("administrative"),
				Description:  stringPtr("负责医院信息化建设和维护"),
				ContactPhone: stringPtr("010-12345679"),
				ContactEmail: stringPtr("<EMAIL>"),
				Level:        int64Ptr(1),
				Status:       int64Ptr(1),
				IsActive:     boolPtr(true),
			},
			{
				BaseModel:    models.BaseModel{ID: uuid.New()},
				Name:         "财务科",
				Code:         stringPtr("D_FINANCE"),
				DeptType:     stringPtr("administrative"),
				Description:  stringPtr("负责医院财务管理和预算控制"),
				ContactPhone: stringPtr("010-12345680"),
				ContactEmail: stringPtr("<EMAIL>"),
				Level:        int64Ptr(1),
				Status:       int64Ptr(1),
				IsActive:     boolPtr(true),
			},
			{
				BaseModel:    models.BaseModel{ID: uuid.New()},
				Name:         "心血管内科",
				Code:         stringPtr("D_CARDIO"),
				DeptType:     stringPtr("clinical"),
				Description:  stringPtr("负责心血管疾病的诊断和治疗"),
				ContactPhone: stringPtr("010-12345681"),
				ContactEmail: stringPtr("<EMAIL>"),
				Level:        int64Ptr(1),
				Status:       int64Ptr(1),
				IsActive:     boolPtr(true),
			},
		}

		for _, dept := range departments {
			if err := tx.Create(&dept).Error; err != nil {
				return fmt.Errorf("创建部门失败: %w", err)
			}
		}
		log.Printf("✓ 已创建 %d 个部门", len(departments))

		// 1.1 创建子部门
		subDepartments := []models.Department{
			{
				BaseModel:    models.BaseModel{ID: uuid.New()},
				ParentID:     &departments[3].ID, // 心血管内科
				Name:         "心血管内科一病区",
				Code:         stringPtr("D_CARDIO_1"),
				DeptType:     stringPtr("clinical"),
				Description:  stringPtr("心血管内科第一病区"),
				ContactPhone: stringPtr("010-12345682"),
				ContactEmail: stringPtr("<EMAIL>"),
				Level:        int64Ptr(2),
				Status:       int64Ptr(1),
				IsActive:     boolPtr(true),
			},
			{
				BaseModel:    models.BaseModel{ID: uuid.New()},
				ParentID:     &departments[3].ID, // 心血管内科
				Name:         "心血管内科二病区",
				Code:         stringPtr("D_CARDIO_2"),
				DeptType:     stringPtr("clinical"),
				Description:  stringPtr("心血管内科第二病区"),
				ContactPhone: stringPtr("010-12345683"),
				ContactEmail: stringPtr("<EMAIL>"),
				Level:        int64Ptr(2),
				Status:       int64Ptr(1),
				IsActive:     boolPtr(true),
			},
		}

		for _, subDept := range subDepartments {
			if err := tx.Create(&subDept).Error; err != nil {
				return fmt.Errorf("创建子部门失败: %w", err)
			}
		}
		log.Printf("✓ 已创建 %d 个子部门", len(subDepartments))

		// 2. 创建用户
		users := []models.User{
			{
				BaseModel:    models.BaseModel{ID: uuid.New()},
				DepartmentID: departments[0].ID, // 院办
				UserName:     "系统管理员",
				EmployeeID:   stringPtr("SYSADMIN"),
				Email:        stringPtr("<EMAIL>"),
				PasswordHash: "$2a$10$8O2/dErF4nORjIeB.NWLfOQPnhlCcIyfjwChPVY5GJlRiJhPQ6Dee", // 默认密码: admin123
				JobTitle:     stringPtr("管理员"),
				IsActive:     true,
			},
			{
				BaseModel:    models.BaseModel{ID: uuid.New()},
				DepartmentID: departments[1].ID, // 信息科
				UserName:     "张三",
				EmployeeID:   stringPtr("EMP001"),
				Email:        stringPtr("<EMAIL>"),
				PasswordHash: "$2a$10$8O2/dErF4nORjIeB.NWLfOQPnhlCcIyfjwChPVY5GJlRiJhPQ6Dee", // 默认密码: admin123
				JobTitle:     stringPtr("软件工程师"),
				IsActive:     true,
			},
			{
				BaseModel:    models.BaseModel{ID: uuid.New()},
				DepartmentID: departments[2].ID, // 财务科
				UserName:     "李四",
				EmployeeID:   stringPtr("EMP002"),
				Email:        stringPtr("<EMAIL>"),
				PasswordHash: "$2a$10$8O2/dErF4nORjIeB.NWLfOQPnhlCcIyfjwChPVY5GJlRiJhPQ6Dee", // 默认密码: admin123
				JobTitle:     stringPtr("财务科长"),
				IsActive:     true,
			},
			{
				BaseModel:    models.BaseModel{ID: uuid.New()},
				DepartmentID: departments[3].ID, // 心血管内科
				UserName:     "王五",
				EmployeeID:   stringPtr("EMP003"),
				Email:        stringPtr("<EMAIL>"),
				PasswordHash: "$2a$10$abcdefghijklmnopqrstuv",
				JobTitle:     stringPtr("主治医师"),
				IsActive:     true,
			},
		}

		for i := range users {
			users[i].CreatedBy = &users[0].ID // 系统管理员创建
			if err := tx.Create(&users[i]).Error; err != nil {
				return fmt.Errorf("创建用户失败: %w", err)
			}
		}
		log.Printf("✓ 已创建 %d 个用户", len(users))

		// 更新部门管理员
		if err := tx.Model(&departments[2]).Update("manager_id", users[2].ID).Error; err != nil {
			return fmt.Errorf("更新部门管理员失败: %w", err)
		}

		return m.seedMoreData(tx, users, departments)
	})
}

// seedMoreData 插入更多初始数据
func (m *MigrationManager) seedMoreData(tx *gorm.DB, users []models.User, departments []models.Department) error {
	// 3. 创建角色
	roles := []models.Role{
		{
			BaseModel:   models.BaseModel{ID: uuid.New(), CreatedBy: &users[0].ID},
			RoleName:    "系统管理员",
			RoleCode:    "SYS_ADMIN",
			Description: stringPtr("拥有系统所有权限"),
		},
		{
			BaseModel:   models.BaseModel{ID: uuid.New(), CreatedBy: &users[0].ID},
			RoleName:    "普通员工",
			RoleCode:    "EMPLOYEE",
			Description: stringPtr("基础报销、申请权限"),
		},
		{
			BaseModel:   models.BaseModel{ID: uuid.New(), CreatedBy: &users[0].ID},
			RoleName:    "财务审批人",
			RoleCode:    "FINANCE_APPROVER",
			Description: stringPtr("负责财务相关单据的审批"),
		},
	}

	for _, role := range roles {
		if err := tx.Create(&role).Error; err != nil {
			return fmt.Errorf("创建角色失败: %w", err)
		}
	}
	log.Printf("✓ 已创建 %d 个角色", len(roles))

	// 4. 分配用户角色
	userRoles := []models.UserRole{
		{UserID: users[0].ID.String(), RoleID: roles[0].ID.String()}, // 系统管理员
		{UserID: users[1].ID.String(), RoleID: roles[1].ID.String()}, // 张三 - 普通员工
		{UserID: users[2].ID.String(), RoleID: roles[1].ID.String()}, // 李四 - 普通员工
		{UserID: users[2].ID.String(), RoleID: roles[2].ID.String()}, // 李四 - 财务审批人
		{UserID: users[3].ID.String(), RoleID: roles[1].ID.String()}, // 王五 - 普通员工
	}

	for _, userRole := range userRoles {
		if err := tx.Create(&userRole).Error; err != nil {
			return fmt.Errorf("分配用户角色失败: %w", err)
		}
	}
	log.Printf("✓ 已分配 %d 个用户角色", len(userRoles))

	// 5. 创建预算方案
	budgetScheme := models.BudgetScheme{
		BaseModel:   models.BaseModel{ID: uuid.New(), CreatedBy: &users[0].ID},
		Name:        "2025年度预算方案",
		Year:        2025,
		ControlRule: "FLEXIBLE",
		Status:      "ACTIVE",
	}
	if err := tx.Create(&budgetScheme).Error; err != nil {
		return fmt.Errorf("创建预算方案失败: %w", err)
	}
	log.Println("✓ 已创建预算方案")

	// 6. 创建预算科目
	budgetSubject := models.BudgetSubject{
		BaseModel:             models.BaseModel{ID: uuid.New(), CreatedBy: &users[0].ID},
		Name:                  "差旅费",
		Code:                  "660101",
		Type:                  "EXPENDITURE",
		AccountingSubjectCode: stringPtr("660101"),
	}
	if err := tx.Create(&budgetSubject).Error; err != nil {
		return fmt.Errorf("创建预算科目失败: %w", err)
	}
	log.Println("✓ 已创建预算科目")

	// 7. 创建预算明细项
	budgetItem := models.BudgetItem{
		BaseModel:    models.BaseModel{ID: uuid.New(), CreatedBy: &users[2].ID},
		SchemeID:     budgetScheme.ID,
		DepartmentID: departments[1].ID, // 信息科
		SubjectID:    budgetSubject.ID,
		TotalAmount:  decimal.NewFromFloat(50000.00),
		UsedAmount:   decimal.NewFromFloat(0.00),
		FrozenAmount: decimal.NewFromFloat(205.50),
	}
	if err := tx.Create(&budgetItem).Error; err != nil {
		return fmt.Errorf("创建预算明细项失败: %w", err)
	}
	log.Println("✓ 已创建预算明细项")

	// 8. 创建供应商
	contactInfo := models.JSONMap{
		"contact_person": "张经理",
		"contact_phone":  "***********",
		"contact_email":  "<EMAIL>",
		"address":        "上海市浦东新区张江高科技园区",
		"bank_account":   "6228480012345678901",
		"bank_name":      "中国银行上海分行",
		"tax_number":     "91310115MA1H88888A",
		"type":           "goods",
	}

	suppliers := []models.Supplier{
		{
			BaseModel:   models.BaseModel{ID: uuid.New(), CreatedBy: &users[0].ID},
			Name:        "某某医疗器械有限公司",
			CreditCode:  stringPtr("91310115MA1H88888A"),
			Status:      1,
			ContactInfo: &contactInfo,
		},
		{
			BaseModel:  models.BaseModel{ID: uuid.New(), CreatedBy: &users[0].ID},
			Name:       "北京科技服务有限公司",
			CreditCode: stringPtr("91110108MA1H99999B"),
			Status:     1,
			ContactInfo: &models.JSONMap{
				"contact_person": "李总",
				"contact_phone":  "***********",
				"contact_email":  "<EMAIL>",
				"address":        "北京市海淀区中关村软件园",
				"type":           "service",
			},
		},
	}

	for _, supplier := range suppliers {
		if err := tx.Create(&supplier).Error; err != nil {
			return fmt.Errorf("创建供应商失败: %w", err)
		}
	}
	log.Printf("✓ 已创建 %d 个供应商", len(suppliers))

	// 9. 创建合同
	contracts := []models.Contract{
		{
			BaseModel:        models.BaseModel{ID: uuid.New(), CreatedBy: &users[2].ID},
			ContractCode:     "HT2025001",
			ContractName:     "2025年度心电监护仪采购合同",
			ContractType:     "采购合同",
			CounterpartyID:   &suppliers[0].ID,
			CounterpartyName: "某某医疗器械有限公司",
			TotalAmount:      decimal.NewFromFloat(150000.00),
			Status:           "ACTIVE",
		},
		{
			BaseModel:        models.BaseModel{ID: uuid.New(), CreatedBy: &users[2].ID},
			ContractCode:     "HT2025002",
			ContractName:     "信息系统维护服务合同",
			ContractType:     "服务合同",
			CounterpartyID:   &suppliers[1].ID,
			CounterpartyName: "北京科技服务有限公司",
			TotalAmount:      decimal.NewFromFloat(80000.00),
			Status:           "ACTIVE",
		},
		{
			BaseModel:        models.BaseModel{ID: uuid.New(), CreatedBy: &users[1].ID},
			ContractCode:     "HT2025003",
			ContractName:     "办公设备采购合同",
			ContractType:     "采购合同",
			CounterpartyID:   &suppliers[0].ID,
			CounterpartyName: "某某医疗器械有限公司",
			TotalAmount:      decimal.NewFromFloat(50000.00),
			Status:           "DRAFT",
		},
	}

	for _, contract := range contracts {
		if err := tx.Create(&contract).Error; err != nil {
			return fmt.Errorf("创建合同失败: %w", err)
		}
	}
	log.Printf("✓ 已创建 %d 个合同", len(contracts))

	// 10. 创建合同付款计划
	paymentSchedules := []models.ContractPaymentSchedule{
		// 第一个合同的付款计划
		{
			ID:         uuid.New(),
			ContractID: contracts[0].ID,
			PhaseName:  stringPtr("首付款"),
			Amount:     decimal.NewFromFloat(75000.00),
			Status:     "PENDING",
			CreatedAt:  time.Now(),
			UpdatedAt:  time.Now(),
		},
		{
			ID:         uuid.New(),
			ContractID: contracts[0].ID,
			PhaseName:  stringPtr("验收款"),
			Amount:     decimal.NewFromFloat(75000.00),
			Status:     "PENDING",
			CreatedAt:  time.Now(),
			UpdatedAt:  time.Now(),
		},
		// 第二个合同的付款计划
		{
			ID:         uuid.New(),
			ContractID: contracts[1].ID,
			PhaseName:  stringPtr("首期服务费"),
			Amount:     decimal.NewFromFloat(40000.00),
			Status:     "PAID",
			CreatedAt:  time.Now(),
			UpdatedAt:  time.Now(),
		},
		{
			ID:         uuid.New(),
			ContractID: contracts[1].ID,
			PhaseName:  stringPtr("年度服务费"),
			Amount:     decimal.NewFromFloat(40000.00),
			Status:     "PENDING",
			CreatedAt:  time.Now(),
			UpdatedAt:  time.Now(),
		},
		// 第三个合同的付款计划
		{
			ID:         uuid.New(),
			ContractID: contracts[2].ID,
			PhaseName:  stringPtr("全款"),
			Amount:     decimal.NewFromFloat(50000.00),
			Status:     "PENDING",
			CreatedAt:  time.Now(),
			UpdatedAt:  time.Now(),
		},
	}

	for _, schedule := range paymentSchedules {
		if err := tx.Create(&schedule).Error; err != nil {
			return fmt.Errorf("创建付款计划失败: %w", err)
		}
	}
	log.Printf("✓ 已创建 %d 个付款计划", len(paymentSchedules))

	// 11. 创建资产分类
	assetCategories := []models.AssetCategory{
		{
			BaseModel:          models.BaseModel{ID: uuid.New()},
			Name:               "电子设备",
			Code:               "ELECTRONIC",
			DepreciationMethod: stringPtr("straight_line"),
			DepreciationYears:  intPtr(5),
			Description:        stringPtr("电子设备类资产，包括计算机、打印机等"),
		},
		{
			BaseModel:          models.BaseModel{ID: uuid.New()},
			Name:               "办公设备",
			Code:               "OFFICE",
			DepreciationMethod: stringPtr("straight_line"),
			DepreciationYears:  intPtr(8),
			Description:        stringPtr("办公设备类资产，包括办公桌椅、文件柜等"),
		},
		{
			BaseModel:          models.BaseModel{ID: uuid.New()},
			Name:               "医疗设备",
			Code:               "MEDICAL",
			DepreciationMethod: stringPtr("straight_line"),
			DepreciationYears:  intPtr(10),
			Description:        stringPtr("医疗设备类资产，包括各种医疗器械"),
		},
		{
			BaseModel:          models.BaseModel{ID: uuid.New()},
			Name:               "车辆",
			Code:               "VEHICLE",
			DepreciationMethod: stringPtr("declining_balance"),
			DepreciationYears:  intPtr(8),
			Description:        stringPtr("车辆类资产"),
		},
		{
			BaseModel:          models.BaseModel{ID: uuid.New()},
			Name:               "家具",
			Code:               "FURNITURE",
			DepreciationMethod: stringPtr("straight_line"),
			DepreciationYears:  intPtr(15),
			Description:        stringPtr("家具类资产"),
		},
	}

	for _, category := range assetCategories {
		if err := tx.Create(&category).Error; err != nil {
			return fmt.Errorf("创建资产分类失败: %w", err)
		}
	}
	log.Printf("✓ 已创建 %d 个资产分类", len(assetCategories))

	// 12. 创建资产
	purchaseDate := time.Date(2024, 6, 15, 0, 0, 0, 0, time.UTC)
	warrantyDate := time.Date(2027, 6, 15, 0, 0, 0, 0, time.UTC)

	assets := []models.Asset{
		{
			BaseModel:               models.BaseModel{ID: uuid.New(), CreatedBy: &users[1].ID},
			AssetCode:               "ZC2024001",
			AssetName:               "心电监护仪",
			CategoryID:              assetCategories[2].ID, // 医疗设备
			Specification:           stringPtr("12导联心电监护仪，支持多参数监测"),
			Model:                   stringPtr("ECG-12A"),
			Brand:                   stringPtr("飞利浦"),
			SupplierID:              &suppliers[0].ID,
			SourceType:              stringPtr("PURCHASE"),
			PurchaseContractID:      &contracts[0].ID,
			PurchaseDate:            &purchaseDate,
			PurchasePrice:           decimalPtr(decimal.NewFromFloat(45000.00)),
			OriginalValue:           decimalPtr(decimal.NewFromFloat(45000.00)),
			CurrentValue:            decimalPtr(decimal.NewFromFloat(40500.00)),
			AccumulatedDepreciation: decimalPtr(decimal.NewFromFloat(4500.00)),
			DepreciationRate:        decimalPtr(decimal.NewFromFloat(0.1000)),
			UsefulLife:              intPtr(10),
			Status:                  "IN_USE",
			OwnerDeptID:             departments[3].ID, // 心血管内科
			CustodianID:             &users[3].ID,      // 王五
			Location:                stringPtr("心血管内科一病区"),
			WarrantyDate:            &warrantyDate,
			MaintenanceCycle:        intPtr(6), // 6个月
			Remark:                  stringPtr("主要用于心血管疾病患者监护"),
		},
		{
			BaseModel:               models.BaseModel{ID: uuid.New(), CreatedBy: &users[1].ID},
			AssetCode:               "ZC2024002",
			AssetName:               "联想台式电脑",
			CategoryID:              assetCategories[0].ID, // 电子设备
			Specification:           stringPtr("Intel i5-12400, 16GB内存, 512GB SSD"),
			Model:                   stringPtr("ThinkCentre M720q"),
			Brand:                   stringPtr("联想"),
			SupplierID:              &suppliers[0].ID,
			SourceType:              stringPtr("PURCHASE"),
			PurchaseDate:            &purchaseDate,
			PurchasePrice:           decimalPtr(decimal.NewFromFloat(5500.00)),
			OriginalValue:           decimalPtr(decimal.NewFromFloat(5500.00)),
			CurrentValue:            decimalPtr(decimal.NewFromFloat(4400.00)),
			AccumulatedDepreciation: decimalPtr(decimal.NewFromFloat(1100.00)),
			DepreciationRate:        decimalPtr(decimal.NewFromFloat(0.2000)),
			UsefulLife:              intPtr(5),
			Status:                  "IN_USE",
			OwnerDeptID:             departments[1].ID, // 信息科
			CustodianID:             &users[1].ID,      // 张三
			Location:                stringPtr("信息科办公室"),
			WarrantyDate:            &warrantyDate,
			MaintenanceCycle:        intPtr(12), // 12个月
			Remark:                  stringPtr("用于日常办公和系统维护"),
		},
		{
			BaseModel:               models.BaseModel{ID: uuid.New(), CreatedBy: &users[0].ID},
			AssetCode:               "ZC2024003",
			AssetName:               "办公桌椅套装",
			CategoryID:              assetCategories[1].ID, // 办公设备
			Specification:           stringPtr("实木办公桌1.6m + 人体工学椅"),
			Model:                   stringPtr("OD-160"),
			Brand:                   stringPtr("震旦"),
			SupplierID:              &suppliers[0].ID,
			SourceType:              stringPtr("PURCHASE"),
			PurchaseDate:            &purchaseDate,
			PurchasePrice:           decimalPtr(decimal.NewFromFloat(3200.00)),
			OriginalValue:           decimalPtr(decimal.NewFromFloat(3200.00)),
			CurrentValue:            decimalPtr(decimal.NewFromFloat(2800.00)),
			AccumulatedDepreciation: decimalPtr(decimal.NewFromFloat(400.00)),
			DepreciationRate:        decimalPtr(decimal.NewFromFloat(0.1250)),
			UsefulLife:              intPtr(8),
			Status:                  "IN_USE",
			OwnerDeptID:             departments[2].ID, // 财务科
			CustodianID:             &users[2].ID,      // 李四
			Location:                stringPtr("财务科办公室"),
			MaintenanceCycle:        intPtr(24), // 24个月
			Remark:                  stringPtr("财务科主任办公桌椅"),
		},
	}

	for _, asset := range assets {
		if err := tx.Create(&asset).Error; err != nil {
			return fmt.Errorf("创建资产失败: %w", err)
		}
	}
	log.Printf("✓ 已创建 %d 个资产", len(assets))

	log.Println("✓ 初始数据插入完成")
	return nil
}

// 辅助函数
func stringPtr(s string) *string {
	return &s
}

func int64Ptr(i int64) *int64 {
	return &i
}

func intPtr(i int) *int {
	return &i
}

func decimalPtr(d decimal.Decimal) *decimal.Decimal {
	return &d
}

func boolPtr(b bool) *bool {
	return &b
}

// updateDepartmentFields 更新现有部门的字段
func (m *MigrationManager) updateDepartmentFields() error {
	log.Println("正在更新部门字段...")

	// 更新现有部门数据
	departmentUpdates := []struct {
		Name         string
		DeptType     string
		Description  string
		ContactPhone string
		ContactEmail string
	}{
		{
			Name:         "院办",
			DeptType:     "administrative",
			Description:  "负责医院行政管理工作",
			ContactPhone: "010-12345678",
			ContactEmail: "<EMAIL>",
		},
		{
			Name:         "信息科",
			DeptType:     "administrative",
			Description:  "负责医院信息化建设和维护",
			ContactPhone: "010-12345679",
			ContactEmail: "<EMAIL>",
		},
		{
			Name:         "财务科",
			DeptType:     "administrative",
			Description:  "负责医院财务管理和预算控制",
			ContactPhone: "010-12345680",
			ContactEmail: "<EMAIL>",
		},
		{
			Name:         "心血管内科",
			DeptType:     "clinical",
			Description:  "负责心血管疾病的诊断和治疗",
			ContactPhone: "010-12345681",
			ContactEmail: "<EMAIL>",
		},
	}

	for _, update := range departmentUpdates {
		result := m.db.Model(&models.Department{}).
			Where("name = ?", update.Name).
			Updates(map[string]interface{}{
				"dept_type":     update.DeptType,
				"description":   update.Description,
				"contact_phone": update.ContactPhone,
				"contact_email": update.ContactEmail,
			})

		if result.Error != nil {
			log.Printf("更新部门 %s 失败: %v", update.Name, result.Error)
		} else if result.RowsAffected > 0 {
			log.Printf("✓ 已更新部门: %s", update.Name)
		}
	}

	log.Println("部门字段更新完成")
	return nil
}

// createSubDepartments 创建子部门
func (m *MigrationManager) createSubDepartments() error {
	log.Println("正在创建子部门...")

	// 创建子部门用于测试树形结构
	var parentDept models.Department
	if err := m.db.Where("name = ?", "心血管内科").First(&parentDept).Error; err != nil {
		return fmt.Errorf("找不到心血管内科部门: %w", err)
	}

	subDepts := []models.Department{
		{
			BaseModel:    models.BaseModel{ID: uuid.New()},
			ParentID:     &parentDept.ID,
			Name:         "心血管内科一病区",
			Code:         stringPtr("D_CARDIO_1"),
			DeptType:     stringPtr("clinical"),
			Description:  stringPtr("心血管内科第一病区"),
			ContactPhone: stringPtr("010-12345682"),
			ContactEmail: stringPtr("<EMAIL>"),
			IsActive:     boolPtr(true),
		},
		{
			BaseModel:    models.BaseModel{ID: uuid.New()},
			ParentID:     &parentDept.ID,
			Name:         "心血管内科二病区",
			Code:         stringPtr("D_CARDIO_2"),
			DeptType:     stringPtr("clinical"),
			Description:  stringPtr("心血管内科第二病区"),
			ContactPhone: stringPtr("010-12345683"),
			ContactEmail: stringPtr("<EMAIL>"),
			IsActive:     boolPtr(true),
		},
	}

	for _, subDept := range subDepts {
		// 检查是否已存在
		var existing models.Department
		if err := m.db.Where("name = ?", subDept.Name).First(&existing).Error; err != nil {
			// 不存在，创建新的
			if err := m.db.Create(&subDept).Error; err != nil {
				log.Printf("创建子部门失败 %s: %v", subDept.Name, err)
				return fmt.Errorf("创建子部门失败: %w", err)
			} else {
				log.Printf("✓ 已创建子部门: %s", subDept.Name)
			}
		} else {
			log.Printf("子部门 %s 已存在", subDept.Name)
		}
	}

	log.Println("子部门创建完成")
	return nil
}
