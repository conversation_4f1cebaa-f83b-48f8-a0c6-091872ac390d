package dto

import (
	"github.com/google/uuid"
)

// CreateApprovalFlowRequest 创建审批流请求
type CreateApprovalFlowRequest struct {
	BusinessType   string                 `json:"business_type" validate:"required,max=50"`
	BusinessID     uuid.UUID              `json:"business_id" validate:"required,uuid"`
	Title          string                 `json:"title" validate:"required,max=200"`
	Content        *string                `json:"content,omitempty" validate:"omitempty,max=2000"`
	ApprovalNodes  []ApprovalNodeRequest  `json:"approval_nodes" validate:"required,dive"`
	Attachments    []uuid.UUID            `json:"attachments,omitempty" validate:"omitempty,dive,uuid"`
}

// ApprovalNodeRequest 审批节点请求
type ApprovalNodeRequest struct {
	NodeOrder    int         `json:"node_order" validate:"required,min=1"`
	NodeName     string      `json:"node_name" validate:"required,max=100"`
	ApproverType string      `json:"approver_type" validate:"required,oneof=user role department"`
	ApproverID   uuid.UUID   `json:"approver_id" validate:"required,uuid"`
	IsRequired   bool        `json:"is_required"`
	Description  *string     `json:"description,omitempty" validate:"omitempty,max=500"`
}

// ApprovalFlowResponse 审批流响应
type ApprovalFlowResponse struct {
	ID             uuid.UUID               `json:"id"`
	BusinessType   string                  `json:"business_type"`
	BusinessID     uuid.UUID               `json:"business_id"`
	Title          string                  `json:"title"`
	Content        *string                 `json:"content,omitempty"`
	Status         string                  `json:"status"`
	CurrentNodeID  *uuid.UUID              `json:"current_node_id,omitempty"`
	CreatedAt      string                  `json:"created_at"`
	UpdatedAt      string                  `json:"updated_at"`
	CreatedBy      uuid.UUID               `json:"created_by"`
	Creator        *UserSimpleResponse     `json:"creator,omitempty"`
	ApprovalNodes  []ApprovalNodeResponse  `json:"approval_nodes,omitempty"`
	Attachments    []FileSimpleResponse    `json:"attachments,omitempty"`
}

// ApprovalNodeResponse 审批节点响应
type ApprovalNodeResponse struct {
	ID           uuid.UUID            `json:"id"`
	FlowID       uuid.UUID            `json:"flow_id"`
	NodeOrder    int                  `json:"node_order"`
	NodeName     string               `json:"node_name"`
	ApproverType string               `json:"approver_type"`
	ApproverID   uuid.UUID            `json:"approver_id"`
	IsRequired   bool                 `json:"is_required"`
	Status       string               `json:"status"`
	ApprovedAt   *string              `json:"approved_at,omitempty"`
	ApprovedBy   *uuid.UUID           `json:"approved_by,omitempty"`
	Comments     *string              `json:"comments,omitempty"`
	Description  *string              `json:"description,omitempty"`
	Approver     *UserSimpleResponse  `json:"approver,omitempty"`
}

// ApprovalActionRequest 审批操作请求
type ApprovalActionRequest struct {
	Action   string  `json:"action" validate:"required,oneof=approve reject transfer"`
	Comments *string `json:"comments,omitempty" validate:"omitempty,max=1000"`
	TransferTo *uuid.UUID `json:"transfer_to,omitempty" validate:"omitempty,uuid"`
}

// TodoApprovalRequest 待办审批列表请求
type TodoApprovalRequest struct {
	Page         int    `form:"page" validate:"min=1"`
	PageSize     int    `form:"page_size" validate:"min=1,max=100"`
	BusinessType string `form:"business_type"`
	Status       string `form:"status" validate:"omitempty,oneof=pending approved rejected"`
	Keyword      string `form:"keyword"`
}

// TodoApprovalResponse 待办审批响应
type TodoApprovalResponse struct {
	ID            uuid.UUID            `json:"id"`
	FlowID        uuid.UUID            `json:"flow_id"`
	NodeID        uuid.UUID            `json:"node_id"`
	BusinessType  string               `json:"business_type"`
	Title         string               `json:"title"`
	NodeName      string               `json:"node_name"`
	Status        string               `json:"status"`
	IsRequired    bool                 `json:"is_required"`
	CreatedAt     string               `json:"created_at"`
	Creator       *UserSimpleResponse  `json:"creator,omitempty"`
	Flow          *ApprovalFlowSimple  `json:"flow,omitempty"`
}

// ApprovalFlowSimple 审批流简单信息
type ApprovalFlowSimple struct {
	ID           uuid.UUID `json:"id"`
	BusinessType string    `json:"business_type"`
	BusinessID   uuid.UUID `json:"business_id"`
	Title        string    `json:"title"`
	Status       string    `json:"status"`
}

// ApprovalFlowListRequest 审批流列表请求
type ApprovalFlowListRequest struct {
	Page         int        `form:"page" validate:"min=1"`
	PageSize     int        `form:"page_size" validate:"min=1,max=100"`
	BusinessType string     `form:"business_type"`
	Status       string     `form:"status" validate:"omitempty,oneof=pending approved rejected cancelled"`
	CreatedBy    *uuid.UUID `form:"created_by" validate:"omitempty,uuid"`
	Keyword      string     `form:"keyword"`
	StartDate    string     `form:"start_date" validate:"omitempty,datetime=2006-01-02"`
	EndDate      string     `form:"end_date" validate:"omitempty,datetime=2006-01-02"`
}

// UpdateApprovalFlowStatusRequest 更新审批流状态请求
type UpdateApprovalFlowStatusRequest struct {
	Status string `json:"status" validate:"required,oneof=pending approved rejected cancelled"`
}

// FileSimpleResponse 文件简单响应（用于关联显示）
type FileSimpleResponse struct {
	ID       uuid.UUID `json:"id"`
	FileName string    `json:"file_name"`
	FileSize int64     `json:"file_size"`
	FileType string    `json:"file_type"`
}
