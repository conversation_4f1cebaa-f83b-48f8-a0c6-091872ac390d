#!/bin/bash

echo "开始修复nginx配置..."

# 1. 备份原配置
echo "备份原配置..."
sudo cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup.$(date +%Y%m%d_%H%M%S)

# 2. 检查并创建目录
echo "检查目录..."
sudo mkdir -p /var/www/hospital/dist
sudo mkdir -p /opt/hospital

# 3. 设置权限
echo "设置权限..."
sudo chown -R nginx:nginx /var/www/hospital/ 2>/dev/null || sudo chown -R www-data:www-data /var/www/hospital/
sudo chmod -R 755 /var/www/hospital/

# 4. 创建nginx配置文件
echo "创建nginx配置..."
sudo tee /etc/nginx/conf.d/hospital.conf > /dev/null <<'EOF'
server {
    listen 80;
    server_name localhost;

    # 前端静态文件
    location /hospital {
        alias /var/www/hospital/dist;
        index index.html;
        try_files $uri $uri/ /hospital/index.html;
    }
    
    # 根路径重定向
    location = / {
        return 301 /hospital/;
    }
    
    # API 代理到后端
    location /api/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    client_max_body_size 100M;
}
EOF

# 5. 禁用默认站点（如果存在）
if [ -f /etc/nginx/sites-enabled/default ]; then
    echo "禁用默认站点..."
    sudo rm /etc/nginx/sites-enabled/default
fi

# 6. 测试配置
echo "测试nginx配置..."
if sudo nginx -t; then
    echo "配置测试通过，重启nginx..."
    sudo systemctl restart nginx
    echo "nginx重启完成"
    
    echo ""
    echo "配置完成！请检查："
    echo "1. 文件是否存在: ls -la /var/www/hospital/dist/index.html"
    echo "2. 访问测试: http://你的服务器IP/hospital/"
    echo "3. 查看日志: sudo tail -f /var/log/nginx/error.log"
else
    echo "nginx配置测试失败，请检查配置文件"
    exit 1
fi
