# 医院管理系统部署指南 - /hospital 路径访问

## 1. 本地重新打包

由于修改了前端配置支持 `/hospital/` 路径，需要重新打包：

```bash
# 前端重新打包
cd frontend
npm run build

# 后端编译（如果需要）
cd ../backend
set GOOS=linux
set GOARCH=amd64
go build -o hospital-management main.go
```

## 2. 上传文件到服务器

1. 将 `frontend/dist/` 目录内容上传到服务器 `/var/www/hospital/dist/`
2. 将 `backend/hospital-management` 上传到服务器 `/opt/hospital/`
3. 将 `config.yaml` 上传到服务器 `/opt/hospital/`

**确保目录结构**：
```
/var/www/hospital/dist/
├── index.html
├── assets/
└── ...

/opt/hospital/
├── hospital-management
└── config.yaml
```

## 3. nginx配置

使用以下配置（您当前的配置是正确的）：

```nginx
server {
    listen 80;
    server_name localhost;

    # 前端静态文件
    location /hospital {
        alias /var/www/hospital/dist;
        index index.html;
        try_files $uri $uri/ /hospital/index.html;
    }
    
    # 根路径重定向到 /hospital/
    location = / {
        return 301 /hospital/;
    }
    
    # API 代理到后端
    location /api/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 文件上传大小限制
    client_max_body_size 100M;
}
```

## 4. 排查步骤

如果仍然出现404错误，请按以下步骤排查：

### 4.1 检查文件是否存在
```bash
# 检查前端文件
ls -la /var/www/hospital/dist/
ls -la /var/www/hospital/dist/index.html

# 检查后端文件
ls -la /opt/hospital/
ls -la /opt/hospital/hospital-management
```

### 4.2 检查文件权限
```bash
# 设置正确的权限
sudo chown -R nginx:nginx /var/www/hospital/
sudo chmod -R 755 /var/www/hospital/

# 或者使用 www-data 用户（取决于您的系统）
sudo chown -R www-data:www-data /var/www/hospital/
```

### 4.3 检查nginx配置
```bash
# 测试nginx配置
sudo nginx -t

# 重启nginx
sudo systemctl restart nginx

# 查看nginx错误日志
sudo tail -f /var/log/nginx/error.log
```

### 4.4 检查SELinux（如果是RHEL/CentOS）
```bash
# 检查SELinux状态
getenforce

# 如果是Enforcing，可能需要设置SELinux上下文
sudo setsebool -P httpd_can_network_connect 1
sudo restorecon -R /var/www/hospital/
```

## 5. 启动后端服务

```bash
# 修改配置文件
sudo nano /opt/hospital/config.yaml

# 给执行权限
sudo chmod +x /opt/hospital/hospital-management

# 启动后端
cd /opt/hospital
sudo ./hospital-management
```

## 6. 访问系统

访问地址：`http://你的服务器IP/hospital/`

## 7. 常见问题

### 问题1：404错误
- 检查文件路径是否正确
- 检查nginx配置是否正确
- 检查文件权限

### 问题2：静态资源加载失败
- 确认前端已重新打包
- 检查nginx配置中的alias路径

### 问题3：API请求失败
- 确认后端服务已启动
- 检查防火墙是否开放8080端口
- 检查数据库连接配置
