package controllers

import (
	"hospital-management/internal/services"
	"hospital-management/pkg/logger"
	customValidator "hospital-management/pkg/validator"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

type CommonController struct {
	commonService *services.CommonService
	validator     *validator.Validate
}

func NewCommonController(commonService *services.CommonService) *CommonController {
	return &CommonController{
		commonService: commonService,
		validator:     customValidator.NewValidator(),
	}
}

// ===== 数据字典相关 =====

// GetDictionaries 获取数据字典
// @Summary 获取数据字典
// @Description 获取系统中所有的数据字典项
// @Tags 通用功能
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "success"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /dictionaries [get]
func (c *CommonController) GetDictionaries(ctx *gin.Context) {
	// 调用服务
	dictionaries, err := c.commonService.GetDictionaries()
	if err != nil {
		logger.Error("Failed to get dictionaries:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取数据字典失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    dictionaries,
	})
}

// ===== 统计报表相关 =====

// GetDashboardStatistics 获取仪表盘统计数据
// @Summary 获取仪表盘统计数据
// @Description 获取系统各模块的统计数据，用于仪表盘展示
// @Tags 通用功能
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "success"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /dashboard/statistics [get]
func (c *CommonController) GetDashboardStatistics(ctx *gin.Context) {
	// 调用服务
	statistics, err := c.commonService.GetDashboardStatistics()
	if err != nil {
		logger.Error("Failed to get dashboard statistics:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取仪表盘统计数据失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    statistics,
	})
}

// ===== 系统信息相关 =====

// GetSystemInfo 获取系统信息
// @Summary 获取系统信息
// @Description 获取系统版本、环境等基本信息
// @Tags 通用功能
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "success"
// @Security BearerAuth
// @Router /system/info [get]
func (c *CommonController) GetSystemInfo(ctx *gin.Context) {
	systemInfo := map[string]interface{}{
		"system_name":    "医院内部控制与运营管理系统",
		"system_version": "v1.0.0",
		"build_time":     "2025-01-30",
		"environment":    "development", // 可以从配置文件读取
		"database":       "PostgreSQL",
		"framework":      "Gin + GORM",
		"language":       "Go 1.21+",
		"features": []string{
			"全面预算管理",
			"支出控制管理",
			"采购管理",
			"合同管理",
			"资产管理",
			"审批流程",
			"权限管理",
			"统计报表",
		},
		"modules": map[string]interface{}{
			"budget": map[string]interface{}{
				"name":        "预算管理",
				"description": "预算方案、预算科目、预算明细管理",
				"status":      "已完成",
				"apis":        19,
			},
			"expense": map[string]interface{}{
				"name":        "支出控制",
				"description": "事前申请、费用报销、付款管理",
				"status":      "已完成",
				"apis":        13,
			},
			"procurement": map[string]interface{}{
				"name":        "采购管理",
				"description": "供应商管理、采购申请管理",
				"status":      "已完成",
				"apis":        9,
			},
			"contract": map[string]interface{}{
				"name":        "合同管理",
				"description": "合同管理、付款计划管理",
				"status":      "已完成",
				"apis":        10,
			},
			"asset": map[string]interface{}{
				"name":        "资产管理",
				"description": "资产分类、资产管理",
				"status":      "已完成",
				"apis":        9,
			},
		},
		"total_apis":      63, // 总API数量
		"completion_rate": "100%",
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    systemInfo,
	})
}

// ===== 健康检查相关 =====

// HealthCheck 健康检查
// @Summary 健康检查
// @Description 检查系统运行状态
// @Tags 通用功能
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "success"
// @Router /health [get]
func (c *CommonController) HealthCheck(ctx *gin.Context) {
	ctx.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": "2025-01-30T10:00:00Z",
		"services": map[string]string{
			"database": "connected",
			"redis":    "connected", // 如果使用Redis
			"storage":  "available",
		},
	})
}

// ===== 版本信息相关 =====

// GetVersion 获取版本信息
// @Summary 获取版本信息
// @Description 获取系统版本信息
// @Tags 通用功能
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "success"
// @Router /version [get]
func (c *CommonController) GetVersion(ctx *gin.Context) {
	versionInfo := map[string]interface{}{
		"version":     "v1.0.0",
		"build_time":  "2025-01-30T10:00:00Z",
		"git_commit":  "abc123def456", // 可以从构建时注入
		"go_version":  "go1.21.0",
		"environment": "development",
		"changelog": []map[string]interface{}{
			{
				"version": "v1.0.0",
				"date":    "2025-01-30",
				"changes": []string{
					"完成预算管理模块",
					"完成支出控制模块",
					"完成采购管理模块",
					"完成合同管理模块",
					"完成资产管理模块",
					"实现完整的API接口",
					"添加数据字典和统计功能",
				},
			},
		},
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    versionInfo,
	})
}
