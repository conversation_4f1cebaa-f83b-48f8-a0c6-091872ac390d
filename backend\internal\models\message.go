package models

import (
	"time"

	"github.com/google/uuid"
)

// Message 消息表 - 对应 tbl_messages
type Message struct {
	BaseModel
	MessageType  string     `json:"message_type" gorm:"type:varchar(20);not null" validate:"required,oneof=notification announcement todo reminder"`
	Title        string     `json:"title" gorm:"type:varchar(200);not null" validate:"required,max=200"`
	Content      string     `json:"content" gorm:"type:text;not null" validate:"required,max=2000"`
	Priority     string     `json:"priority" gorm:"type:varchar(10);not null;default:'normal'" validate:"required,oneof=low normal high urgent"`
	Status       string     `json:"status" gorm:"type:varchar(10);not null;default:'draft'" validate:"required,oneof=draft sent"`
	BusinessID   *uuid.UUID `json:"business_id,omitempty" gorm:"type:uuid"`
	BusinessType *string    `json:"business_type,omitempty" gorm:"type:varchar(50)"`
	ScheduledAt  *time.Time `json:"scheduled_at,omitempty" gorm:"type:timestamptz"`
	SentAt       *time.Time `json:"sent_at,omitempty" gorm:"type:timestamptz"`

	// 关联关系
	Sender    *User             `json:"sender,omitempty" gorm:"foreignKey:CreatedBy"`
	Receivers []MessageReceiver `json:"receivers,omitempty" gorm:"foreignKey:MessageID"`
}

// TableName 指定表名
func (Message) TableName() string {
	return "tbl_messages"
}

// MessageReceiver 消息接收者表 - 对应 tbl_message_receivers
type MessageReceiver struct {
	ID         uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	MessageID  uuid.UUID  `json:"message_id" gorm:"type:uuid;not null"`
	ReceiverID uuid.UUID  `json:"receiver_id" gorm:"type:uuid;not null"`
	Status     string     `json:"status" gorm:"type:varchar(10);not null;default:'unread'" validate:"required,oneof=unread read"`
	ReadAt     *time.Time `json:"read_at,omitempty" gorm:"type:timestamptz"`
	CreatedAt  time.Time  `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt  time.Time  `json:"updated_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	CreatedBy  *uuid.UUID `json:"created_by,omitempty" gorm:"type:uuid"`
	UpdatedBy  *uuid.UUID `json:"updated_by,omitempty" gorm:"type:uuid"`

	// 关联关系
	Message  *Message `json:"message,omitempty" gorm:"foreignKey:MessageID"`
	Receiver *User    `json:"receiver,omitempty" gorm:"foreignKey:ReceiverID"`
}

// TableName 指定表名
func (MessageReceiver) TableName() string {
	return "tbl_message_receivers"
}
