package services

import (
	"errors"
	"fmt"
	"hospital-management/internal/dto"
	"hospital-management/internal/models"
	"hospital-management/pkg/logger"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type PaymentService struct {
	db *gorm.DB
}

func NewPaymentService(db *gorm.DB) *PaymentService {
	return &PaymentService{db: db}
}

// GetPayments 获取付款单列表
func (s *PaymentService) GetPayments(req *dto.PaymentListRequest) ([]dto.PaymentResponse, int64, error) {
	var payments []models.Payment
	var total int64

	query := s.db.Model(&models.Payment{})

	// 搜索条件
	if req.Keyword != "" {
		query = query.Where("payment_code ILIKE ? OR payee_name ILIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.SourceType != "" {
		query = query.Where("source_type = ?", req.SourceType)
	}
	if req.SourceID != nil {
		query = query.Where("source_id = ?", *req.SourceID)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count payments:", err)
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&payments).Error; err != nil {
		logger.Error("Failed to get payments:", err)
		return nil, 0, err
	}

	// 转换响应
	responses := make([]dto.PaymentResponse, len(payments))
	for i, payment := range payments {
		responses[i] = s.convertPaymentToResponse(payment)
	}

	return responses, total, nil
}

// CreatePayment 创建付款单
func (s *PaymentService) CreatePayment(req *dto.CreatePaymentRequest, userID uuid.UUID) (*dto.PaymentResponse, error) {
	// 验证来源单据是否存在
	if err := s.validateSourceDocument(req.SourceID, req.SourceType); err != nil {
		return nil, err
	}

	// 生成付款单号
	paymentCode, err := s.generatePaymentCode()
	if err != nil {
		return nil, err
	}

	payment := models.Payment{
		PaymentCode:  paymentCode,
		SourceID:     req.SourceID,
		SourceType:   req.SourceType,
		PayeeName:    req.PayeeName,
		PayeeAccount: req.PayeeAccount,
		PayeeBank:    req.PayeeBank,
		Amount:       req.Amount,
		Status:       "PENDING",
	}
	payment.CreatedBy = &userID

	if err := s.db.Create(&payment).Error; err != nil {
		logger.Error("Failed to create payment:", err)
		return nil, err
	}

	response := s.convertPaymentToResponse(payment)
	return &response, nil
}

// UpdatePaymentStatus 更新付款状态
func (s *PaymentService) UpdatePaymentStatus(id uuid.UUID, req *dto.UpdatePaymentStatusRequest, userID uuid.UUID) (*dto.PaymentResponse, error) {
	var payment models.Payment
	if err := s.db.First(&payment, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("付款单不存在")
		}
		return nil, err
	}

	// 更新状态
	payment.Status = req.Status
	if req.TransactionID != nil {
		payment.TransactionID = req.TransactionID
	}
	
	// 如果状态为已支付，设置支付时间
	if req.Status == "PAID" {
		now := time.Now()
		payment.PaidAt = &now
	}
	
	payment.UpdatedBy = &userID

	if err := s.db.Save(&payment).Error; err != nil {
		logger.Error("Failed to update payment status:", err)
		return nil, err
	}

	// 如果付款成功，需要更新相关业务单据状态和预算消费
	if req.Status == "PAID" {
		if err := s.processPaymentSuccess(payment); err != nil {
			logger.Error("Failed to process payment success:", err)
			// 这里不返回错误，因为付款状态已经更新成功
		}
	}

	response := s.convertPaymentToResponse(payment)
	return &response, nil
}

// BatchPayment 批量付款
func (s *PaymentService) BatchPayment(req *dto.BatchPaymentRequest, userID uuid.UUID) error {
	// 开启事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, paymentID := range req.PaymentIDs {
		var payment models.Payment
		if err := tx.First(&payment, paymentID).Error; err != nil {
			tx.Rollback()
			return errors.New("付款单不存在")
		}

		if payment.Status != "PENDING" {
			tx.Rollback()
			return errors.New("只能批量处理待付款状态的单据")
		}

		// 更新状态
		payment.Status = "PAID"
		now := time.Now()
		payment.PaidAt = &now
		payment.UpdatedBy = &userID

		if err := tx.Save(&payment).Error; err != nil {
			tx.Rollback()
			logger.Error("Failed to update payment status:", err)
			return err
		}

		// 处理付款成功后的业务逻辑
		if err := s.processPaymentSuccessWithTx(tx, payment); err != nil {
			tx.Rollback()
			logger.Error("Failed to process payment success:", err)
			return err
		}
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

// ===== 辅助方法 =====

// generatePaymentCode 生成付款单号
func (s *PaymentService) generatePaymentCode() (string, error) {
	now := time.Now()
	prefix := fmt.Sprintf("FK%04d%02d%02d", now.Year(), now.Month(), now.Day())
	
	var count int64
	if err := s.db.Model(&models.Payment{}).
		Where("payment_code LIKE ?", prefix+"%").
		Count(&count).Error; err != nil {
		return "", err
	}
	
	return fmt.Sprintf("%s%04d", prefix, count+1), nil
}

// validateSourceDocument 验证来源单据是否存在
func (s *PaymentService) validateSourceDocument(sourceID uuid.UUID, sourceType string) error {
	switch sourceType {
	case "EXPENSE":
		var expense models.ExpenseApplication
		if err := s.db.First(&expense, sourceID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return errors.New("报销申请不存在")
			}
			return err
		}
		if expense.Status != "APPROVED" {
			return errors.New("报销申请未审批通过")
		}
	case "CONTRACT_PAY":
		// TODO: 验证合同付款计划
		return errors.New("合同付款功能暂未实现")
	default:
		return errors.New("不支持的来源类型")
	}
	return nil
}

// processPaymentSuccess 处理付款成功后的业务逻辑
func (s *PaymentService) processPaymentSuccess(payment models.Payment) error {
	return s.processPaymentSuccessWithTx(s.db, payment)
}

// processPaymentSuccessWithTx 使用事务处理付款成功后的业务逻辑
func (s *PaymentService) processPaymentSuccessWithTx(tx *gorm.DB, payment models.Payment) error {
	switch payment.SourceType {
	case "EXPENSE":
		// 更新报销申请状态为已支付
		if err := tx.Model(&models.ExpenseApplication{}).
			Where("id = ?", payment.SourceID).
			Update("status", "PAID").Error; err != nil {
			return err
		}

		// 获取报销明细，消费预算额度
		var details []models.ExpenseDetail
		if err := tx.Where("application_id = ?", payment.SourceID).Find(&details).Error; err != nil {
			return err
		}

		for _, detail := range details {
			var budgetItem models.BudgetItem
			if err := tx.First(&budgetItem, detail.BudgetItemID).Error; err != nil {
				return err
			}

			// 从冻结额度转为已使用额度
			budgetItem.FrozenAmount = budgetItem.FrozenAmount.Sub(detail.Amount)
			budgetItem.UsedAmount = budgetItem.UsedAmount.Add(detail.Amount)

			if err := tx.Save(&budgetItem).Error; err != nil {
				return err
			}
		}

	case "CONTRACT_PAY":
		// TODO: 处理合同付款
		return errors.New("合同付款功能暂未实现")
	}

	return nil
}

// convertPaymentToResponse 转换付款单为响应格式
func (s *PaymentService) convertPaymentToResponse(payment models.Payment) dto.PaymentResponse {
	response := dto.PaymentResponse{
		ID:            payment.ID,
		PaymentCode:   payment.PaymentCode,
		SourceID:      payment.SourceID,
		SourceType:    payment.SourceType,
		PayeeName:     payment.PayeeName,
		PayeeAccount:  payment.PayeeAccount,
		PayeeBank:     payment.PayeeBank,
		Amount:        payment.Amount,
		Status:        payment.Status,
		TransactionID: payment.TransactionID,
		CreatedAt:     payment.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:     payment.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	if payment.PaidAt != nil {
		paidAtStr := payment.PaidAt.Format("2006-01-02 15:04:05")
		response.PaidAt = &paidAtStr
	}

	return response
}
