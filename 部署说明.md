# 医院管理系统部署说明

## 1. 本地打包

### 前端打包
双击运行 `build-frontend.bat` 或手动执行：
```bash
cd frontend
npm run build
```
打包后的文件在 `frontend/dist/` 目录

### 后端编译
双击运行 `build-backend.bat` 或手动执行：
```bash
cd backend
set GOOS=linux
set GOARCH=amd64
go build -o hospital-management main.go
go build -o migrate ./cmd/migrate
```

## 2. 上传文件到服务器

1. 将 `frontend/dist/` 目录内容上传到服务器 `/var/www/hospital/dist/`
2. 将 `backend/hospital-management` 上传到服务器 `/opt/hospital/`
3. 将 `config.yaml` 上传到服务器 `/opt/hospital/`

**注意**：确保目录结构正确：
- `/var/www/hospital/dist/index.html` （前端文件）
- `/opt/hospital/hospital-management` （后端程序）
- `/opt/hospital/config.yaml` （配置文件）

## 3. 配置nginx

### 方案一：根路径访问（推荐）
直接编辑 `/etc/nginx/nginx.conf`，在 http 块中添加：

```nginx
server {
    listen 80;
    server_name localhost;

    # 前端静态文件
    location / {
        root /var/www/hospital/dist;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # API 代理到后端
    location /api/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    client_max_body_size 100M;
}
```

访问地址：`http://你的服务器IP/`

### 方案二：子路径访问
如果您想要在 `/hospital` 路径下访问：

```nginx
server {
    listen 80;
    server_name localhost;

    # 前端静态文件
    location /hospital {
        alias /var/www/hospital/dist;
        index index.html;
        try_files $uri $uri/ /hospital/index.html;
    }

    # 根路径重定向
    location = / {
        return 301 /hospital/;
    }

    # API 代理到后端
    location /api/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    client_max_body_size 100M;
}
```

访问地址：`http://你的服务器IP/hospital/`

**重启nginx**：`systemctl restart nginx`

## 4. 修改配置文件

编辑 `/opt/hospital/config.yaml`，修改为您当前的数据库连接信息：

```yaml
server:
  port: 8080
  host: "0.0.0.0"

database:
  host: "您的数据库主机"
  port: 5432
  user: "您的数据库用户名"
  password: "您的数据库密码"
  dbname: "您的数据库名"
  sslmode: "disable"

jwt:
  secret: "your-jwt-secret-change-this"
  expire_hours: 24
```

## 5. 运行程序

```bash
# 给执行权限
chmod +x /opt/hospital/hospital-management

# 启动后端服务
cd /opt/hospital
./hospital-management
```

## 6. 访问系统

浏览器访问：`http://你的服务器IP/`

使用您现有的管理员账号登录即可。

## 7. 后台运行（可选）

如果想让程序在后台运行：
```bash
nohup ./hospital-management > hospital.log 2>&1 &
```

## 注意事项

- 确保服务器防火墙开放了80端口（nginx）和8080端口（后端API）
- 如果数据库在其他服务器上，请确保网络连通性
- 建议定期备份数据库数据
