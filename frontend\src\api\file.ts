import request from './request'

// 文件管理相关接口
export interface FileInfo {
  id: string
  file_name: string
  original_name: string
  file_size: number
  file_type: string
  file_path: string
  file_url: string
  business_type?: string
  business_id?: string
  description?: string
  is_public: boolean
  download_count: number
  created_at: string
  updated_at: string
  created_by: string
  uploader?: {
    id: string
    user_name: string
    job_title?: string
  }
}

export interface FileQuery {
  keyword?: string
  business_type?: string
  business_id?: string
  file_type?: string
  is_public?: boolean
  uploader_id?: string
  start_date?: string
  end_date?: string
  page?: number
  page_size?: number
}

// 获取文件列表
export const getFiles = (params: FileQuery): Promise<{
  list: FileInfo[]
  total: number
  page: number
  page_size: number
}> => {
  return request.get('/api/v1/files', { params })
}

// 获取文件详情
export const getFileDetail = (id: string): Promise<FileInfo> => {
  return request.get(`/api/v1/files/${id}`)
}

// 上传文件
export const uploadFile = (file: File, businessType?: string, businessId?: string): Promise<{
  id: string
  file_name: string
  original_name: string
  file_size: number
  file_type: string
  file_path: string
  file_url: string
  created_at: string
  created_by: string
}> => {
  const formData = new FormData()
  formData.append('file', file)
  if (businessType) {
    formData.append('business_type', businessType)
  }
  if (businessId) {
    formData.append('business_id', businessId)
  }

  return request.post('/api/v1/files/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 批量上传文件
export const batchUploadFiles = (files: File[], businessType?: string, businessId?: string): Promise<{
  success_files: Array<{
    id: string
    file_name: string
    file_path: string
    file_size: number
    mime_type: string
    url: string
  }>
  failed_files: Array<{
    file_name: string
    error: string
  }>
}> => {
  const formData = new FormData()
  files.forEach(file => {
    formData.append('files', file)
  })
  if (businessType) {
    formData.append('business_type', businessType)
  }
  if (businessId) {
    formData.append('business_id', businessId)
  }
  
  return request.post('/api/v1/files/batch-upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 删除文件
export const deleteFile = (id: string): Promise<void> => {
  return request.delete(`/api/v1/files/${id}`)
}

// 批量删除文件
export const batchDeleteFiles = (ids: string[]): Promise<void> => {
  return request.delete('/api/v1/files/batch', { data: { ids } })
}

// 下载文件
export const downloadFile = (id: string): Promise<Blob> => {
  return request.get(`/api/v1/files/${id}/download`, {
    responseType: 'blob'
  })
}

// 获取文件预览URL
export const getFilePreviewUrl = (id: string): string => {
  return `/api/v1/files/${id}/preview`
}

// 获取文件统计
export const getFileStatistics = (): Promise<{
  total_files: number
  total_size: number
  storage_usage: {
    local: number
    cloud: number
  }
  file_types: Array<{
    mime_type: string
    count: number
    size: number
  }>
  business_types: Array<{
    business_type: string
    count: number
    size: number
  }>
  monthly_uploads: Array<{
    month: string
    count: number
    size: number
  }>
}> => {
  return request.get('/api/v1/files/statistics')
}

// 清理无效文件
export const cleanupInvalidFiles = (): Promise<{
  cleaned_count: number
  freed_size: number
}> => {
  return request.post('/api/v1/files/cleanup')
}

// 文件存储配置
export interface StorageConfig {
  storage_type: 'local' | 'oss' | 's3'
  max_file_size: number
  allowed_extensions: string[]
  upload_path: string
  cdn_domain?: string
}

// 获取存储配置
export const getStorageConfig = (): Promise<StorageConfig> => {
  return request.get('/api/v1/files/config')
}

// 更新存储配置
export const updateStorageConfig = (config: Partial<StorageConfig>): Promise<StorageConfig> => {
  return request.put('/api/v1/files/config', config)
}

// 文件分享相关接口
export interface FileShare {
  id: string
  file_id: string
  share_token: string
  share_url: string
  expire_time?: string
  download_limit?: number
  download_count: number
  password?: string
  created_at: string
}

export interface FileShareForm {
  file_id: string
  expire_time?: string
  download_limit?: number
  password?: string
}

// 创建文件分享
export const createFileShare = (data: FileShareForm): Promise<FileShare> => {
  return request.post('/api/v1/files/share', data)
}

// 获取分享文件信息
export const getSharedFileInfo = (token: string, password?: string): Promise<{
  file_info: FileInfo
  share_info: FileShare
}> => {
  return request.get(`/api/v1/files/share/${token}`, {
    params: { password }
  })
}

// 下载分享文件
export const downloadSharedFile = (token: string, password?: string): Promise<Blob> => {
  return request.get(`/api/v1/files/share/${token}/download`, {
    params: { password },
    responseType: 'blob'
  })
}

// 获取我的分享列表
export const getMyShares = (params: {
  page?: number
  page_size?: number
}): Promise<{
  list: (FileShare & { file_name: string })[]
  total: number
  page: number
  page_size: number
}> => {
  return request.get('/api/v1/files/my-shares', { params })
}

// 删除文件分享
export const deleteFileShare = (id: string): Promise<void> => {
  return request.delete(`/api/v1/files/share/${id}`)
}
