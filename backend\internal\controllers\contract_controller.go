package controllers

import (
	"hospital-management/internal/dto"
	"hospital-management/internal/services"
	"hospital-management/pkg/logger"
	customValidator "hospital-management/pkg/validator"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
)

type ContractController struct {
	contractService *services.ContractService
	validator       *validator.Validate
}

func NewContractController(contractService *services.ContractService) *ContractController {
	return &ContractController{
		contractService: contractService,
		validator:       customValidator.NewValidator(),
	}
}

// ===== 合同管理 =====

// GetContracts 获取合同列表
// @Summary 获取合同列表
// @Description 获取合同列表，支持分页、搜索和筛选
// @Tags 合同管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param keyword query string false "搜索关键词"
// @Param contract_type query string false "合同类型"
// @Param status query string false "状态" Enums(DRAFT, PENDING, ACTIVE, COMPLETED, TERMINATED)
// @Param counterparty_id query string false "对方单位ID"
// @Param start_date_from query string false "开始日期起"
// @Param start_date_to query string false "开始日期止"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /contracts [get]
func (c *ContractController) GetContracts(ctx *gin.Context) {
	var req dto.ContractListRequest

	// 设置默认值
	req.Page = 1
	req.PageSize = 10

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	contracts, total, err := c.contractService.GetContracts(&req)
	if err != nil {
		logger.Error("Failed to get contracts:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取合同列表失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      contracts,
			"total":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	})
}

// GetContract 获取合同详情
// @Summary 获取合同详情
// @Description 获取指定合同的详细信息
// @Tags 合同管理
// @Accept json
// @Produce json
// @Param id path string true "合同ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "合同不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /contracts/{id} [get]
func (c *ContractController) GetContract(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的合同ID",
		})
		return
	}

	// 调用服务
	contract, err := c.contractService.GetContract(id)
	if err != nil {
		logger.Error("Failed to get contract:", err)
		if err.Error() == "合同不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "获取合同详情失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    contract,
	})
}

// CreateContract 创建合同
// @Summary 创建合同
// @Description 创建新的合同
// @Tags 合同管理
// @Accept json
// @Produce json
// @Param request body dto.CreateContractRequest true "创建合同请求"
// @Success 201 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /contracts [post]
func (c *ContractController) CreateContract(ctx *gin.Context) {
	var req dto.CreateContractRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	contract, err := c.contractService.CreateContract(&req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to create contract:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建合同失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "创建成功",
		"data":    contract,
	})
}

// UpdateContract 更新合同
// @Summary 更新合同
// @Description 更新合同信息
// @Tags 合同管理
// @Accept json
// @Produce json
// @Param id path string true "合同ID"
// @Param request body dto.UpdateContractRequest true "更新合同请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "合同不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /contracts/{id} [put]
func (c *ContractController) UpdateContract(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的合同ID",
		})
		return
	}

	var req dto.UpdateContractRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	contract, err := c.contractService.UpdateContract(id, &req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to update contract:", err)
		if err.Error() == "合同不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "更新合同失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    contract,
	})
}

// UpdateContractStatus 更新合同状态
// @Summary 更新合同状态
// @Description 更新合同的状态
// @Tags 合同管理
// @Accept json
// @Produce json
// @Param id path string true "合同ID"
// @Param request body dto.UpdateContractStatusRequest true "更新合同状态请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "合同不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /contracts/{id}/status [put]
func (c *ContractController) UpdateContractStatus(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的合同ID",
		})
		return
	}

	var req dto.UpdateContractStatusRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	contract, err := c.contractService.UpdateContractStatus(id, &req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to update contract status:", err)
		if err.Error() == "合同不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "更新合同状态失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    contract,
	})
}

// DeleteContract 删除合同
// @Summary 删除合同
// @Description 删除指定的合同
// @Tags 合同管理
// @Accept json
// @Produce json
// @Param id path string true "合同ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "合同不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /contracts/{id} [delete]
func (c *ContractController) DeleteContract(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的合同ID",
		})
		return
	}

	// 调用服务
	if err := c.contractService.DeleteContract(id); err != nil {
		logger.Error("Failed to delete contract:", err)
		if err.Error() == "合同不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "删除合同失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// ===== 付款计划管理 =====

// GetPaymentSchedules 获取付款计划列表
// @Summary 获取付款计划列表
// @Description 获取付款计划列表，支持分页和筛选
// @Tags 合同管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param contract_id query string false "合同ID"
// @Param status query string false "状态" Enums(PENDING, PAID)
// @Param due_date_from query string false "到期日期起"
// @Param due_date_to query string false "到期日期止"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /payment-schedules [get]
func (c *ContractController) GetPaymentSchedules(ctx *gin.Context) {
	var req dto.PaymentScheduleListRequest

	// 设置默认值
	req.Page = 1
	req.PageSize = 10

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	schedules, total, err := c.contractService.GetPaymentSchedules(&req)
	if err != nil {
		logger.Error("Failed to get payment schedules:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取付款计划列表失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      schedules,
			"total":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	})
}

// CreatePaymentSchedule 创建付款计划
// @Summary 创建付款计划
// @Description 创建新的付款计划
// @Tags 合同管理
// @Accept json
// @Produce json
// @Param request body dto.CreatePaymentScheduleRequest true "创建付款计划请求"
// @Success 201 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /payment-schedules [post]
func (c *ContractController) CreatePaymentSchedule(ctx *gin.Context) {
	var req dto.CreatePaymentScheduleRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	schedule, err := c.contractService.CreatePaymentSchedule(&req)
	if err != nil {
		logger.Error("Failed to create payment schedule:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建付款计划失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "创建成功",
		"data":    schedule,
	})
}

// UpdatePaymentSchedule 更新付款计划
// @Summary 更新付款计划
// @Description 更新付款计划信息
// @Tags 合同管理
// @Accept json
// @Produce json
// @Param id path string true "付款计划ID"
// @Param request body dto.UpdatePaymentScheduleRequest true "更新付款计划请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "付款计划不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /payment-schedules/{id} [put]
func (c *ContractController) UpdatePaymentSchedule(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的付款计划ID",
		})
		return
	}

	var req dto.UpdatePaymentScheduleRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	schedule, err := c.contractService.UpdatePaymentSchedule(id, &req)
	if err != nil {
		logger.Error("Failed to update payment schedule:", err)
		if err.Error() == "付款计划不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "更新付款计划失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    schedule,
	})
}

// BatchCreatePaymentSchedules 批量创建付款计划
// @Summary 批量创建付款计划
// @Description 批量创建付款计划
// @Tags 合同管理
// @Accept json
// @Produce json
// @Param request body dto.BatchCreatePaymentScheduleRequest true "批量创建付款计划请求"
// @Success 201 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /payment-schedules/batch [post]
func (c *ContractController) BatchCreatePaymentSchedules(ctx *gin.Context) {
	var req dto.BatchCreatePaymentScheduleRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	schedules, err := c.contractService.BatchCreatePaymentSchedules(&req)
	if err != nil {
		logger.Error("Failed to batch create payment schedules:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "批量创建付款计划失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "批量创建成功",
		"data":    schedules,
	})
}
