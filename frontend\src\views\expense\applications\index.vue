<template>
  <div class="expense-applications-page">
    <a-card :bordered="false">
      <template #title>
        <span>费用报销管理</span>
      </template>

      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <PlusOutlined />
            </template>
            新建报销
          </a-button>
          <a-button @click="handleRefresh">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 搜索区域 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="申请状态">
            <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear style="width: 120px">
              <a-select-option value="DRAFT">草稿</a-select-option>
              <a-select-option value="PENDING">待审批</a-select-option>
              <a-select-option value="APPROVED">已批准</a-select-option>
              <a-select-option value="REJECTED">已驳回</a-select-option>
              <a-select-option value="PAID">已付款</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="申请时间">
            <a-range-picker v-model:value="dateRange" style="width: 240px" @change="handleDateChange" />
          </a-form-item>

          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon>
                  <SearchOutlined />
                </template>
                搜索
              </a-button>
              <a-button @click="handleReset">
                <template #icon>
                  <ReloadOutlined />
                </template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 报销列表 -->
      <a-table :columns="columns" :data-source="applicationList" :loading="loading" :pagination="pagination"
        row-key="id" size="middle" @change="handleTableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'total_amount'">
            <span class="amount-text">{{ formatAmount(record.total_amount) }}</span>
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleView(record)">
                查看
              </a-button>
              <a-button type="link" size="small" @click="handleEdit(record)" v-if="record.status === 'DRAFT'">
                编辑
              </a-button>
              <a-button type="link" size="small" @click="handleSubmit(record)" v-if="record.status === 'DRAFT'">
                提交
              </a-button>
              <a-popconfirm title="确定要删除这个报销申请吗？" @confirm="handleDelete(record)" v-if="record.status === 'DRAFT'">
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑报销弹窗 -->
    <a-modal v-model:open="modalVisible" :title="modalTitle" :confirm-loading="modalLoading" width="1000px"
      @ok="handleModalSubmit" @cancel="handleCancel">
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="报销标题" name="title">
          <a-input v-model:value="formData.title" placeholder="请输入报销标题" />
        </a-form-item>

        <a-form-item label="所属部门" name="department_id">
          <a-tree-select v-model:value="formData.department_id" :tree-data="departmentTreeOptions" placeholder="请选择部门"
            tree-default-expand-all />
        </a-form-item>

        <a-form-item label="关联事前申请" name="pre_application_id">
          <a-select v-model:value="formData.pre_application_id" placeholder="请选择关联的事前申请（可选）" allow-clear
            :options="preApplicationOptions" />
        </a-form-item>

        <a-form-item label="费用明细" name="items">
          <div class="expense-items">
            <a-table :columns="itemColumns" :data-source="formData.items" :pagination="false" row-key="key"
              size="small">
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'expense_type'">
                  <a-select v-model:value="record.expense_type" placeholder="费用类型" style="width: 100%">
                    <a-select-option value="transport">交通费</a-select-option>
                    <a-select-option value="accommodation">住宿费</a-select-option>
                    <a-select-option value="meal">餐费</a-select-option>
                    <a-select-option value="communication">通讯费</a-select-option>
                    <a-select-option value="office">办公费</a-select-option>
                    <a-select-option value="other">其他</a-select-option>
                  </a-select>
                </template>

                <template v-else-if="column.key === 'expense_date'">
                  <a-date-picker v-model:value="record.expense_date" placeholder="费用日期" style="width: 100%" />
                </template>

                <template v-else-if="column.key === 'amount'">
                  <a-input-number v-model:value="record.amount" :min="0" :precision="2" placeholder="金额"
                    style="width: 100%" @change="calculateTotal" />
                </template>

                <template v-else-if="column.key === 'description'">
                  <a-input v-model:value="record.description" placeholder="费用说明" />
                </template>

                <template v-else-if="column.key === 'invoice_number'">
                  <a-input v-model:value="record.invoice_number" placeholder="发票号码" />
                </template>

                <template v-else-if="column.key === 'invoice_amount'">
                  <a-input-number v-model:value="record.invoice_amount" :min="0" :precision="2" placeholder="发票金额"
                    style="width: 100%" />
                </template>

                <template v-else-if="column.key === 'action'">
                  <a-button type="link" size="small" danger @click="removeItem(index)">
                    删除
                  </a-button>
                </template>
              </template>
            </a-table>

            <div class="expense-actions">
              <a-button type="dashed" @click="addItem" block>
                <template #icon>
                  <PlusOutlined />
                </template>
                添加费用明细
              </a-button>
            </div>

            <div class="total-amount">
              <span>总金额：<strong class="amount-text">{{ formatAmount(totalAmount) }}</strong></span>
            </div>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 查看详情弹窗 -->
    <a-modal v-model:open="viewModalVisible" title="报销详情" :footer="null" width="1000px">
      <div v-if="currentRecord">
        <a-descriptions :column="2" bordered class="detail-descriptions">
          <a-descriptions-item label="报销标题" :span="2">
            {{ currentRecord.title }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人">
            {{ currentRecord.applicant_name }}
          </a-descriptions-item>
          <a-descriptions-item label="所属部门">
            {{ currentRecord.department_name }}
          </a-descriptions-item>
          <a-descriptions-item label="总金额">
            <span class="amount-text">{{ formatAmount(currentRecord.total_amount) }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="申请状态">
            <a-tag :color="getStatusColor(currentRecord.status)">
              {{ getStatusText(currentRecord.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="关联事前申请" v-if="currentRecord.pre_application_title">
            {{ currentRecord.pre_application_title }}
          </a-descriptions-item>
          <a-descriptions-item label="申请时间">
            {{ currentRecord.created_at }}
          </a-descriptions-item>
        </a-descriptions>

        <a-divider>费用明细</a-divider>

        <a-table :columns="viewItemColumns" :data-source="currentRecord.items" :pagination="false" row-key="id"
          size="small" />
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'
import dayjs, { type Dayjs } from 'dayjs'
import {
  getExpenseApplications,
  createExpenseApplication,
  updateExpenseApplication,
  submitExpenseApplication,
  getPreApplications,
  type ExpenseApplication,
  type ExpenseApplicationForm,
  type ExpenseApplicationQuery,
  type ExpenseItem,
  type PreApplication
} from '@/api/expense'
import { getDepartmentTree, type Department } from '@/api/system'
import type { Rule } from 'ant-design-vue/es/form'
import type { FormInstance } from 'ant-design-vue'

const loading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const viewModalVisible = ref(false)
const formRef = ref<FormInstance>()
const editingId = ref<number | null>(null)

const applicationList = ref<ExpenseApplication[]>([])
const departmentList = ref<Department[]>([])
const preApplicationList = ref<PreApplication[]>([])
const currentRecord = ref<ExpenseApplication | null>(null)
const dateRange = ref<[Dayjs, Dayjs] | null>(null)

const searchForm = reactive<ExpenseApplicationQuery>({
  status: undefined,
  start_date: undefined,
  end_date: undefined,
  page: 1,
  page_size: 10
})

const formData = reactive<ExpenseApplicationForm & {
  items: (ExpenseItem & { key: number; expense_date: Dayjs | null })[]
}>({
  title: '',
  department_id: 0,
  pre_application_id: undefined,
  items: []
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '报销标题',
    dataIndex: 'title',
    key: 'title',
    width: 200,
    ellipsis: true
  },
  {
    title: '申请人',
    dataIndex: 'applicant_name',
    key: 'applicant_name',
    width: 100
  },
  {
    title: '所属部门',
    dataIndex: 'department_name',
    key: 'department_name',
    width: 150
  },
  {
    title: '总金额',
    key: 'total_amount',
    width: 120,
    align: 'right'
  },
  {
    title: '关联事前申请',
    dataIndex: 'pre_application_title',
    key: 'pre_application_title',
    width: 150,
    ellipsis: true
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '申请时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 费用明细表格列配置
const itemColumns = [
  {
    title: '费用类型',
    key: 'expense_type',
    width: 120
  },
  {
    title: '费用日期',
    key: 'expense_date',
    width: 120
  },
  {
    title: '金额',
    key: 'amount',
    width: 100
  },
  {
    title: '费用说明',
    key: 'description',
    width: 150
  },
  {
    title: '发票号码',
    key: 'invoice_number',
    width: 120
  },
  {
    title: '发票金额',
    key: 'invoice_amount',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 80
  }
]

// 查看详情时的费用明细列配置
const viewItemColumns = [
  {
    title: '费用类型',
    dataIndex: 'expense_type',
    key: 'expense_type',
    width: 120
  },
  {
    title: '费用日期',
    dataIndex: 'expense_date',
    key: 'expense_date',
    width: 120
  },
  {
    title: '金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 100,
    align: 'right',
    customRender: ({ text }: any) => formatAmount(text)
  },
  {
    title: '费用说明',
    dataIndex: 'description',
    key: 'description'
  },
  {
    title: '发票号码',
    dataIndex: 'invoice_number',
    key: 'invoice_number',
    width: 120
  },
  {
    title: '发票金额',
    dataIndex: 'invoice_amount',
    key: 'invoice_amount',
    width: 100,
    align: 'right',
    customRender: ({ text }: any) => text ? formatAmount(text) : '-'
  }
]

// 表单验证规则
const rules: Record<string, Rule[]> = {
  title: [
    { required: true, message: '请输入报销标题', trigger: 'blur' }
  ],
  department_id: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ],
  items: [
    { required: true, message: '请添加费用明细', trigger: 'change' }
  ]
}

// 弹窗标题
const modalTitle = computed(() => {
  return editingId.value ? '编辑费用报销' : '新建费用报销'
})

// 部门树形选择器选项
const departmentTreeOptions = computed(() => {
  const convertToTreeData = (departments: Department[]): any[] => {
    return departments.map(dept => ({
      title: dept.name,
      value: dept.id,
      key: dept.id,
      children: dept.children ? convertToTreeData(dept.children) : undefined
    }))
  }
  return convertToTreeData(departmentList.value)
})

// 事前申请选项
const preApplicationOptions = computed(() => {
  return preApplicationList.value
    .filter(app => app.status === 'APPROVED')  // 修改为大写
    .map(app => ({
      label: app.title,
      value: app.id
    }))
})

// 计算总金额
const totalAmount = computed(() => {
  return formData.items.reduce((sum, item) => sum + (item.amount || 0), 0)
})

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'DRAFT': 'default',
    'PENDING': 'blue',
    'APPROVED': 'green',
    'REJECTED': 'red',
    'PAID': 'purple'
  }
  return colorMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'DRAFT': '草稿',
    'PENDING': '待审批',
    'APPROVED': '已批准',
    'REJECTED': '已驳回',
    'PAID': '已付款'
  }
  return textMap[status] || status
}

// 格式化金额
const formatAmount = (amount: number) => {
  return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`
}

// 加载报销数据
const loadApplications = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.current,
      page_size: pagination.pageSize
    }
    const response = await getExpenseApplications(params)
    applicationList.value = response.list
    pagination.total = response.total
  } catch (error) {
    message.error('加载报销数据失败')
  } finally {
    loading.value = false
  }
}

// 加载基础数据
const loadBaseData = async () => {
  try {
    const [departments, preApps] = await Promise.all([
      getDepartmentTree(),
      getPreApplications({ status: 'APPROVED' })  // 修改为大写
    ])
    departmentList.value = departments
    preApplicationList.value = preApps.list || []  // 确保是数组
  } catch (error) {
    console.error('加载基础数据失败:', error)
    message.error('加载基础数据失败')
    // 出错时设置默认值
    departmentList.value = []
    preApplicationList.value = []
  }
}

// 日期范围变化处理
const handleDateChange = (dates: [Dayjs, Dayjs] | null) => {
  if (dates) {
    searchForm.start_date = dates[0].format('YYYY-MM-DD')
    searchForm.end_date = dates[1].format('YYYY-MM-DD')
  } else {
    searchForm.start_date = undefined
    searchForm.end_date = undefined
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadApplications()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    status: undefined,
    start_date: undefined,
    end_date: undefined
  })
  dateRange.value = null
  pagination.current = 1
  loadApplications()
}

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadApplications()
}

// 新建报销
const handleAdd = () => {
  editingId.value = null
  resetForm()
  addItem() // 默认添加一行费用明细
  modalVisible.value = true
}

// 编辑报销
const handleEdit = (record: ExpenseApplication) => {
  editingId.value = record.id
  Object.assign(formData, {
    title: record.title,
    department_id: record.department_id,
    pre_application_id: record.pre_application_id,
    items: record.items.map((item, index) => ({
      ...item,
      key: index,
      expense_date: item.expense_date ? dayjs(item.expense_date) : null
    }))
  })
  modalVisible.value = true
}

// 查看详情
const handleView = (record: ExpenseApplication) => {
  currentRecord.value = record
  viewModalVisible.value = true
}

// 提交报销
const handleSubmit = (record: ExpenseApplication) => {
  Modal.confirm({
    title: '确认提交',
    content: '提交后将无法修改，确定要提交这个报销申请吗？',
    onOk: async () => {
      try {
        await submitExpenseApplication(record.id)
        message.success('提交成功')
        loadApplications()
      } catch (error) {
        message.error('提交失败')
      }
    }
  })
}

// 删除报销
const handleDelete = async (record: ExpenseApplication) => {
  try {
    // TODO: 实现删除API
    message.success('删除成功')
    loadApplications()
  } catch (error) {
    message.error('删除失败')
  }
}

// 刷新数据
const handleRefresh = () => {
  loadApplications()
}

// 添加费用明细
const addItem = () => {
  const newItem = {
    key: Date.now(),
    expense_type: '',
    expense_date: null,
    amount: 0,
    description: '',
    invoice_number: '',
    invoice_amount: 0
  }
  formData.items.push(newItem)
}

// 移除费用明细
const removeItem = (index: number) => {
  formData.items.splice(index, 1)
  calculateTotal()
}

// 计算总金额
const calculateTotal = () => {
  // 触发响应式更新
  formData.items = [...formData.items]
}

// 提交表单
const handleModalSubmit = async () => {
  try {
    await formRef.value?.validate()

    if (formData.items.length === 0) {
      message.error('请添加费用明细')
      return
    }

    modalLoading.value = true

    const submitData = {
      title: formData.title,
      department_id: formData.department_id,
      pre_application_id: formData.pre_application_id,
      items: formData.items.map(item => ({
        expense_type: item.expense_type,
        expense_date: item.expense_date?.format('YYYY-MM-DD') || '',
        amount: item.amount,
        description: item.description,
        invoice_number: item.invoice_number,
        invoice_amount: item.invoice_amount
      }))
    }

    if (editingId.value) {
      await updateExpenseApplication(editingId.value, submitData)
      message.success('更新成功')
    } else {
      await createExpenseApplication(submitData)
      message.success('创建成功')
    }

    modalVisible.value = false
    loadApplications()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    modalLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    title: '',
    department_id: 0,
    pre_application_id: undefined,
    items: []
  })
  formRef.value?.resetFields()
}

onMounted(() => {
  loadBaseData()
  loadApplications()
})
</script>

<style scoped>
.expense-applications-page {
  padding: 24px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.amount-text {
  font-weight: 500;
  color: #1890ff;
}

.expense-items {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
}

.expense-actions {
  margin-top: 16px;
}

.total-amount {
  margin-top: 16px;
  text-align: right;
  font-size: 16px;
}

.detail-descriptions {
  margin-bottom: 16px;
}
</style>
