package dto

import (
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// ===== 资产分类管理相关 =====

// CreateAssetCategoryRequest 创建资产分类请求
type CreateAssetCategoryRequest struct {
	ParentID *uuid.UUID `json:"parent_id,omitempty" validate:"omitempty,uuid"`
	Name     string     `json:"name" validate:"required,max=100"`
	Code     string     `json:"code" validate:"required,max=50"`
}

// UpdateAssetCategoryRequest 更新资产分类请求
type UpdateAssetCategoryRequest struct {
	ParentID *uuid.UUID `json:"parent_id,omitempty" validate:"omitempty,uuid"`
	Name     *string    `json:"name,omitempty" validate:"omitempty,max=100"`
	Code     *string    `json:"code,omitempty" validate:"omitempty,max=50"`
}

// AssetCategoryResponse 资产分类响应
type AssetCategoryResponse struct {
	ID                 uuid.UUID               `json:"id"`
	ParentID           *uuid.UUID              `json:"parent_id,omitempty"`
	Name               string                  `json:"name"`
	Code               string                  `json:"code"`
	DepreciationMethod *string                 `json:"depreciation_method,omitempty"`
	DepreciationYears  *int                    `json:"depreciation_years,omitempty"`
	Description        *string                 `json:"description,omitempty"`
	CreatedAt          string                  `json:"created_at"`
	UpdatedAt          string                  `json:"updated_at"`
	Parent             *AssetCategoryResponse  `json:"parent,omitempty"`
	Children           []AssetCategoryResponse `json:"children,omitempty"`
}

// AssetCategoryTreeResponse 资产分类树形响应
type AssetCategoryTreeResponse struct {
	ID       uuid.UUID                   `json:"id"`
	ParentID *uuid.UUID                  `json:"parent_id,omitempty"`
	Name     string                      `json:"name"`
	Code     string                      `json:"code"`
	Children []AssetCategoryTreeResponse `json:"children,omitempty"`
}

// ===== 资产管理相关 =====

// CreateAssetRequest 创建资产请求
type CreateAssetRequest struct {
	AssetName          string           `json:"asset_name" validate:"required,max=255"`
	CategoryID         uuid.UUID        `json:"category_id" validate:"required,uuid"`
	SourceType         *string          `json:"source_type,omitempty" validate:"omitempty,max=30"`
	PurchaseContractID *uuid.UUID       `json:"purchase_contract_id,omitempty" validate:"omitempty,uuid"`
	PurchaseDate       *string          `json:"purchase_date,omitempty" validate:"omitempty,datetime=2006-01-02"`
	PurchasePrice      *decimal.Decimal `json:"purchase_price,omitempty" validate:"omitempty,gte=0"`
	Status             string           `json:"status" validate:"required,oneof=IN_USE IDLE MAINTENANCE SCRAPPED"`
	OwnerDeptID        uuid.UUID        `json:"owner_dept_id" validate:"required,uuid"`
	CustodianID        *uuid.UUID       `json:"custodian_id,omitempty" validate:"omitempty,uuid"`
	Location           *string          `json:"location,omitempty" validate:"omitempty,max=255"`
}

// UpdateAssetRequest 更新资产请求
type UpdateAssetRequest struct {
	AssetName          *string          `json:"asset_name,omitempty" validate:"omitempty,max=255"`
	CategoryID         *uuid.UUID       `json:"category_id,omitempty" validate:"omitempty,uuid"`
	SourceType         *string          `json:"source_type,omitempty" validate:"omitempty,max=30"`
	PurchaseContractID *uuid.UUID       `json:"purchase_contract_id,omitempty" validate:"omitempty,uuid"`
	PurchaseDate       *string          `json:"purchase_date,omitempty" validate:"omitempty,datetime=2006-01-02"`
	PurchasePrice      *decimal.Decimal `json:"purchase_price,omitempty" validate:"omitempty,gte=0"`
	Status             *string          `json:"status,omitempty" validate:"omitempty,oneof=IN_USE IDLE MAINTENANCE SCRAPPED"`
	OwnerDeptID        *uuid.UUID       `json:"owner_dept_id,omitempty" validate:"omitempty,uuid"`
	CustodianID        *uuid.UUID       `json:"custodian_id,omitempty" validate:"omitempty,uuid"`
	Location           *string          `json:"location,omitempty" validate:"omitempty,max=255"`
}

// UpdateAssetStatusRequest 更新资产状态请求
type UpdateAssetStatusRequest struct {
	Status string `json:"status" validate:"required,oneof=IN_USE IDLE MAINTENANCE SCRAPPED"`
}

// AssetResponse 资产响应
type AssetResponse struct {
	ID                 uuid.UUID                 `json:"id"`
	AssetCode          string                    `json:"asset_code"`
	AssetName          string                    `json:"asset_name"`
	CategoryID         uuid.UUID                 `json:"category_id"`
	SourceType         *string                   `json:"source_type,omitempty"`
	PurchaseContractID *uuid.UUID                `json:"purchase_contract_id,omitempty"`
	PurchaseDate       *string                   `json:"purchase_date,omitempty"`
	PurchasePrice      *decimal.Decimal          `json:"purchase_price,omitempty"`
	Status             string                    `json:"status"`
	OwnerDeptID        uuid.UUID                 `json:"owner_dept_id"`
	CustodianID        *uuid.UUID                `json:"custodian_id,omitempty"`
	Location           *string                   `json:"location,omitempty"`
	CreatedAt          string                    `json:"created_at"`
	UpdatedAt          string                    `json:"updated_at"`
	Category           *AssetCategoryResponse    `json:"category,omitempty"`
	PurchaseContract   *ContractSimpleResponse   `json:"purchase_contract,omitempty"`
	OwnerDept          *DepartmentSimpleResponse `json:"owner_dept,omitempty"`
	Custodian          *UserSimpleResponse       `json:"custodian,omitempty"`
}

// AssetListRequest 资产列表请求
type AssetListRequest struct {
	Page             int        `form:"page" validate:"min=1"`
	PageSize         int        `form:"page_size" validate:"min=1,max=100"`
	Keyword          string     `form:"keyword"`
	CategoryID       *uuid.UUID `form:"category_id" validate:"omitempty,uuid"`
	Status           string     `form:"status" validate:"omitempty,oneof=IN_USE IDLE MAINTENANCE SCRAPPED"`
	OwnerDeptID      *uuid.UUID `form:"owner_dept_id" validate:"omitempty,uuid"`
	CustodianID      *uuid.UUID `form:"custodian_id" validate:"omitempty,uuid"`
	PurchaseDateFrom *string    `form:"purchase_date_from" validate:"omitempty,datetime=2006-01-02"`
	PurchaseDateTo   *string    `form:"purchase_date_to" validate:"omitempty,datetime=2006-01-02"`
}

// BatchUpdateAssetStatusRequest 批量更新资产状态请求
type BatchUpdateAssetStatusRequest struct {
	AssetIDs []uuid.UUID `json:"asset_ids" validate:"required,dive,uuid"`
	Status   string      `json:"status" validate:"required,oneof=IN_USE IDLE MAINTENANCE SCRAPPED"`
}

// AssetTransferRequest 资产转移请求
type AssetTransferRequest struct {
	AssetIDs       []uuid.UUID `json:"asset_ids" validate:"required,dive,uuid"`
	NewOwnerDept   uuid.UUID   `json:"new_owner_dept" validate:"required,uuid"`
	NewCustodian   *uuid.UUID  `json:"new_custodian,omitempty" validate:"omitempty,uuid"`
	NewLocation    *string     `json:"new_location,omitempty" validate:"omitempty,max=255"`
	TransferReason string      `json:"transfer_reason" validate:"required,max=500"`
}

// ===== 资产统计分析相关 =====

// AssetStatisticsRequest 资产统计请求
type AssetStatisticsRequest struct {
	CategoryID  *uuid.UUID `form:"category_id" validate:"omitempty,uuid"`
	OwnerDeptID *uuid.UUID `form:"owner_dept_id" validate:"omitempty,uuid"`
	Status      string     `form:"status" validate:"omitempty,oneof=IN_USE IDLE MAINTENANCE SCRAPPED"`
}

// AssetStatisticsResponse 资产统计响应
type AssetStatisticsResponse struct {
	TotalAssets       int64                          `json:"total_assets"`
	TotalValue        decimal.Decimal                `json:"total_value"`
	InUseAssets       int64                          `json:"in_use_assets"`
	IdleAssets        int64                          `json:"idle_assets"`
	MaintenanceAssets int64                          `json:"maintenance_assets"`
	ScrappedAssets    int64                          `json:"scrapped_assets"`
	CategoryStats     []AssetCategoryStatsResponse   `json:"category_stats"`
	DepartmentStats   []AssetDepartmentStatsResponse `json:"department_stats"`
	StatusStats       []AssetStatusStatsResponse     `json:"status_stats"`
}

// AssetCategoryStatsResponse 资产分类统计响应
type AssetCategoryStatsResponse struct {
	CategoryID   uuid.UUID       `json:"category_id"`
	CategoryName string          `json:"category_name"`
	AssetCount   int64           `json:"asset_count"`
	TotalValue   decimal.Decimal `json:"total_value"`
}

// AssetDepartmentStatsResponse 部门资产统计响应
type AssetDepartmentStatsResponse struct {
	DepartmentID   uuid.UUID       `json:"department_id"`
	DepartmentName string          `json:"department_name"`
	AssetCount     int64           `json:"asset_count"`
	TotalValue     decimal.Decimal `json:"total_value"`
}

// AssetStatusStatsResponse 资产状态统计响应
type AssetStatusStatsResponse struct {
	Status     string          `json:"status"`
	AssetCount int64           `json:"asset_count"`
	TotalValue decimal.Decimal `json:"total_value"`
}

// ===== 资产盘点相关 =====

// AssetInventoryRequest 资产盘点请求
type AssetInventoryRequest struct {
	InventoryName string      `json:"inventory_name" validate:"required,max=255"`
	DepartmentIDs []uuid.UUID `json:"department_ids" validate:"required,dive,uuid"`
	CategoryIDs   []uuid.UUID `json:"category_ids,omitempty" validate:"omitempty,dive,uuid"`
	InventoryDate string      `json:"inventory_date" validate:"required,datetime=2006-01-02"`
}

// AssetInventoryResponse 资产盘点响应
type AssetInventoryResponse struct {
	ID            uuid.UUID `json:"id"`
	InventoryCode string    `json:"inventory_code"`
	InventoryName string    `json:"inventory_name"`
	Status        string    `json:"status"`
	TotalAssets   int64     `json:"total_assets"`
	CheckedAssets int64     `json:"checked_assets"`
	InventoryDate string    `json:"inventory_date"`
	CreatedAt     string    `json:"created_at"`
	UpdatedAt     string    `json:"updated_at"`
}

// ===== 辅助响应结构 =====

// AssetSimpleResponse 资产简单响应（用于关联显示）
type AssetSimpleResponse struct {
	ID        uuid.UUID                    `json:"id"`
	AssetCode string                       `json:"asset_code"`
	AssetName string                       `json:"asset_name"`
	Status    string                       `json:"status"`
	Category  *AssetCategorySimpleResponse `json:"category,omitempty"`
}

// AssetCategorySimpleResponse 资产分类简单响应（用于关联显示）
type AssetCategorySimpleResponse struct {
	ID   uuid.UUID `json:"id"`
	Name string    `json:"name"`
	Code string    `json:"code"`
}

// ===== 资产维护记录相关（预留接口） =====

// AssetMaintenanceRequest 资产维护记录请求（预留）
type AssetMaintenanceRequest struct {
	AssetID         uuid.UUID       `json:"asset_id" validate:"required,uuid"`
	MaintenanceType string          `json:"maintenance_type" validate:"required,max=50"`
	MaintenanceDate string          `json:"maintenance_date" validate:"required,datetime=2006-01-02"`
	Cost            decimal.Decimal `json:"cost" validate:"required,gte=0"`
	Description     string          `json:"description" validate:"required,max=500"`
	Vendor          *string         `json:"vendor,omitempty" validate:"omitempty,max=255"`
}

// AssetMaintenanceResponse 已在下方重新定义，删除此重复定义

// ===== 通用功能相关DTO =====

// DashboardStatisticsResponse 仪表盘统计响应
type DashboardStatisticsResponse struct {
	BudgetStats      *BudgetStatsResponse      `json:"budget_stats"`
	ExpenseStats     *ExpenseStatsResponse     `json:"expense_stats"`
	ProcurementStats *ProcurementStatsResponse `json:"procurement_stats"`
	ContractStats    *ContractStatsResponse    `json:"contract_stats"`
	AssetStats       *AssetStatsResponse       `json:"asset_stats"`
}

// BudgetStatsResponse 预算统计响应
type BudgetStatsResponse struct {
	TotalBudget     decimal.Decimal `json:"total_budget"`
	UsedBudget      decimal.Decimal `json:"used_budget"`
	FrozenBudget    decimal.Decimal `json:"frozen_budget"`
	AvailableBudget decimal.Decimal `json:"available_budget"`
	UsageRate       decimal.Decimal `json:"usage_rate"`
}

// ExpenseStatsResponse 支出统计响应
type ExpenseStatsResponse struct {
	TotalExpenseApplications   int64           `json:"total_expense_applications"`
	PendingExpenseApplications int64           `json:"pending_expense_applications"`
	TotalExpenseAmount         decimal.Decimal `json:"total_expense_amount"`
	TotalPayments              int64           `json:"total_payments"`
	TotalPaymentAmount         decimal.Decimal `json:"total_payment_amount"`
}

// ProcurementStatsResponse 采购统计响应
type ProcurementStatsResponse struct {
	TotalSuppliers              int64           `json:"total_suppliers"`
	TotalPurchaseRequisitions   int64           `json:"total_purchase_requisitions"`
	PendingPurchaseRequisitions int64           `json:"pending_purchase_requisitions"`
	TotalPurchaseAmount         decimal.Decimal `json:"total_purchase_amount"`
}

// ContractStatsResponse 合同统计响应
type ContractStatsResponse struct {
	TotalContracts        int64           `json:"total_contracts"`
	ActiveContracts       int64           `json:"active_contracts"`
	TotalContractAmount   decimal.Decimal `json:"total_contract_amount"`
	TotalPaymentSchedules int64           `json:"total_payment_schedules"`
	TotalScheduleAmount   decimal.Decimal `json:"total_schedule_amount"`
}

// AssetStatsResponse 资产统计响应
type AssetStatsResponse struct {
	TotalAssets       int64           `json:"total_assets"`
	TotalAssetValue   decimal.Decimal `json:"total_asset_value"`
	InUseAssets       int64           `json:"in_use_assets"`
	IdleAssets        int64           `json:"idle_assets"`
	MaintenanceAssets int64           `json:"maintenance_assets"`
	ScrappedAssets    int64           `json:"scrapped_assets"`
	TotalCategories   int64           `json:"total_categories"`
}

// ===== 资产折旧管理相关 =====

// CreateAssetDepreciationRequest 创建资产折旧记录请求
type CreateAssetDepreciationRequest struct {
	AssetID                 uuid.UUID       `json:"asset_id" validate:"required,uuid"`
	Period                  string          `json:"period" validate:"required,len=7"` // YYYY-MM
	DepreciationAmount      decimal.Decimal `json:"depreciation_amount" validate:"required,gte=0"`
	AccumulatedDepreciation decimal.Decimal `json:"accumulated_depreciation" validate:"required,gte=0"`
	BookValue               decimal.Decimal `json:"book_value" validate:"required,gte=0"`
	DepreciationMethod      string          `json:"depreciation_method" validate:"required,oneof=straight_line declining_balance sum_of_years"`
	CalculationBasis        *string         `json:"calculation_basis,omitempty" validate:"omitempty,max=1000"`
	Remark                  *string         `json:"remark,omitempty" validate:"omitempty,max=500"`
}

// AssetDepreciationResponse 资产折旧记录响应
type AssetDepreciationResponse struct {
	ID                      uuid.UUID       `json:"id"`
	AssetID                 uuid.UUID       `json:"asset_id"`
	Period                  string          `json:"period"`
	DepreciationAmount      decimal.Decimal `json:"depreciation_amount"`
	AccumulatedDepreciation decimal.Decimal `json:"accumulated_depreciation"`
	BookValue               decimal.Decimal `json:"book_value"`
	DepreciationMethod      string          `json:"depreciation_method"`
	CalculationBasis        *string         `json:"calculation_basis,omitempty"`
	Remark                  *string         `json:"remark,omitempty"`
	CreatedAt               string          `json:"created_at"`
	CreatedBy               uuid.UUID       `json:"created_by"`

	// 关联数据
	Asset   *AssetResponse      `json:"asset,omitempty"`
	Creator *UserSimpleResponse `json:"creator,omitempty"`
}

// AssetDepreciationListRequest 资产折旧列表请求
type AssetDepreciationListRequest struct {
	Page      int        `form:"page" validate:"min=1"`
	PageSize  int        `form:"page_size" validate:"min=1,max=100"`
	AssetID   *uuid.UUID `form:"asset_id" validate:"omitempty,uuid"`
	Period    string     `form:"period" validate:"omitempty,len=7"`
	StartDate string     `form:"start_date" validate:"omitempty,datetime=2006-01-02"`
	EndDate   string     `form:"end_date" validate:"omitempty,datetime=2006-01-02"`
}

// BatchDepreciationRequest 批量折旧请求
type BatchDepreciationRequest struct {
	Period      string      `json:"period" validate:"required,len=7"` // YYYY-MM
	AssetIDs    []uuid.UUID `json:"asset_ids" validate:"required,dive,uuid"`
	ForceRecalc bool        `json:"force_recalc"` // 是否强制重新计算
}

// ===== 资产维护管理相关 =====

// CreateAssetMaintenanceRequest 创建资产维护记录请求
type CreateAssetMaintenanceRequest struct {
	AssetID             uuid.UUID       `json:"asset_id" validate:"required,uuid"`
	MaintenanceType     string          `json:"maintenance_type" validate:"required,oneof=routine repair upgrade"`
	MaintenanceDate     string          `json:"maintenance_date" validate:"required,datetime=2006-01-02"`
	MaintenanceCost     decimal.Decimal `json:"maintenance_cost" validate:"required,gte=0"`
	MaintenanceCompany  *string         `json:"maintenance_company,omitempty" validate:"omitempty,max=200"`
	MaintenancePerson   *string         `json:"maintenance_person,omitempty" validate:"omitempty,max=100"`
	Description         string          `json:"description" validate:"required,max=1000"`
	NextMaintenanceDate *string         `json:"next_maintenance_date,omitempty" validate:"omitempty,datetime=2006-01-02"`
	Status              string          `json:"status" validate:"required,oneof=planned in_progress completed"`
	AttachmentUrls      []string        `json:"attachment_urls,omitempty"`
	Remark              *string         `json:"remark,omitempty" validate:"omitempty,max=500"`
}

// UpdateAssetMaintenanceRequest 更新资产维护记录请求
type UpdateAssetMaintenanceRequest struct {
	MaintenanceType     *string          `json:"maintenance_type,omitempty" validate:"omitempty,oneof=routine repair upgrade"`
	MaintenanceDate     *string          `json:"maintenance_date,omitempty" validate:"omitempty,datetime=2006-01-02"`
	MaintenanceCost     *decimal.Decimal `json:"maintenance_cost,omitempty" validate:"omitempty,gte=0"`
	MaintenanceCompany  *string          `json:"maintenance_company,omitempty" validate:"omitempty,max=200"`
	MaintenancePerson   *string          `json:"maintenance_person,omitempty" validate:"omitempty,max=100"`
	Description         *string          `json:"description,omitempty" validate:"omitempty,max=1000"`
	NextMaintenanceDate *string          `json:"next_maintenance_date,omitempty" validate:"omitempty,datetime=2006-01-02"`
	Status              *string          `json:"status,omitempty" validate:"omitempty,oneof=planned in_progress completed"`
	AttachmentUrls      []string         `json:"attachment_urls,omitempty"`
	Remark              *string          `json:"remark,omitempty" validate:"omitempty,max=500"`
}

// AssetMaintenanceResponse 资产维护记录响应
type AssetMaintenanceResponse struct {
	ID                  uuid.UUID       `json:"id"`
	AssetID             uuid.UUID       `json:"asset_id"`
	MaintenanceType     string          `json:"maintenance_type"`
	MaintenanceDate     string          `json:"maintenance_date"`
	MaintenanceCost     decimal.Decimal `json:"maintenance_cost"`
	MaintenanceCompany  *string         `json:"maintenance_company,omitempty"`
	MaintenancePerson   *string         `json:"maintenance_person,omitempty"`
	Description         string          `json:"description"`
	NextMaintenanceDate *string         `json:"next_maintenance_date,omitempty"`
	Status              string          `json:"status"`
	AttachmentUrls      []string        `json:"attachment_urls,omitempty"`
	Remark              *string         `json:"remark,omitempty"`
	CreatedAt           string          `json:"created_at"`
	UpdatedAt           string          `json:"updated_at"`
	CreatedBy           uuid.UUID       `json:"created_by"`

	// 关联数据
	Asset   *AssetResponse      `json:"asset,omitempty"`
	Creator *UserSimpleResponse `json:"creator,omitempty"`
}

// AssetMaintenanceListRequest 资产维护列表请求
type AssetMaintenanceListRequest struct {
	Page            int        `form:"page" validate:"min=1"`
	PageSize        int        `form:"page_size" validate:"min=1,max=100"`
	AssetID         *uuid.UUID `form:"asset_id" validate:"omitempty,uuid"`
	MaintenanceType string     `form:"maintenance_type" validate:"omitempty,oneof=routine repair upgrade"`
	Status          string     `form:"status" validate:"omitempty,oneof=planned in_progress completed"`
	StartDate       string     `form:"start_date" validate:"omitempty,datetime=2006-01-02"`
	EndDate         string     `form:"end_date" validate:"omitempty,datetime=2006-01-02"`
}
