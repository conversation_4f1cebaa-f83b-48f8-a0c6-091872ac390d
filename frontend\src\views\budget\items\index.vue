<template>
  <div class="budget-items-page">
    <a-card :bordered="false">
      <template #title>
        <span>预算明细管理</span>
      </template>

      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <PlusOutlined />
            </template>
            新增明细
          </a-button>
          <a-button @click="handleBatchAdd">
            <template #icon>
              <AppstoreAddOutlined />
            </template>
            批量新增
          </a-button>
          <a-button @click="handleRefresh">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 搜索区域 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="预算方案">
            <a-select v-model:value="searchForm.scheme_id" placeholder="请选择预算方案" allow-clear style="width: 200px"
              :options="schemeOptions" />
          </a-form-item>

          <a-form-item label="预算科目">
            <a-tree-select v-model:value="searchForm.subject_id" :tree-data="subjectTreeOptions" placeholder="请选择预算科目"
              allow-clear style="width: 200px" />
          </a-form-item>

          <a-form-item label="部门">
            <a-tree-select v-model:value="searchForm.department_id" :tree-data="departmentTreeOptions"
              placeholder="请选择部门" allow-clear style="width: 200px" />
          </a-form-item>

          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon>
                  <SearchOutlined />
                </template>
                搜索
              </a-button>
              <a-button @click="handleReset">
                <template #icon>
                  <ReloadOutlined />
                </template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 预算明细表格 -->
      <a-table :columns="columns" :data-source="itemList" :loading="loading" :pagination="pagination" row-key="id"
        size="middle" :scroll="{ x: 1200 }" @change="handleTableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'budget_amount'">
            <span class="amount-text">{{ formatAmount(record.budget_amount) }}</span>
          </template>

          <template v-else-if="column.key === 'used_amount'">
            <span class="amount-text used">{{ formatAmount(record.used_amount) }}</span>
          </template>

          <template v-else-if="column.key === 'frozen_amount'">
            <span class="amount-text frozen">{{ formatAmount(record.frozen_amount) }}</span>
          </template>

          <template v-else-if="column.key === 'available_amount'">
            <span class="amount-text available">{{ formatAmount(record.available_amount) }}</span>
          </template>

          <template v-else-if="column.key === 'execution_rate'">
            <a-progress :percent="getExecutionRate(record)" size="small" :status="getProgressStatus(record)" />
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)">
                编辑
              </a-button>
              <a-button type="link" size="small" @click="handleViewDetail(record)">
                详情
              </a-button>
              <a-popconfirm title="确定要删除这个预算明细吗？" @confirm="handleDelete(record)">
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑预算明细弹窗 -->
    <a-modal v-model:open="modalVisible" :title="modalTitle" :confirm-loading="modalLoading" width="600px"
      @ok="handleSubmit" @cancel="handleCancel">
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="预算方案" name="scheme_id">
          <a-select v-model:value="formData.scheme_id" placeholder="请选择预算方案" :options="schemeOptions" />
        </a-form-item>

        <a-form-item label="预算科目" name="subject_id">
          <a-tree-select v-model:value="formData.subject_id" :tree-data="subjectTreeOptions" placeholder="请选择预算科目"
            tree-default-expand-all />
        </a-form-item>

        <a-form-item label="所属部门" name="department_id">
          <a-tree-select v-model:value="formData.department_id" :tree-data="departmentTreeOptions" placeholder="请选择部门"
            tree-default-expand-all />
        </a-form-item>

        <a-form-item label="预算金额" name="budget_amount">
          <a-input-number v-model:value="formData.budget_amount" :min="0" :precision="2" placeholder="请输入预算金额"
            style="width: 100%" :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/¥\s?|(,*)/g, '')" />
        </a-form-item>

        <a-form-item label="备注说明" name="description">
          <a-textarea v-model:value="formData.description" placeholder="请输入备注说明" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 批量新增弹窗 -->
    <a-modal v-model:open="batchModalVisible" title="批量新增预算明细" :confirm-loading="batchLoading" width="800px"
      @ok="handleBatchSubmit" @cancel="batchModalVisible = false">
      <div class="batch-form">
        <a-form layout="inline" :model="batchForm">
          <a-form-item label="预算方案">
            <a-select v-model:value="batchForm.scheme_id" placeholder="请选择预算方案" style="width: 200px"
              :options="schemeOptions" />
          </a-form-item>

          <a-form-item label="预算科目">
            <a-tree-select v-model:value="batchForm.subject_id" :tree-data="subjectTreeOptions" placeholder="请选择预算科目"
              style="width: 200px" />
          </a-form-item>

          <a-form-item>
            <a-button type="primary" @click="generateBatchItems">
              生成明细
            </a-button>
          </a-form-item>
        </a-form>

        <a-table :columns="batchColumns" :data-source="batchItems" :pagination="false" row-key="key" size="small"
          class="batch-table">
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'budget_amount'">
              <a-input-number v-model:value="record.budget_amount" :min="0" :precision="2" size="small"
                style="width: 100%" />
            </template>

            <template v-else-if="column.key === 'action'">
              <a-button type="link" size="small" danger @click="removeBatchItem(index)">
                删除
              </a-button>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  AppstoreAddOutlined,
  ReloadOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'
import {
  getBudgetItems,
  createBudgetItem,
  updateBudgetItem,
  batchCreateBudgetItems,
  getBudgetSchemes,
  getBudgetSubjectTree,
  type BudgetItem,
  type BudgetItemForm,
  type BudgetItemQuery,
  type BudgetScheme,
  type BudgetSubject
} from '@/api/budget'
import { getDepartmentTree, type Department } from '@/api/system'
import type { Rule } from 'ant-design-vue/es/form'
import type { FormInstance } from 'ant-design-vue'

const loading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const batchModalVisible = ref(false)
const batchLoading = ref(false)
const formRef = ref<FormInstance>()
const editingId = ref<string | null>(null)

const itemList = ref<BudgetItem[]>([])
const schemeList = ref<BudgetScheme[]>([])
const subjectList = ref<BudgetSubject[]>([])
const departmentList = ref<Department[]>([])
const batchItems = ref<any[]>([])

const searchForm = reactive<BudgetItemQuery>({
  scheme_id: undefined,
  subject_id: undefined,
  department_id: undefined,
  page: 1,
  page_size: 10
})

const formData = reactive<BudgetItemForm>({
  scheme_id: '',
  subject_id: '',
  department_id: '',
  budget_amount: 0,
  description: ''
})

const batchForm = reactive({
  scheme_id: undefined,
  subject_id: undefined
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '预算方案',
    dataIndex: 'scheme_name',
    key: 'scheme_name',
    width: 150
  },
  {
    title: '预算科目',
    dataIndex: 'subject_name',
    key: 'subject_name',
    width: 200
  },
  {
    title: '所属部门',
    dataIndex: 'department_name',
    key: 'department_name',
    width: 150
  },
  {
    title: '预算金额',
    dataIndex: 'budget_amount',
    key: 'budget_amount',
    width: 120,
    align: 'right'
  },
  {
    title: '已用金额',
    dataIndex: 'used_amount',
    key: 'used_amount',
    width: 120,
    align: 'right'
  },
  {
    title: '冻结金额',
    dataIndex: 'frozen_amount',
    key: 'frozen_amount',
    width: 120,
    align: 'right'
  },
  {
    title: '可用金额',
    dataIndex: 'available_amount',
    key: 'available_amount',
    width: 120,
    align: 'right'
  },
  {
    title: '执行率',
    dataIndex: 'execution_rate',
    key: 'execution_rate',
    width: 120
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right'
  }
]

// 批量表格列配置
const batchColumns = [
  {
    title: '部门名称',
    dataIndex: 'department_name',
    key: 'department_name'
  },
  {
    title: '预算金额',
    key: 'budget_amount',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 80
  }
]

// 表单验证规则
const rules: Record<string, Rule[]> = {
  scheme_id: [
    { required: true, message: '请选择预算方案', trigger: 'change' }
  ],
  subject_id: [
    { required: true, message: '请选择预算科目', trigger: 'change' }
  ],
  department_id: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ],
  budget_amount: [
    { required: true, message: '请输入预算金额', trigger: 'blur' }
  ]
}

// 弹窗标题
const modalTitle = computed(() => {
  return editingId.value ? '编辑预算明细' : '新增预算明细'
})

// 预算方案选项
const schemeOptions = computed(() => {
  return schemeList.value.map(scheme => ({
    label: scheme.name,
    value: scheme.id
  }))
})

// 预算科目树形选择器选项
const subjectTreeOptions = computed(() => {
  const convertToTreeData = (subjects: BudgetSubject[]): any[] => {
    return subjects.map(subject => ({
      title: `${subject.code} - ${subject.name}`,
      value: subject.id,
      key: subject.id,
      children: subject.children ? convertToTreeData(subject.children) : undefined
    }))
  }
  return convertToTreeData(subjectList.value)
})

// 部门树形选择器选项
const departmentTreeOptions = computed(() => {
  const convertToTreeData = (departments: Department[]): any[] => {
    return departments.map(dept => ({
      title: dept.name,
      value: dept.id,
      key: dept.id,
      children: dept.children ? convertToTreeData(dept.children) : undefined
    }))
  }
  return convertToTreeData(departmentList.value)
})

// 格式化金额
const formatAmount = (amount: number | undefined | null) => {
  if (amount === undefined || amount === null) return '¥0.00'
  return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`
}

// 获取执行率
const getExecutionRate = (record: BudgetItem) => {
  return Math.round(record.execution_rate || 0)
}

// 获取进度条状态
const getProgressStatus = (record: BudgetItem) => {
  const rate = getExecutionRate(record)
  if (rate >= 100) return 'exception'
  if (rate >= 80) return 'active'
  return 'normal'
}

// 加载预算明细数据
const loadItems = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.current,
      page_size: pagination.pageSize
    }
    const response = await getBudgetItems(params)
    itemList.value = response.list
    pagination.total = response.total
  } catch (error) {
    message.error('加载预算明细数据失败')
  } finally {
    loading.value = false
  }
}

// 加载基础数据
const loadBaseData = async () => {
  try {
    const [schemesResult, subjects, departments] = await Promise.all([
      getBudgetSchemes({ page: 1, page_size: 100 }), // 获取方案用于选择
      getBudgetSubjectTree(),
      getDepartmentTree()
    ])
    schemeList.value = schemesResult.list || []
    subjectList.value = subjects || []
    departmentList.value = departments || []
  } catch (error) {
    message.error('加载基础数据失败')
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadItems()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    scheme_id: undefined,
    subject_id: undefined,
    department_id: undefined
  })
  pagination.current = 1
  loadItems()
}

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadItems()
}

// 新增明细
const handleAdd = () => {
  editingId.value = null
  resetForm()
  modalVisible.value = true
}

// 编辑明细
const handleEdit = (record: BudgetItem) => {
  editingId.value = record.id
  Object.assign(formData, {
    scheme_id: record.scheme_id,
    subject_id: record.subject_id,
    department_id: record.department_id,
    budget_amount: record.budget_amount,
    description: record.description
  })
  modalVisible.value = true
}

// 查看详情
const handleViewDetail = (record: BudgetItem) => {
  // TODO: 实现查看详情功能
  message.info('查看详情功能待实现')
}

// 删除明细
const handleDelete = async (record: BudgetItem) => {
  try {
    // TODO: 实现删除API
    message.success('删除成功')
    loadItems()
  } catch (error) {
    message.error('删除失败')
  }
}

// 批量新增
const handleBatchAdd = () => {
  batchItems.value = []
  Object.assign(batchForm, {
    scheme_id: undefined,
    subject_id: undefined
  })
  batchModalVisible.value = true
}

// 生成批量明细
const generateBatchItems = () => {
  if (!batchForm.scheme_id || !batchForm.subject_id) {
    message.error('请先选择预算方案和预算科目')
    return
  }

  // 为所有部门生成预算明细
  const items = departmentList.value.map((dept, index) => ({
    key: index,
    department_id: dept.id,
    department_name: dept.name,
    budget_amount: 0
  }))

  batchItems.value = items
}

// 移除批量项
const removeBatchItem = (index: number) => {
  batchItems.value.splice(index, 1)
}

// 批量提交
const handleBatchSubmit = async () => {
  if (batchItems.value.length === 0) {
    message.error('请先生成预算明细')
    return
  }

  const validItems = batchItems.value.filter(item => item.budget_amount > 0)
  if (validItems.length === 0) {
    message.error('请至少为一个部门设置预算金额')
    return
  }

  batchLoading.value = true
  try {
    const items = validItems.map(item => ({
      scheme_id: batchForm.scheme_id!,
      subject_id: batchForm.subject_id!,
      department_id: item.department_id,
      budget_amount: item.budget_amount,
      description: ''
    }))

    await batchCreateBudgetItems(items)
    message.success(`批量创建成功，共创建 ${items.length} 条记录`)
    batchModalVisible.value = false
    loadItems()
  } catch (error) {
    message.error('批量创建失败')
  } finally {
    batchLoading.value = false
  }
}

// 刷新数据
const handleRefresh = () => {
  loadItems()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    modalLoading.value = true

    if (editingId.value) {
      await updateBudgetItem(editingId.value, formData)
      message.success('更新成功')
    } else {
      await createBudgetItem(formData)
      message.success('创建成功')
    }

    modalVisible.value = false
    loadItems()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    modalLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    scheme_id: 0,
    subject_id: 0,
    department_id: 0,
    budget_amount: 0,
    description: ''
  })
  formRef.value?.resetFields()
}

onMounted(() => {
  loadBaseData()
  loadItems()
})
</script>

<style scoped>
.budget-items-page {
  padding: 24px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.amount-text {
  font-weight: 500;
}

.amount-text.used {
  color: #f5222d;
}

.amount-text.frozen {
  color: #faad14;
}

.amount-text.available {
  color: #52c41a;
}

.batch-form {
  margin-bottom: 16px;
}

.batch-table {
  margin-top: 16px;
}
</style>
