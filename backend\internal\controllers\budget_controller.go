package controllers

import (
	"hospital-management/internal/dto"
	"hospital-management/internal/services"
	"hospital-management/pkg/logger"
	customValidator "hospital-management/pkg/validator"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
)

type BudgetController struct {
	budgetService *services.BudgetService
	validator     *validator.Validate
}

func NewBudgetController(budgetService *services.BudgetService) *BudgetController {
	return &BudgetController{
		budgetService: budgetService,
		validator:     customValidator.NewValidator(),
	}
}

// ===== 预算方案管理 =====

// GetBudgetSchemes 获取预算方案列表
// @Summary 获取预算方案列表
// @Description 获取预算方案列表，支持分页、搜索和筛选
// @Tags 预算管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param keyword query string false "搜索关键词"
// @Param year query int false "年度"
// @Param status query string false "状态" Enums(DRAFT, ACTIVE, CLOSED)
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /budget/schemes [get]
func (c *BudgetController) GetBudgetSchemes(ctx *gin.Context) {
	var req dto.BudgetSchemeListRequest

	// 设置默认值
	req.Page = 1
	req.PageSize = 10

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	schemes, total, err := c.budgetService.GetBudgetSchemes(&req)
	if err != nil {
		logger.Error("Failed to get budget schemes:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取预算方案列表失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      schemes,
			"total":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	})
}

// CreateBudgetScheme 创建预算方案
// @Summary 创建预算方案
// @Description 创建新的预算方案
// @Tags 预算管理
// @Accept json
// @Produce json
// @Param request body dto.CreateBudgetSchemeRequest true "创建预算方案请求"
// @Success 201 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /budget/schemes [post]
func (c *BudgetController) CreateBudgetScheme(ctx *gin.Context) {
	var req dto.CreateBudgetSchemeRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	scheme, err := c.budgetService.CreateBudgetScheme(&req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to create budget scheme:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建预算方案失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "创建成功",
		"data":    scheme,
	})
}

// UpdateBudgetScheme 更新预算方案
// @Summary 更新预算方案
// @Description 更新预算方案信息
// @Tags 预算管理
// @Accept json
// @Produce json
// @Param id path string true "预算方案ID"
// @Param request body dto.UpdateBudgetSchemeRequest true "更新预算方案请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "预算方案不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /budget/schemes/{id} [put]
func (c *BudgetController) UpdateBudgetScheme(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的预算方案ID",
		})
		return
	}

	var req dto.UpdateBudgetSchemeRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	scheme, err := c.budgetService.UpdateBudgetScheme(id, &req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to update budget scheme:", err)
		if err.Error() == "预算方案不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "更新预算方案失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    scheme,
	})
}

// DeleteBudgetScheme 删除预算方案
// @Summary 删除预算方案
// @Description 删除预算方案（软删除）
// @Tags 预算管理
// @Accept json
// @Produce json
// @Param id path string true "预算方案ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "预算方案不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /budget/schemes/{id} [delete]
func (c *BudgetController) DeleteBudgetScheme(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的预算方案ID",
		})
		return
	}

	// 调用服务
	if err := c.budgetService.DeleteBudgetScheme(id); err != nil {
		logger.Error("Failed to delete budget scheme:", err)
		if err.Error() == "预算方案不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "删除预算方案失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// ===== 预算科目管理 =====

// GetBudgetSubjects 获取预算科目列表
// @Summary 获取预算科目列表
// @Description 获取预算科目列表，支持分页、搜索和筛选
// @Tags 预算管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param keyword query string false "搜索关键词"
// @Param type query string false "科目类型" Enums(INCOME, EXPENSE)
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /budget/subjects [get]
func (c *BudgetController) GetBudgetSubjects(ctx *gin.Context) {
	var req dto.BudgetSubjectListRequest

	// 设置默认值
	req.Page = 1
	req.PageSize = 10

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	subjects, total, err := c.budgetService.GetBudgetSubjects(&req)
	if err != nil {
		logger.Error("Failed to get budget subjects:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取预算科目列表失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      subjects,
			"total":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	})
}

// GetBudgetSubjectTree 获取预算科目树形结构
// @Summary 获取预算科目树形结构
// @Description 获取预算科目的树形结构
// @Tags 预算管理
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "success"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /budget/subjects/tree [get]
func (c *BudgetController) GetBudgetSubjectTree(ctx *gin.Context) {
	tree, err := c.budgetService.GetBudgetSubjectTree()
	if err != nil {
		logger.Error("Failed to get budget subject tree:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取预算科目树形结构失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    tree,
	})
}

// CreateBudgetSubject 创建预算科目
// @Summary 创建预算科目
// @Description 创建新的预算科目
// @Tags 预算管理
// @Accept json
// @Produce json
// @Param request body dto.CreateBudgetSubjectRequest true "创建预算科目请求"
// @Success 201 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /budget/subjects [post]
func (c *BudgetController) CreateBudgetSubject(ctx *gin.Context) {
	var req dto.CreateBudgetSubjectRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	subject, err := c.budgetService.CreateBudgetSubject(&req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to create budget subject:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建预算科目失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "创建成功",
		"data":    subject,
	})
}

// UpdateBudgetSubject 更新预算科目
// @Summary 更新预算科目
// @Description 更新预算科目信息
// @Tags 预算管理
// @Accept json
// @Produce json
// @Param id path string true "预算科目ID"
// @Param request body dto.UpdateBudgetSubjectRequest true "更新预算科目请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "预算科目不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /budget/subjects/{id} [put]
func (c *BudgetController) UpdateBudgetSubject(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的预算科目ID",
		})
		return
	}

	var req dto.UpdateBudgetSubjectRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	subject, err := c.budgetService.UpdateBudgetSubject(id, &req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to update budget subject:", err)
		if err.Error() == "预算科目不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "更新预算科目失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    subject,
	})
}

// ===== 预算明细管理 =====

// GetBudgetItems 获取预算明细列表
// @Summary 获取预算明细列表
// @Description 获取预算明细列表，支持分页和筛选
// @Tags 预算管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param scheme_id query string false "预算方案ID"
// @Param department_id query string false "部门ID"
// @Param subject_id query string false "科目ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /budget/items [get]
func (c *BudgetController) GetBudgetItems(ctx *gin.Context) {
	var req dto.BudgetItemListRequest

	// 设置默认值
	req.Page = 1
	req.PageSize = 10

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	items, total, err := c.budgetService.GetBudgetItems(&req)
	if err != nil {
		logger.Error("Failed to get budget items:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取预算明细列表失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      items,
			"total":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	})
}

// CreateBudgetItem 创建预算明细
// @Summary 创建预算明细
// @Description 创建新的预算明细
// @Tags 预算管理
// @Accept json
// @Produce json
// @Param request body dto.CreateBudgetItemRequest true "创建预算明细请求"
// @Success 201 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /budget/items [post]
func (c *BudgetController) CreateBudgetItem(ctx *gin.Context) {
	var req dto.CreateBudgetItemRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	item, err := c.budgetService.CreateBudgetItem(&req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to create budget item:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建预算明细失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "创建成功",
		"data":    item,
	})
}

// UpdateBudgetItem 更新预算明细
// @Summary 更新预算明细
// @Description 更新预算明细信息
// @Tags 预算管理
// @Accept json
// @Produce json
// @Param id path string true "预算明细ID"
// @Param request body dto.UpdateBudgetItemRequest true "更新预算明细请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "预算明细不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /budget/items/{id} [put]
func (c *BudgetController) UpdateBudgetItem(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的预算明细ID",
		})
		return
	}

	var req dto.UpdateBudgetItemRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	item, err := c.budgetService.UpdateBudgetItem(id, &req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to update budget item:", err)
		if err.Error() == "预算明细不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "更新预算明细失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    item,
	})
}

// BatchCreateBudgetItems 批量创建预算明细
// @Summary 批量创建预算明细
// @Description 批量创建预算明细
// @Tags 预算管理
// @Accept json
// @Produce json
// @Param request body dto.BatchCreateBudgetItemRequest true "批量创建预算明细请求"
// @Success 201 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /budget/items/batch [post]
func (c *BudgetController) BatchCreateBudgetItems(ctx *gin.Context) {
	var req dto.BatchCreateBudgetItemRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	items, err := c.budgetService.BatchCreateBudgetItems(&req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to batch create budget items:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "批量创建预算明细失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "批量创建成功",
		"data":    items,
	})
}

// ===== 预算控制管理 =====

// FreezeBudget 冻结预算额度
// @Summary 冻结预算额度
// @Description 冻结指定预算明细的额度
// @Tags 预算管理
// @Accept json
// @Produce json
// @Param request body dto.BudgetFreezeRequest true "冻结预算请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /budget/freeze [post]
func (c *BudgetController) FreezeBudget(ctx *gin.Context) {
	var req dto.BudgetFreezeRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	if err := c.budgetService.FreezeBudget(&req, userID.(uuid.UUID)); err != nil {
		logger.Error("Failed to freeze budget:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "冻结预算额度失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "冻结成功",
	})
}

// UnfreezeBudget 解冻预算额度
// @Summary 解冻预算额度
// @Description 解冻指定预算明细的额度
// @Tags 预算管理
// @Accept json
// @Produce json
// @Param request body dto.BudgetUnfreezeRequest true "解冻预算请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /budget/unfreeze [post]
func (c *BudgetController) UnfreezeBudget(ctx *gin.Context) {
	var req dto.BudgetUnfreezeRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	if err := c.budgetService.UnfreezeBudget(&req, userID.(uuid.UUID)); err != nil {
		logger.Error("Failed to unfreeze budget:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "解冻预算额度失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "解冻成功",
	})
}

// ConsumeBudget 消费预算额度
// @Summary 消费预算额度
// @Description 消费指定预算明细的额度
// @Tags 预算管理
// @Accept json
// @Produce json
// @Param request body dto.BudgetConsumeRequest true "消费预算请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /budget/consume [post]
func (c *BudgetController) ConsumeBudget(ctx *gin.Context) {
	var req dto.BudgetConsumeRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	if err := c.budgetService.ConsumeBudget(&req, userID.(uuid.UUID)); err != nil {
		logger.Error("Failed to consume budget:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "消费预算额度失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "消费成功",
	})
}

// GetBudgetBalance 获取预算余额
// @Summary 获取预算余额
// @Description 获取指定预算明细的余额信息
// @Tags 预算管理
// @Accept json
// @Produce json
// @Param itemId path string true "预算明细ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "预算明细不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /budget/balance/{itemId} [get]
func (c *BudgetController) GetBudgetBalance(ctx *gin.Context) {
	itemIdStr := ctx.Param("itemId")
	itemId, err := uuid.Parse(itemIdStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的预算明细ID",
		})
		return
	}

	// 调用服务
	balance, err := c.budgetService.GetBudgetBalance(itemId)
	if err != nil {
		logger.Error("Failed to get budget balance:", err)
		if err.Error() == "预算明细不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "获取预算余额失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    balance,
	})
}

// ===== 预算分析管理 =====

// GetBudgetExecutionAnalysis 获取预算执行分析
// @Summary 获取预算执行分析
// @Description 获取预算执行分析数据
// @Tags 预算管理
// @Accept json
// @Produce json
// @Param scheme_id query string false "预算方案ID"
// @Param department_id query string false "部门ID"
// @Param subject_id query string false "科目ID"
// @Param start_date query string false "开始日期"
// @Param end_date query string false "结束日期"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /budget/analysis/execution [get]
func (c *BudgetController) GetBudgetExecutionAnalysis(ctx *gin.Context) {
	var req dto.BudgetExecutionAnalysisRequest

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	analysis, err := c.budgetService.GetBudgetExecutionAnalysis(&req)
	if err != nil {
		logger.Error("Failed to get budget execution analysis:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取预算执行分析失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    analysis,
	})
}

// GetBudgetDepartmentAnalysis 获取部门预算分析
// @Summary 获取部门预算分析
// @Description 获取部门预算分析数据
// @Tags 预算管理
// @Accept json
// @Produce json
// @Param scheme_id query string false "预算方案ID"
// @Param department_id query string false "部门ID"
// @Param subject_id query string false "科目ID"
// @Param start_date query string false "开始日期"
// @Param end_date query string false "结束日期"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /budget/analysis/department [get]
func (c *BudgetController) GetBudgetDepartmentAnalysis(ctx *gin.Context) {
	var req dto.BudgetExecutionAnalysisRequest

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	analysis, err := c.budgetService.GetBudgetDepartmentAnalysis(&req)
	if err != nil {
		logger.Error("Failed to get budget department analysis:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取部门预算分析失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    analysis,
	})
}

// GetBudgetSubjectAnalysis 获取科目预算分析
// @Summary 获取科目预算分析
// @Description 获取科目预算分析数据
// @Tags 预算管理
// @Accept json
// @Produce json
// @Param scheme_id query string false "预算方案ID"
// @Param department_id query string false "部门ID"
// @Param subject_id query string false "科目ID"
// @Param start_date query string false "开始日期"
// @Param end_date query string false "结束日期"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /budget/analysis/subject [get]
func (c *BudgetController) GetBudgetSubjectAnalysis(ctx *gin.Context) {
	var req dto.BudgetExecutionAnalysisRequest

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	analysis, err := c.budgetService.GetBudgetSubjectAnalysis(&req)
	if err != nil {
		logger.Error("Failed to get budget subject analysis:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取科目预算分析失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    analysis,
	})
}
