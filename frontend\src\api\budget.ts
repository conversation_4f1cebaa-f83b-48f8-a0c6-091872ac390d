import request from './request'

// 预算方案相关接口
export interface BudgetScheme {
  id: string
  name: string
  year: number
  control_rule: 'FLEXIBLE' | 'RIGID'
  status: 'DRAFT' | 'ACTIVE' | 'CLOSED'
  created_at: string
  updated_at: string
}

export interface BudgetSchemeForm {
  name: string
  year: number
  control_rule: 'FLEXIBLE' | 'RIGID'
  status: 'DRAFT' | 'ACTIVE' | 'CLOSED'
}

// 获取预算方案列表
export const getBudgetSchemes = (params?: {
  page?: number
  page_size?: number
  keyword?: string
  year?: number
  status?: string
}): Promise<{
  list: BudgetScheme[]
  total: number
  page: number
  page_size: number
}> => {
  return request.get('/api/v1/budget/schemes', { params })
}

// 创建预算方案
export const createBudgetScheme = (data: BudgetSchemeForm): Promise<BudgetScheme> => {
  return request.post('/api/v1/budget/schemes', data)
}

// 更新预算方案
export const updateBudgetScheme = (id: string, data: BudgetSchemeForm): Promise<BudgetScheme> => {
  return request.put(`/api/v1/budget/schemes/${id}`, data)
}

// 删除预算方案
export const deleteBudgetScheme = (id: string): Promise<void> => {
  return request.delete(`/api/v1/budget/schemes/${id}`)
}

// 预算科目相关接口
export interface BudgetSubject {
  id: string
  parent_id?: string
  name: string
  code: string
  type: 'INCOME' | 'EXPENSE'
  accounting_subject_code?: string
  level: number
  created_at: string
  updated_at: string
  children?: BudgetSubject[]
}

export interface BudgetSubjectForm {
  parent_id?: string
  name: string
  code: string
  type: 'INCOME' | 'EXPENSE'
  accounting_subject_code?: string
}

// 获取预算科目列表
export const getBudgetSubjects = (): Promise<BudgetSubject[]> => {
  return request.get('/api/v1/budget/subjects')
}

// 获取预算科目树形结构
export const getBudgetSubjectTree = (): Promise<BudgetSubject[]> => {
  return request.get('/api/v1/budget/subjects/tree')
}

// 创建预算科目
export const createBudgetSubject = (data: BudgetSubjectForm): Promise<BudgetSubject> => {
  return request.post('/api/v1/budget/subjects', data)
}

// 更新预算科目
export const updateBudgetSubject = (id: string, data: BudgetSubjectForm): Promise<BudgetSubject> => {
  return request.put(`/api/v1/budget/subjects/${id}`, data)
}

// 预算明细相关接口
export interface BudgetItem {
  id: string
  scheme_id: string
  scheme_name: string
  subject_id: string
  subject_name: string
  department_id: string
  department_name: string
  budget_amount: number
  used_amount: number
  frozen_amount: number
  available_amount: number
  execution_rate: number
  created_at: string
  updated_at: string
}

export interface BudgetItemForm {
  scheme_id: string
  subject_id: string
  department_id: string
  budget_amount: number
  description?: string
}

export interface BudgetItemQuery {
  scheme_id?: string
  subject_id?: string
  department_id?: string
  page?: number
  page_size?: number
}

// 获取预算明细列表
export const getBudgetItems = (params: BudgetItemQuery): Promise<{
  list: BudgetItem[]
  total: number
  page: number
  page_size: number
}> => {
  return request.get('/api/v1/budget/items', { params })
}

// 创建预算明细
export const createBudgetItem = (data: BudgetItemForm): Promise<BudgetItem> => {
  return request.post('/api/v1/budget/items', data)
}

// 更新预算明细
export const updateBudgetItem = (id: string, data: BudgetItemForm): Promise<BudgetItem> => {
  return request.put(`/api/v1/budget/items/${id}`, data)
}

// 批量创建预算明细
export const batchCreateBudgetItems = (data: BudgetItemForm[]): Promise<BudgetItem[]> => {
  return request.post('/api/v1/budget/items/batch', { items: data })
}

// 预算控制相关接口
export interface BudgetControlRequest {
  item_id: string
  amount: number
  business_type: string
  business_id: string
  description?: string
}

// 冻结预算额度
export const freezeBudget = (data: BudgetControlRequest): Promise<void> => {
  return request.post('/api/v1/budget/freeze', data)
}

// 解冻预算额度
export const unfreezeBudget = (data: BudgetControlRequest): Promise<void> => {
  return request.post('/api/v1/budget/unfreeze', data)
}

// 消费预算额度
export const consumeBudget = (data: BudgetControlRequest): Promise<void> => {
  return request.post('/api/v1/budget/consume', data)
}

// 获取预算余额
export const getBudgetBalance = (itemId: string): Promise<{
  budget_amount: number
  used_amount: number
  frozen_amount: number
  available_amount: number
}> => {
  return request.get(`/api/v1/budget/balance/${itemId}`)
}

// 预算分析相关接口
export interface BudgetAnalysisQuery {
  scheme_id?: string
  department_id?: string
  subject_id?: string
  start_date?: string
  end_date?: string
}

export interface BudgetExecutionAnalysis {
  department_name: string
  subject_name: string
  budget_amount: number
  used_amount: number
  execution_rate: number
  remaining_amount: number
}

// 预算执行分析
export const getBudgetExecutionAnalysis = (params: BudgetAnalysisQuery): Promise<BudgetExecutionAnalysis[]> => {
  return request.get('/api/v1/budget/analysis/execution', { params })
}

// 部门预算分析
export const getDepartmentBudgetAnalysis = (params: BudgetAnalysisQuery): Promise<BudgetExecutionAnalysis[]> => {
  return request.get('/api/v1/budget/analysis/department', { params })
}

// 科目预算分析
export const getSubjectBudgetAnalysis = (params: BudgetAnalysisQuery): Promise<BudgetExecutionAnalysis[]> => {
  return request.get('/api/v1/budget/analysis/subject', { params })
}

// 预算分析综合数据
export interface BudgetAnalysisData {
  overview: {
    total_budget: number
    executed_amount: number
    execution_rate: number
    remaining_budget: number
  }
  details: BudgetExecutionAnalysis[]
  execution_comparison: Array<{
    department_name: string
    execution_rate: number
  }>
}

// 获取预算分析数据
export const getBudgetAnalysis = (params: BudgetAnalysisQuery): Promise<BudgetAnalysisData> => {
  return request.get('/api/v1/budget/analysis', { params })
}

// 获取预算趋势分析
export const getBudgetTrendAnalysis = (params: BudgetAnalysisQuery): Promise<Array<{
  period: string
  budget_amount: number
  executed_amount: number
  execution_rate: number
}>> => {
  return request.get('/api/v1/budget/analysis/trend', { params })
}

// 获取预算预警
export const getBudgetWarnings = (params: BudgetAnalysisQuery): Promise<Array<{
  warning_type: 'over_budget' | 'near_limit' | 'low_usage'
  department_name: string
  subject_name: string
  budget_amount: number
  used_amount: number
  execution_rate: number
  warning_message: string
}>> => {
  return request.get('/api/v1/budget/warnings', { params })
}
