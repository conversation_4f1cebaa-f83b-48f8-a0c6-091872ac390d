package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// AssetCategory 资产分类表 - 对应 tbl_asset_categories
type AssetCategory struct {
	BaseModel
	ParentID           *uuid.UUID `json:"parent_id,omitempty" gorm:"type:uuid"`
	Name               string     `json:"name" gorm:"type:varchar(100);not null" validate:"required,max=100"`
	Code               string     `json:"code" gorm:"type:varchar(50);unique;not null" validate:"required,max=50"`
	DepreciationMethod *string    `json:"depreciation_method,omitempty" gorm:"type:varchar(20);default:'straight_line'"`
	DepreciationYears  *int       `json:"depreciation_years,omitempty" gorm:"default:10"`
	Description        *string    `json:"description,omitempty" gorm:"type:text"`

	// 关联关系
	Parent   *AssetCategory  `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	Children []AssetCategory `json:"children,omitempty" gorm:"foreignKey:ParentID"`
	Assets   []Asset         `json:"assets,omitempty" gorm:"foreignKey:CategoryID"`
}

// TableName 指定表名
func (AssetCategory) TableName() string {
	return "tbl_asset_categories"
}

// Asset 资产卡片表 - 对应 tbl_assets
type Asset struct {
	BaseModel
	AssetCode               string           `json:"asset_code" gorm:"type:varchar(50);unique;not null" validate:"required,max=50"`
	AssetName               string           `json:"asset_name" gorm:"type:varchar(255);not null" validate:"required,max=255"`
	CategoryID              uuid.UUID        `json:"category_id" gorm:"type:uuid;not null" validate:"required"`
	Specification           *string          `json:"specification,omitempty" gorm:"type:varchar(500)"`
	Model                   *string          `json:"model,omitempty" gorm:"type:varchar(100)"`
	Brand                   *string          `json:"brand,omitempty" gorm:"type:varchar(100)"`
	SupplierID              *uuid.UUID       `json:"supplier_id,omitempty" gorm:"type:uuid"`
	SourceType              *string          `json:"source_type,omitempty" gorm:"type:varchar(30)"`
	PurchaseContractID      *uuid.UUID       `json:"purchase_contract_id,omitempty" gorm:"type:uuid"`
	PurchaseDate            *time.Time       `json:"purchase_date,omitempty" gorm:"type:date"`
	PurchasePrice           *decimal.Decimal `json:"purchase_price,omitempty" gorm:"type:decimal(18,2)"`
	OriginalValue           *decimal.Decimal `json:"original_value,omitempty" gorm:"type:decimal(18,2)"`
	CurrentValue            *decimal.Decimal `json:"current_value,omitempty" gorm:"type:decimal(18,2)"`
	AccumulatedDepreciation *decimal.Decimal `json:"accumulated_depreciation,omitempty" gorm:"type:decimal(18,2);default:0"`
	DepreciationRate        *decimal.Decimal `json:"depreciation_rate,omitempty" gorm:"type:decimal(5,4);default:0"`
	UsefulLife              *int             `json:"useful_life,omitempty"`
	Status                  string           `json:"status" gorm:"type:varchar(30);not null" validate:"required,max=30"`
	OwnerDeptID             uuid.UUID        `json:"owner_dept_id" gorm:"type:uuid;not null" validate:"required"`
	CustodianID             *uuid.UUID       `json:"custodian_id,omitempty" gorm:"type:uuid"`
	Location                *string          `json:"location,omitempty" gorm:"type:varchar(255)"`
	WarrantyDate            *time.Time       `json:"warranty_date,omitempty" gorm:"type:date"`
	MaintenanceCycle        *int             `json:"maintenance_cycle,omitempty"`
	LastMaintenanceDate     *time.Time       `json:"last_maintenance_date,omitempty" gorm:"type:date"`
	NextMaintenanceDate     *time.Time       `json:"next_maintenance_date,omitempty" gorm:"type:date"`
	Remark                  *string          `json:"remark,omitempty" gorm:"type:text"`

	// 关联关系
	Category         *AssetCategory `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
	Supplier         *Supplier      `json:"supplier,omitempty" gorm:"foreignKey:SupplierID"`
	PurchaseContract *Contract      `json:"purchase_contract,omitempty" gorm:"foreignKey:PurchaseContractID"`
	OwnerDept        *Department    `json:"owner_dept,omitempty" gorm:"foreignKey:OwnerDeptID"`
	Custodian        *User          `json:"custodian,omitempty" gorm:"foreignKey:CustodianID"`
}

// TableName 指定表名
func (Asset) TableName() string {
	return "tbl_assets"
}

// AssetDepreciationRecord 资产折旧记录表 - 对应 tbl_asset_depreciation_records
type AssetDepreciationRecord struct {
	BaseModel
	AssetID                 uuid.UUID       `json:"asset_id" gorm:"type:uuid;not null" validate:"required"`
	Period                  string          `json:"period" gorm:"type:varchar(7);not null" validate:"required,max=7"` // YYYY-MM
	DepreciationAmount      decimal.Decimal `json:"depreciation_amount" gorm:"type:decimal(18,2);not null" validate:"required"`
	AccumulatedDepreciation decimal.Decimal `json:"accumulated_depreciation" gorm:"type:decimal(18,2);not null" validate:"required"`
	BookValue               decimal.Decimal `json:"book_value" gorm:"type:decimal(18,2);not null" validate:"required"`
	DepreciationMethod      string          `json:"depreciation_method" gorm:"type:varchar(20);not null" validate:"required,max=20"`
	CalculationBasis        *string         `json:"calculation_basis,omitempty" gorm:"type:text"`
	Remark                  *string         `json:"remark,omitempty" gorm:"type:text"`

	// 关联关系
	Asset   *Asset `json:"asset,omitempty" gorm:"foreignKey:AssetID"`
	Creator *User  `json:"creator,omitempty" gorm:"foreignKey:CreatedBy"`
}

// TableName 指定表名
func (AssetDepreciationRecord) TableName() string {
	return "tbl_asset_depreciation_records"
}

// AssetMaintenanceRecord 资产维护记录表 - 对应 tbl_asset_maintenance_records
type AssetMaintenanceRecord struct {
	BaseModel
	AssetID             uuid.UUID       `json:"asset_id" gorm:"type:uuid;not null" validate:"required"`
	MaintenanceType     string          `json:"maintenance_type" gorm:"type:varchar(20);not null" validate:"required,max=20"`
	MaintenanceDate     time.Time       `json:"maintenance_date" gorm:"type:date;not null" validate:"required"`
	MaintenanceCost     decimal.Decimal `json:"maintenance_cost" gorm:"type:decimal(18,2);not null;default:0" validate:"required"`
	MaintenanceCompany  *string         `json:"maintenance_company,omitempty" gorm:"type:varchar(200)"`
	MaintenancePerson   *string         `json:"maintenance_person,omitempty" gorm:"type:varchar(100)"`
	Description         string          `json:"description" gorm:"type:text;not null" validate:"required"`
	NextMaintenanceDate *time.Time      `json:"next_maintenance_date,omitempty" gorm:"type:date"`
	Status              string          `json:"status" gorm:"type:varchar(20);not null;default:'planned'" validate:"required,max=20"`
	AttachmentUrls      *string         `json:"attachment_urls,omitempty" gorm:"type:jsonb"`
	Remark              *string         `json:"remark,omitempty" gorm:"type:text"`

	// 关联关系
	Asset   *Asset `json:"asset,omitempty" gorm:"foreignKey:AssetID"`
	Creator *User  `json:"creator,omitempty" gorm:"foreignKey:CreatedBy"`
}

// TableName 指定表名
func (AssetMaintenanceRecord) TableName() string {
	return "tbl_asset_maintenance_records"
}

// AssetChangeRecord 资产变更记录表 - 对应 tbl_asset_change_records
type AssetChangeRecord struct {
	BaseModel
	AssetID        uuid.UUID  `json:"asset_id" gorm:"type:uuid;not null" validate:"required"`
	ChangeType     string     `json:"change_type" gorm:"type:varchar(30);not null" validate:"required,max=30"`
	OldValue       *string    `json:"old_value,omitempty" gorm:"type:text"`
	NewValue       *string    `json:"new_value,omitempty" gorm:"type:text"`
	ChangeReason   *string    `json:"change_reason,omitempty" gorm:"type:varchar(500)"`
	ApprovalStatus string     `json:"approval_status" gorm:"type:varchar(20);not null;default:'pending'" validate:"required,max=20"`
	ApprovedBy     *uuid.UUID `json:"approved_by,omitempty" gorm:"type:uuid"`
	ApprovedAt     *time.Time `json:"approved_at,omitempty" gorm:"type:timestamptz"`
	EffectiveDate  *time.Time `json:"effective_date,omitempty" gorm:"type:date"`
	Remark         *string    `json:"remark,omitempty" gorm:"type:text"`

	// 关联关系
	Asset    *Asset `json:"asset,omitempty" gorm:"foreignKey:AssetID"`
	Approver *User  `json:"approver,omitempty" gorm:"foreignKey:ApprovedBy"`
}

// TableName 指定表名
func (AssetChangeRecord) TableName() string {
	return "tbl_asset_change_records"
}
