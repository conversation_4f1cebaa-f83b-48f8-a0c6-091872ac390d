# 医院内部控制与运营管理系统 - API实现进度

## 模块一：统一支撑平台 (Foundation Platform)

### 1.1 组织权限管理

#### 部门管理API
- [x] `GET /api/v1/departments` - 获取部门列表（支持树形结构）
- [x] `POST /api/v1/departments` - 创建部门
- [x] `PUT /api/v1/departments/:id` - 更新部门信息
- [x] `DELETE /api/v1/departments/:id` - 删除部门
- [x] `GET /api/v1/departments/tree` - 获取部门树形结构

#### 用户管理API
- [x] `GET /api/v1/users` - 获取用户列表（分页、搜索、筛选）
- [x] `POST /api/v1/users` - 创建用户
- [x] `GET /api/v1/users/:id` - 获取用户详情
- [x] `PUT /api/v1/users/:id` - 更新用户信息
- [x] `DELETE /api/v1/users/:id` - 删除用户
- [x] `POST /api/v1/users/import` - 批量导入用户
- [x] `GET /api/v1/users/export` - 导出用户数据

#### 角色管理API
- [x] `GET /api/v1/roles` - 获取角色列表
- [x] `POST /api/v1/roles` - 创建角色
- [x] `PUT /api/v1/roles/:id` - 更新角色
- [x] `DELETE /api/v1/roles/:id` - 删除角色
- [x] `POST /api/v1/roles/:id/permissions` - 分配权限

#### 认证授权API
- [x] `POST /api/v1/auth/login` - 用户登录
- [x] `POST /api/v1/auth/logout` - 用户登出
- [x] `POST /api/v1/auth/refresh` - 刷新Token
- [x] `GET /api/v1/auth/profile` - 获取当前用户信息

### 1.2 工作流引擎
#### 审批流API
- [x] `POST /api/v1/approval/flows` - 创建审批流实例
- [x] `GET /api/v1/approval/flows/:id` - 获取审批流详情
- [x] `PUT /api/v1/approval/flows/:id/status` - 更新审批流状态
- [x] `GET /api/v1/approval/todo` - 获取待办任务
- [x] `POST /api/v1/approval/approve/:nodeId` - 审批操作
- [x] `POST /api/v1/approval/reject/:nodeId` - 驳回操作
- [x] `POST /api/v1/approval/transfer/:nodeId` - 转办操作

### 1.3 消息中心
#### 消息API
- [x] `GET /api/v1/messages` - 获取消息列表
- [x] `POST /api/v1/messages` - 发送消息
- [x] `PUT /api/v1/messages/:id/read` - 标记已读
- [x] `GET /api/v1/messages/unread-count` - 获取未读消息数量

### 1.4 文件管理
#### 文件API
- [x] `POST /api/v1/files/upload` - 文件上传
- [x] `GET /api/v1/files/:id/download` - 文件下载
- [x] `DELETE /api/v1/files/:id` - 删除文件
- [x] `GET /api/v1/files/business/:businessId` - 获取业务关联文件

## 模块二：全面预算管理

### 2.1 预算编制
#### 预算方案API
- [x] `GET /api/v1/budget/schemes` - 获取预算方案列表
- [x] `POST /api/v1/budget/schemes` - 创建预算方案
- [x] `PUT /api/v1/budget/schemes/:id` - 更新预算方案
- [x] `DELETE /api/v1/budget/schemes/:id` - 删除预算方案

#### 预算科目API
- [x] `GET /api/v1/budget/subjects` - 获取预算科目列表
- [x] `POST /api/v1/budget/subjects` - 创建预算科目
- [x] `PUT /api/v1/budget/subjects/:id` - 更新预算科目
- [x] `GET /api/v1/budget/subjects/tree` - 获取科目树形结构

#### 预算明细API
- [x] `GET /api/v1/budget/items` - 获取预算明细
- [x] `POST /api/v1/budget/items` - 创建预算明细
- [x] `PUT /api/v1/budget/items/:id` - 更新预算明细
- [x] `POST /api/v1/budget/items/batch` - 批量创建预算明细

### 2.2 预算控制
#### 预算控制API
- [x] `POST /api/v1/budget/freeze` - 冻结预算额度
- [x] `POST /api/v1/budget/unfreeze` - 解冻预算额度
- [x] `POST /api/v1/budget/consume` - 消费预算额度
- [x] `GET /api/v1/budget/balance/:itemId` - 获取预算余额

### 2.3 预算分析
#### 预算分析API
- [x] `GET /api/v1/budget/analysis/execution` - 预算执行分析
- [x] `GET /api/v1/budget/analysis/department` - 部门预算分析
- [x] `GET /api/v1/budget/analysis/subject` - 科目预算分析

## 模块三：支出控制管理

### 3.1 事前申请
#### 事前申请API
- [x] `GET /api/v1/pre-applications` - 获取事前申请列表
- [x] `POST /api/v1/pre-applications` - 创建事前申请
- [x] `GET /api/v1/pre-applications/:id` - 获取申请详情
- [x] `PUT /api/v1/pre-applications/:id` - 更新申请
- [x] `POST /api/v1/pre-applications/:id/submit` - 提交申请

### 3.2 费用报销
#### 报销申请API
- [x] `GET /api/v1/expense/applications` - 获取报销申请列表
- [x] `POST /api/v1/expense/applications` - 创建报销申请
- [x] `GET /api/v1/expense/applications/:id` - 获取报销详情
- [x] `PUT /api/v1/expense/applications/:id` - 更新报销申请
- [x] `POST /api/v1/expense/applications/:id/submit` - 提交报销申请
- [x] `GET /api/v1/expense/applications/my` - 获取我的报销记录

### 3.3 付款管理
#### 付款API
- [x] `GET /api/v1/payments` - 获取付款单列表
- [x] `POST /api/v1/payments` - 创建付款单
- [x] `PUT /api/v1/payments/:id/status` - 更新付款状态
- [x] `POST /api/v1/payments/batch` - 批量付款

## 模块四：采购管理

### 4.1 供应商管理
#### 供应商API
- [x] `GET /api/v1/suppliers` - 获取供应商列表
- [x] `POST /api/v1/suppliers` - 创建供应商
- [x] `PUT /api/v1/suppliers/:id` - 更新供应商信息
- [x] `PUT /api/v1/suppliers/:id/status` - 更新供应商状态

### 4.2 采购申请
#### 采购申请API
- [x] `GET /api/v1/purchase/requisitions` - 获取采购申请列表
- [x] `GET /api/v1/purchase/requisitions/:id` - 获取采购申请详情
- [x] `POST /api/v1/purchase/requisitions` - 创建采购申请
- [x] `PUT /api/v1/purchase/requisitions/:id` - 更新采购申请
- [x] `POST /api/v1/purchase/requisitions/:id/submit` - 提交采购申请

## 模块五：合同管理

### 5.1 合同管理
#### 合同API
- [x] `GET /api/v1/contracts` - 获取合同列表
- [x] `POST /api/v1/contracts` - 创建合同
- [x] `GET /api/v1/contracts/:id` - 获取合同详情
- [x] `PUT /api/v1/contracts/:id` - 更新合同
- [x] `PUT /api/v1/contracts/:id/status` - 更新合同状态
- [x] `DELETE /api/v1/contracts/:id` - 删除合同

#### 付款计划API
- [x] `GET /api/v1/payment-schedules` - 获取付款计划列表
- [x] `POST /api/v1/payment-schedules` - 创建付款计划
- [x] `PUT /api/v1/payment-schedules/:id` - 更新付款计划
- [x] `POST /api/v1/payment-schedules/batch` - 批量创建付款计划

## 模块六：资产管理

### 6.1 资产管理
#### 资产分类API
- [x] `GET /api/v1/asset/categories` - 获取资产分类
- [x] `GET /api/v1/asset/categories/tree` - 获取资产分类树形结构
- [x] `POST /api/v1/asset/categories` - 创建资产分类
- [x] `PUT /api/v1/asset/categories/:id` - 更新资产分类
- [x] `DELETE /api/v1/asset/categories/:id` - 删除资产分类

#### 资产API
- [x] `GET /api/v1/assets` - 获取资产列表
- [x] `GET /api/v1/assets/:id` - 获取资产详情
- [x] `POST /api/v1/assets` - 创建资产
- [x] `PUT /api/v1/assets/:id` - 更新资产信息
- [x] `PUT /api/v1/assets/:id/status` - 更新资产状态
- [x] `PUT /api/v1/assets/batch/status` - 批量更新资产状态
- [x] `DELETE /api/v1/assets/:id` - 删除资产

## 通用功能
#### 数据字典API
- [x] `GET /api/v1/dictionaries` - 获取数据字典

#### 统计报表API
- [x] `GET /api/v1/dashboard/statistics` - 获取仪表盘统计

#### 系统信息API
- [x] `GET /api/v1/system/info` - 获取系统信息
- [x] `GET /api/v1/health` - 健康检查
- [x] `GET /api/v1/version` - 获取版本信息

---
**说明**: 
- [x] 表示已完成
- [ ] 表示待实现
- 每完成一个接口，将对应的 [ ] 改为 [x]
