server:
  port: 8080
  host: "0.0.0.0"
  read_timeout: 30s
  write_timeout: 30s

database:
  host: "localhost"
  port: 5432
  user: "hospital_user"
  password: "your_secure_password_here"
  dbname: "hospital_management"
  sslmode: "require"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 300s

jwt:
  secret: "your-very-secure-jwt-secret-key-change-this-in-production"
  expire_hours: 8
  refresh_expire_hours: 168  # 7 days

cors:
  allowed_origins:
    - "https://your-domain.com"
    - "https://www.your-domain.com"
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allowed_headers:
    - "Content-Type"
    - "Authorization"
    - "X-Requested-With"
  allow_credentials: true

upload:
  max_size: 52428800  # 50MB
  allowed_types:
    - "image/jpeg"
    - "image/png"
    - "image/gif"
    - "application/pdf"
    - "application/msword"
    - "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    - "application/vnd.ms-excel"
    - "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  upload_dir: "/opt/hospital-management/uploads"

log:
  level: "warn"
  file: "/var/log/hospital-management.log"
  max_size: 100  # MB
  max_backups: 5
  max_age: 30  # days

security:
  rate_limit:
    enabled: true
    requests_per_minute: 60
  session_timeout: 3600  # 1 hour

# 邮件配置 (可选)
email:
  smtp_host: "smtp.your-email-provider.com"
  smtp_port: 587
  username: "<EMAIL>"
  password: "your-email-password"
  from_address: "<EMAIL>"
  from_name: "医院管理系统"
