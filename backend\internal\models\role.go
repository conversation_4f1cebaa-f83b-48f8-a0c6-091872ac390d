package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Permissions 权限结构体
type Permissions map[string]interface{}

// Value 实现 driver.Valuer 接口
func (p Permissions) Value() (driver.Value, error) {
	return json.Marshal(p)
}

// Scan 实现 sql.Scanner 接口
func (p *Permissions) Scan(value interface{}) error {
	if value == nil {
		*p = make(Permissions)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into Permissions", value)
	}

	return json.Unmarshal(bytes, p)
}

// Role 角色表 - 对应 tbl_roles
type Role struct {
	BaseModel
	RoleName      string       `json:"role_name" gorm:"type:varchar(100);not null;unique" validate:"required,max=100"`
	RoleCode      string       `json:"role_code" gorm:"type:varchar(50);not null;unique" validate:"required,max=50"`
	Description   *string      `json:"description,omitempty" gorm:"type:text"`
	Permissions   *Permissions `json:"permissions,omitempty" gorm:"type:jsonb"`
	PermissionStr *string      `json:"permission_str,omitempty" gorm:"type:text"` // 简单权限字符串，用于兼容
	IsActive      *bool        `json:"is_active,omitempty" gorm:"default:true"`

	// 关联关系
	Users []User `json:"users,omitempty" gorm:"many2many:tbl_user_roles;"`
}

// TableName 指定表名
func (Role) TableName() string {
	return "tbl_roles"
}

// UserRole 用户角色关联表 - 对应 tbl_user_roles
type UserRole struct {
	UserID string `json:"user_id" gorm:"type:uuid;primaryKey"`
	RoleID string `json:"role_id" gorm:"type:uuid;primaryKey"`
}

// TableName 指定表名
func (UserRole) TableName() string {
	return "tbl_user_roles"
}
