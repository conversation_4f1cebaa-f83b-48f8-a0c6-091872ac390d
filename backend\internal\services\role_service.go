package services

import (
	"errors"
	"hospital-management/internal/dto"
	"hospital-management/internal/models"
	"hospital-management/pkg/logger"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type RoleService struct {
	db *gorm.DB
}

func NewRoleService(db *gorm.DB) *RoleService {
	return &RoleService{db: db}
}

// GetRoles 获取角色列表
func (s *RoleService) GetRoles(req *dto.RoleListRequest) ([]dto.RoleResponse, int64, error) {
	var roles []models.Role
	var total int64

	query := s.db.Model(&models.Role{})

	// 搜索条件
	if req.Keyword != "" {
		query = query.Where("role_name ILIKE ? OR role_code ILIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}
	if req.IsActive != nil {
		query = query.Where("is_active = ?", *req.IsActive)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count roles:", err)
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Find(&roles).Error; err != nil {
		logger.Error("Failed to get roles:", err)
		return nil, 0, err
	}

	// 批量获取所有角色的用户数量
	roleIDs := make([]uuid.UUID, len(roles))
	for i, role := range roles {
		roleIDs[i] = role.ID
	}

	// 一次性查询所有角色的用户数量
	type RoleUserCount struct {
		RoleID    uuid.UUID `gorm:"column:role_id"`
		UserCount int64     `gorm:"column:user_count"`
	}
	var roleCounts []RoleUserCount
	s.db.Table("tbl_user_roles").
		Select("role_id, COUNT(user_id) as user_count").
		Where("role_id IN ?", roleIDs).
		Group("role_id").
		Find(&roleCounts)

	// 创建角色ID到用户数量的映射
	userCountMap := make(map[uuid.UUID]int64)
	for _, rc := range roleCounts {
		userCountMap[rc.RoleID] = rc.UserCount
	}

	// 转换为响应格式
	responses := make([]dto.RoleResponse, len(roles))
	for i, role := range roles {
		responses[i] = s.convertToResponse(role)
		responses[i].UserCount = userCountMap[role.ID] // 从映射中获取用户数量
	}

	return responses, total, nil
}

// GetRoleByID 根据ID获取角色
func (s *RoleService) GetRoleByID(id uuid.UUID) (*dto.RoleResponse, error) {
	var role models.Role
	if err := s.db.First(&role, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("角色不存在")
		}
		logger.Error("Failed to get role by ID:", err)
		return nil, err
	}

	response := s.convertToResponse(role)

	// 获取用户数量
	var userCount int64
	s.db.Model(&models.User{}).Joins("JOIN tbl_user_roles ON tbl_users.id = tbl_user_roles.user_id").
		Where("tbl_user_roles.role_id = ?", role.ID).Count(&userCount)
	response.UserCount = userCount

	return &response, nil
}

// CreateRole 创建角色
func (s *RoleService) CreateRole(req *dto.CreateRoleRequest, userID uuid.UUID) (*dto.RoleResponse, error) {
	// 检查角色名称是否重复
	var count int64
	if err := s.db.Model(&models.Role{}).Where("role_name = ?", req.RoleName).Count(&count).Error; err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.New("角色名称已存在")
	}

	// 检查角色代码是否重复
	if req.RoleCode != nil {
		if err := s.db.Model(&models.Role{}).Where("role_code = ?", *req.RoleCode).Count(&count).Error; err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, errors.New("角色代码已存在")
		}
	}

	// 创建角色
	role := models.Role{
		RoleName:      req.RoleName,
		RoleCode:      StringValue(req.RoleCode),
		Description:   req.Description,
		IsActive:      BoolPtr(req.IsActive),
		PermissionStr: StringPtr(strings.Join(req.Permissions, ",")),
	}
	role.CreatedBy = &userID

	if err := s.db.Create(&role).Error; err != nil {
		logger.Error("Failed to create role:", err)
		return nil, err
	}

	response := s.convertToResponse(role)
	return &response, nil
}

// UpdateRole 更新角色
func (s *RoleService) UpdateRole(id uuid.UUID, req *dto.UpdateRoleRequest, userID uuid.UUID) (*dto.RoleResponse, error) {
	var role models.Role
	if err := s.db.First(&role, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("角色不存在")
		}
		return nil, err
	}

	// 检查角色名称是否重复
	if req.RoleName != nil {
		var count int64
		if err := s.db.Model(&models.Role{}).Where("role_name = ? AND id != ?", *req.RoleName, id).Count(&count).Error; err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, errors.New("角色名称已存在")
		}
	}

	// 检查角色代码是否重复
	if req.RoleCode != nil {
		var count int64
		if err := s.db.Model(&models.Role{}).Where("role_code = ? AND id != ?", *req.RoleCode, id).Count(&count).Error; err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, errors.New("角色代码已存在")
		}
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.RoleName != nil {
		updates["role_name"] = *req.RoleName
	}
	if req.RoleCode != nil {
		updates["role_code"] = *req.RoleCode
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}
	if req.Permissions != nil {
		// 更新权限字符串字段
		permissionStr := strings.Join(req.Permissions, ",")
		updates["permission_str"] = &permissionStr
	}
	updates["updated_by"] = userID

	if err := s.db.Model(&role).Updates(updates).Error; err != nil {
		logger.Error("Failed to update role:", err)
		return nil, err
	}

	// 重新获取更新后的角色
	if err := s.db.First(&role, id).Error; err != nil {
		logger.Error("Failed to get updated role:", err)
		return nil, err
	}

	response := s.convertToResponse(role)
	return &response, nil
}

// DeleteRole 删除角色
func (s *RoleService) DeleteRole(id uuid.UUID) error {
	var role models.Role
	if err := s.db.First(&role, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("角色不存在")
		}
		return err
	}

	// 检查是否有用户使用该角色
	var userCount int64
	if err := s.db.Model(&models.User{}).Joins("JOIN tbl_user_roles ON tbl_users.id = tbl_user_roles.user_id").
		Where("tbl_user_roles.role_id = ?", id).Count(&userCount).Error; err != nil {
		return err
	}
	if userCount > 0 {
		return errors.New("该角色下存在用户，无法删除")
	}

	// 软删除角色
	if err := s.db.Delete(&role).Error; err != nil {
		logger.Error("Failed to delete role:", err)
		return err
	}

	return nil
}

// AssignPermissions 分配权限
func (s *RoleService) AssignPermissions(id uuid.UUID, req *dto.AssignPermissionsRequest, userID uuid.UUID) error {
	var role models.Role
	if err := s.db.First(&role, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("角色不存在")
		}
		return err
	}

	// 更新权限
	updates := map[string]interface{}{
		"permissions": strings.Join(req.Permissions, ","),
		"updated_by":  userID,
	}

	if err := s.db.Model(&role).Updates(updates).Error; err != nil {
		logger.Error("Failed to assign permissions:", err)
		return err
	}

	return nil
}

// GetAvailablePermissions 获取可用权限列表
func (s *RoleService) GetAvailablePermissions() []dto.PermissionResponse {
	// 这里应该从配置文件或数据库中获取权限列表
	// 暂时返回硬编码的权限列表
	return []dto.PermissionResponse{
		{Code: "user:read", Name: "查看用户", Description: "查看用户信息", Category: "用户管理"},
		{Code: "user:create", Name: "创建用户", Description: "创建新用户", Category: "用户管理"},
		{Code: "user:update", Name: "更新用户", Description: "更新用户信息", Category: "用户管理"},
		{Code: "user:delete", Name: "删除用户", Description: "删除用户", Category: "用户管理"},
		{Code: "dept:read", Name: "查看部门", Description: "查看部门信息", Category: "部门管理"},
		{Code: "dept:create", Name: "创建部门", Description: "创建新部门", Category: "部门管理"},
		{Code: "dept:update", Name: "更新部门", Description: "更新部门信息", Category: "部门管理"},
		{Code: "dept:delete", Name: "删除部门", Description: "删除部门", Category: "部门管理"},
		{Code: "role:read", Name: "查看角色", Description: "查看角色信息", Category: "角色管理"},
		{Code: "role:create", Name: "创建角色", Description: "创建新角色", Category: "角色管理"},
		{Code: "role:update", Name: "更新角色", Description: "更新角色信息", Category: "角色管理"},
		{Code: "role:delete", Name: "删除角色", Description: "删除角色", Category: "角色管理"},
		{Code: "budget:read", Name: "查看预算", Description: "查看预算信息", Category: "预算管理"},
		{Code: "budget:create", Name: "创建预算", Description: "创建预算方案", Category: "预算管理"},
		{Code: "budget:update", Name: "更新预算", Description: "更新预算信息", Category: "预算管理"},
		{Code: "budget:approve", Name: "审批预算", Description: "审批预算申请", Category: "预算管理"},
		{Code: "expense:read", Name: "查看报销", Description: "查看报销信息", Category: "报销管理"},
		{Code: "expense:create", Name: "创建报销", Description: "创建报销申请", Category: "报销管理"},
		{Code: "expense:approve", Name: "审批报销", Description: "审批报销申请", Category: "报销管理"},
		{Code: "contract:read", Name: "查看合同", Description: "查看合同信息", Category: "合同管理"},
		{Code: "contract:create", Name: "创建合同", Description: "创建合同", Category: "合同管理"},
		{Code: "contract:update", Name: "更新合同", Description: "更新合同信息", Category: "合同管理"},
		{Code: "asset:read", Name: "查看资产", Description: "查看资产信息", Category: "资产管理"},
		{Code: "asset:create", Name: "创建资产", Description: "创建资产记录", Category: "资产管理"},
		{Code: "asset:update", Name: "更新资产", Description: "更新资产信息", Category: "资产管理"},
	}
}

// convertToResponse 转换为响应格式
func (s *RoleService) convertToResponse(role models.Role) dto.RoleResponse {
	response := dto.RoleResponse{
		ID:          role.ID,
		RoleName:    role.RoleName,
		RoleCode:    StringPtr(role.RoleCode),
		Description: role.Description,
		IsActive:    BoolValue(role.IsActive),
		CreatedAt:   role.CreatedAt.Format(time.RFC3339),
		UpdatedAt:   role.UpdatedAt.Format(time.RFC3339),
	}

	// 解析权限
	if role.PermissionStr != nil && *role.PermissionStr != "" {
		response.Permissions = strings.Split(*role.PermissionStr, ",")
	}

	return response
}
