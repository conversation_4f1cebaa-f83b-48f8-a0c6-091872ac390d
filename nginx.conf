server {
    listen 80;
    server_name localhost;  # 替换为您的域名或IP

    # 前端静态文件 - 根据您的配置修改
    location /hospital {
        alias /var/www/hospital/dist;  # 使用 alias 而不是 root
        index index.html;
        try_files $uri $uri/ /hospital/index.html;
    }

    # 如果您想要根路径也能访问，添加这个
    location = / {
        return 301 /hospital/;
    }

    # API 代理到后端
    location /api/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 文件上传大小限制
    client_max_body_size 100M;
}

# HTTPS 配置 (可选)
# server {
#     listen 443 ssl http2;
#     server_name your-domain.com;
#     
#     ssl_certificate /path/to/your/certificate.crt;
#     ssl_certificate_key /path/to/your/private.key;
#     
#     # 其他配置与上面相同...
# }
