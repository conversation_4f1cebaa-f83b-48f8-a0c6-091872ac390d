package controllers

import (
	"hospital-management/internal/dto"
	"hospital-management/internal/services"
	"hospital-management/pkg/logger"
	customValidator "hospital-management/pkg/validator"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
)

type UserController struct {
	userService *services.UserService
	validator   *validator.Validate
}

func NewUserController(userService *services.UserService) *UserController {
	return &UserController{
		userService: userService,
		validator:   customValidator.NewValidator(),
	}
}

// GetUsers 获取用户列表
// @Summary 获取用户列表
// @Description 获取用户列表，支持分页、搜索和筛选
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param keyword query string false "搜索关键词"
// @Param department_id query string false "部门ID"
// @Param is_active query bool false "是否激活"
// @Param role_id query string false "角色ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /users [get]
func (c *UserController) GetUsers(ctx *gin.Context) {
	var req dto.UserListRequest

	// 设置默认值
	req.Page = 1
	req.PageSize = 10

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	users, total, err := c.userService.GetUsers(&req)
	if err != nil {
		logger.Error("Failed to get users:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取用户列表失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      users,
			"total":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	})
}

// GetUserByID 获取用户详情
// @Summary 获取用户详情
// @Description 根据ID获取用户详情
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path string true "用户ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "用户不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /users/{id} [get]
func (c *UserController) GetUserByID(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的用户ID",
		})
		return
	}

	// 调用服务
	user, err := c.userService.GetUserByID(id)
	if err != nil {
		logger.Error("Failed to get user by ID:", err)
		if err.Error() == "用户不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "获取用户详情失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    user,
	})
}

// CreateUser 创建用户
// @Summary 创建用户
// @Description 创建新的用户
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body dto.CreateUserRequest true "创建用户请求"
// @Success 201 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /users [post]
func (c *UserController) CreateUser(ctx *gin.Context) {
	var req dto.CreateUserRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	user, err := c.userService.CreateUser(&req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to create user:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建用户失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "创建成功",
		"data":    user,
	})
}

// UpdateUser 更新用户
// @Summary 更新用户
// @Description 更新用户信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path string true "用户ID"
// @Param request body dto.UpdateUserRequest true "更新用户请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "用户不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /users/{id} [put]
func (c *UserController) UpdateUser(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的用户ID",
		})
		return
	}

	var req dto.UpdateUserRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	user, err := c.userService.UpdateUser(id, &req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to update user:", err)
		if err.Error() == "用户不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "更新用户失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    user,
	})
}

// DeleteUser 删除用户
// @Summary 删除用户
// @Description 删除用户（软删除）
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path string true "用户ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "用户不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /users/{id} [delete]
func (c *UserController) DeleteUser(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的用户ID",
		})
		return
	}

	// 调用服务
	if err := c.userService.DeleteUser(id); err != nil {
		logger.Error("Failed to delete user:", err)
		if err.Error() == "用户不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "删除用户失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// ImportUsers 批量导入用户
// @Summary 批量导入用户
// @Description 批量导入用户数据
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body dto.ImportUserRequest true "导入用户请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /users/import [post]
func (c *UserController) ImportUsers(ctx *gin.Context) {
	var req dto.ImportUserRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 批量创建用户
	successCount := 0
	failedUsers := make([]map[string]interface{}, 0)

	for i, userReq := range req.Users {
		_, err := c.userService.CreateUser(&userReq, userID.(uuid.UUID)) // user
		if err != nil {
			failedUsers = append(failedUsers, map[string]interface{}{
				"index":     i + 1,
				"user_name": userReq.UserName,
				"error":     err.Error(),
			})
		} else {
			successCount++
		}
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "导入完成",
		"data": gin.H{
			"total":         len(req.Users),
			"success_count": successCount,
			"failed_count":  len(failedUsers),
			"failed_users":  failedUsers,
		},
	})
}

// ExportUsers 导出用户数据
// @Summary 导出用户数据
// @Description 导出用户数据为Excel或CSV格式
// @Tags 用户管理
// @Accept json
// @Produce application/octet-stream
// @Param department_id query string false "部门ID"
// @Param is_active query bool false "是否激活"
// @Param format query string true "导出格式" Enums(excel, csv)
// @Success 200 {file} file "导出文件"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /users/export [get]
func (c *UserController) ExportUsers(ctx *gin.Context) {
	var req dto.ExportUserRequest

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// TODO: 实现导出功能
	// 这里应该调用服务层的导出方法，生成Excel或CSV文件
	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "导出功能待实现",
		"data":    req,
	})
}
