package models

// Supplier 供应商信息库 - 对应 tbl_suppliers
type Supplier struct {
	BaseModel
	Name        string   `json:"name" gorm:"type:varchar(255);not null;unique" validate:"required,max=255"`
	CreditCode  *string  `json:"credit_code,omitempty" gorm:"type:varchar(100);unique"`
	Status      int16    `json:"status" gorm:"not null;default:1" validate:"required,oneof=0 1"`
	ContactInfo *JSONMap `json:"contact_info,omitempty" gorm:"type:jsonb"`

	// 关联关系
	Contracts []Contract `json:"contracts,omitempty" gorm:"foreignKey:CounterpartyID"`
}

// TableName 指定表名
func (Supplier) TableName() string {
	return "tbl_suppliers"
}
