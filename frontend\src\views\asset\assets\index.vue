<template>
  <div class="assets-page">
    <a-card :bordered="false">
      <template #title>
        <span>资产台账管理</span>
      </template>

      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <PlusOutlined />
            </template>
            新增资产
          </a-button>
          <a-button @click="handleRefresh">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 搜索区域 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="关键词">
            <a-input v-model:value="searchForm.keyword" placeholder="资产编号/名称/型号" allow-clear style="width: 200px" />
          </a-form-item>

          <a-form-item label="资产分类">
            <a-tree-select v-model:value="searchForm.category_id" :tree-data="categoryTreeOptions" placeholder="请选择分类"
              allow-clear style="width: 200px" />
          </a-form-item>

          <a-form-item label="所属部门">
            <a-tree-select v-model:value="searchForm.department_id" :tree-data="departmentTreeOptions"
              placeholder="请选择部门" allow-clear style="width: 200px" />
          </a-form-item>

          <a-form-item label="资产状态">
            <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear style="width: 120px">
              <a-select-option value="normal">正常</a-select-option>
              <a-select-option value="maintenance">维护中</a-select-option>
              <a-select-option value="idle">闲置</a-select-option>
              <a-select-option value="scrapped">报废</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="购置时间">
            <a-range-picker v-model:value="dateRange" style="width: 240px" @change="handleDateChange" />
          </a-form-item>

          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon>
                  <SearchOutlined />
                </template>
                搜索
              </a-button>
              <a-button @click="handleReset">
                <template #icon>
                  <ReloadOutlined />
                </template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 资产列表 -->
      <a-table :columns="columns" :data-source="assetList" :loading="loading" :pagination="pagination" row-key="id"
        size="middle" :scroll="{ x: 1400 }" @change="handleTableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'asset_no'">
            <a @click="handleView(record)">{{ record.asset_no }}</a>
          </template>

          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'purchase_price'">
            <span class="amount-text">{{ formatAmount(record.purchase_price) }}</span>
          </template>

          <template v-else-if="column.key === 'current_value'">
            <span class="amount-text">{{ formatAmount(record.current_value) }}</span>
          </template>

          <template v-else-if="column.key === 'depreciation_rate'">
            <span>{{ (record.depreciation_rate * 100).toFixed(2) }}%</span>
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleView(record)">
                查看
              </a-button>
              <a-button type="link" size="small" @click="handleEdit(record)">
                编辑
              </a-button>
              <a-dropdown>
                <a-button type="link" size="small">
                  更多
                  <DownOutlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="maintenance" @click="handleUpdateStatus(record, 'maintenance')"
                      v-if="record.status === 'normal'">
                      标记为维护中
                    </a-menu-item>
                    <a-menu-item key="normal" @click="handleUpdateStatus(record, 'normal')"
                      v-if="record.status === 'maintenance'">
                      标记为正常
                    </a-menu-item>
                    <a-menu-item key="idle" @click="handleUpdateStatus(record, 'idle')"
                      v-if="record.status === 'normal'">
                      标记为闲置
                    </a-menu-item>
                    <a-menu-item key="scrapped" @click="handleUpdateStatus(record, 'scrapped')"
                      v-if="record.status !== 'scrapped'">
                      标记为报废
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" @click="handleDelete(record)" danger>
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑资产弹窗 -->
    <a-modal v-model:open="modalVisible" :title="modalTitle" :confirm-loading="modalLoading" width="1000px"
      @ok="handleSubmit" @cancel="handleCancel">
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="资产名称" name="name">
              <a-input v-model:value="formData.name" placeholder="请输入资产名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="资产分类" name="category_id">
              <a-tree-select v-model:value="formData.category_id" :tree-data="categoryTreeOptions" placeholder="请选择资产分类"
                tree-default-expand-all />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="规格型号" name="specification">
              <a-input v-model:value="formData.specification" placeholder="请输入规格型号" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="设备型号" name="model">
              <a-input v-model:value="formData.model" placeholder="请输入设备型号" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="品牌" name="brand">
              <a-input v-model:value="formData.brand" placeholder="请输入品牌" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="供应商" name="supplier_id">
              <a-select v-model:value="formData.supplier_id" placeholder="请选择供应商" allow-clear show-search
                :filter-option="filterSupplierOption" :options="supplierOptions" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="购置日期" name="purchase_date">
              <a-date-picker v-model:value="formData.purchase_date" placeholder="请选择购置日期" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="购置价格" name="purchase_price">
              <a-input-number v-model:value="formData.purchase_price" :min="0" :precision="2" placeholder="请输入购置价格"
                style="width: 100%" :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                :parser="value => value.replace(/¥\s?|(,*)/g, '')" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="存放位置" name="location">
              <a-input v-model:value="formData.location" placeholder="请输入存放位置" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="所属部门" name="department_id">
              <a-tree-select v-model:value="formData.department_id" :tree-data="departmentTreeOptions"
                placeholder="请选择所属部门" tree-default-expand-all />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="资产管理员" name="custodian_id">
              <a-select v-model:value="formData.custodian_id" placeholder="请选择资产管理员" allow-clear show-search
                :filter-option="filterUserOption" :options="userOptions" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="保修期至" name="warranty_date">
              <a-date-picker v-model:value="formData.warranty_date" placeholder="请选择保修期至" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="维护周期" name="maintenance_cycle">
              <a-input-number v-model:value="formData.maintenance_cycle" :min="1" placeholder="请输入维护周期"
                style="width: 100%" addon-after="月" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="备注说明" name="remark">
          <a-textarea v-model:value="formData.remark" placeholder="请输入备注说明" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 查看详情弹窗 -->
    <a-modal v-model:open="viewModalVisible" title="资产详情" :footer="null" width="1000px">
      <a-descriptions :column="2" bordered v-if="currentRecord">
        <a-descriptions-item label="资产编号" :span="2">
          {{ currentRecord.asset_no }}
        </a-descriptions-item>
        <a-descriptions-item label="资产名称" :span="2">
          {{ currentRecord.name }}
        </a-descriptions-item>
        <a-descriptions-item label="资产分类">
          {{ currentRecord.category_name }}
        </a-descriptions-item>
        <a-descriptions-item label="资产状态">
          <a-tag :color="getStatusColor(currentRecord.status)">
            {{ getStatusText(currentRecord.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="规格型号" v-if="currentRecord.specification">
          {{ currentRecord.specification }}
        </a-descriptions-item>
        <a-descriptions-item label="设备型号" v-if="currentRecord.model">
          {{ currentRecord.model }}
        </a-descriptions-item>
        <a-descriptions-item label="品牌" v-if="currentRecord.brand">
          {{ currentRecord.brand }}
        </a-descriptions-item>
        <a-descriptions-item label="供应商" v-if="currentRecord.supplier_name">
          {{ currentRecord.supplier_name }}
        </a-descriptions-item>
        <a-descriptions-item label="购置日期">
          {{ currentRecord.purchase_date }}
        </a-descriptions-item>
        <a-descriptions-item label="购置价格">
          <span class="amount-text">{{ formatAmount(currentRecord.purchase_price) }}</span>
        </a-descriptions-item>
        <a-descriptions-item label="原值">
          <span class="amount-text">{{ formatAmount(currentRecord.original_value) }}</span>
        </a-descriptions-item>
        <a-descriptions-item label="现值">
          <span class="amount-text">{{ formatAmount(currentRecord.current_value) }}</span>
        </a-descriptions-item>
        <a-descriptions-item label="累计折旧">
          <span class="amount-text">{{ formatAmount(currentRecord.accumulated_depreciation) }}</span>
        </a-descriptions-item>
        <a-descriptions-item label="折旧率">
          {{ (currentRecord.depreciation_rate * 100).toFixed(2) }}%
        </a-descriptions-item>
        <a-descriptions-item label="使用年限">
          {{ currentRecord.useful_life }}年
        </a-descriptions-item>
        <a-descriptions-item label="存放位置">
          {{ currentRecord.location }}
        </a-descriptions-item>
        <a-descriptions-item label="所属部门">
          {{ currentRecord.department_name }}
        </a-descriptions-item>
        <a-descriptions-item label="资产管理员" v-if="currentRecord.custodian_name">
          {{ currentRecord.custodian_name }}
        </a-descriptions-item>
        <a-descriptions-item label="保修期至" v-if="currentRecord.warranty_date">
          {{ currentRecord.warranty_date }}
        </a-descriptions-item>
        <a-descriptions-item label="维护周期" v-if="currentRecord.maintenance_cycle">
          {{ currentRecord.maintenance_cycle }}月
        </a-descriptions-item>
        <a-descriptions-item label="上次维护" v-if="currentRecord.last_maintenance_date">
          {{ currentRecord.last_maintenance_date }}
        </a-descriptions-item>
        <a-descriptions-item label="下次维护" v-if="currentRecord.next_maintenance_date">
          {{ currentRecord.next_maintenance_date }}
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">
          {{ currentRecord.created_at }}
        </a-descriptions-item>
        <a-descriptions-item label="备注说明" :span="2" v-if="currentRecord.remark">
          {{ currentRecord.remark }}
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import dayjs, { type Dayjs } from 'dayjs'
import {
  getAssets,
  createAsset,
  updateAsset,
  deleteAsset,
  updateAssetStatus,
  getAssetCategoryTree,
  type Asset,
  type AssetForm,
  type AssetQuery,
  type AssetCategory
} from '@/api/asset'
import { getSuppliers, type Supplier } from '@/api/procurement'
import { getDepartmentTree, getUsers, type Department, type User } from '@/api/system'
import type { Rule } from 'ant-design-vue/es/form'
import type { FormInstance } from 'ant-design-vue'

const loading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const viewModalVisible = ref(false)
const formRef = ref<FormInstance>()
const editingId = ref<number | null>(null)

const assetList = ref<Asset[]>([])
const categoryList = ref<AssetCategory[]>([])
const supplierList = ref<Supplier[]>([])
const departmentList = ref<Department[]>([])
const userList = ref<User[]>([])
const currentRecord = ref<Asset | null>(null)
const dateRange = ref<[Dayjs, Dayjs] | null>(null)

const searchForm = reactive<AssetQuery>({
  keyword: '',
  category_id: undefined,
  department_id: undefined,
  status: undefined,
  start_date: undefined,
  end_date: undefined,
  page: 1,
  page_size: 10
})

const formData = reactive<AssetForm & {
  purchase_date: Dayjs | null
  warranty_date: Dayjs | null
}>({
  name: '',
  category_id: 0,
  specification: '',
  model: '',
  brand: '',
  supplier_id: undefined,
  purchase_date: null,
  purchase_price: 0,
  location: '',
  department_id: 0,
  custodian_id: undefined,
  warranty_date: null,
  maintenance_cycle: undefined,
  remark: ''
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '资产编号',
    key: 'asset_no',
    width: 150,
    fixed: 'left'
  },
  {
    title: '资产名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
    ellipsis: true
  },
  {
    title: '资产分类',
    dataIndex: 'category_name',
    key: 'category_name',
    width: 150
  },
  {
    title: '规格型号',
    dataIndex: 'specification',
    key: 'specification',
    width: 120,
    ellipsis: true
  },
  {
    title: '品牌',
    dataIndex: 'brand',
    key: 'brand',
    width: 100
  },
  {
    title: '购置价格',
    key: 'purchase_price',
    width: 120,
    align: 'right'
  },
  {
    title: '现值',
    key: 'current_value',
    width: 120,
    align: 'right'
  },
  {
    title: '折旧率',
    key: 'depreciation_rate',
    width: 100,
    align: 'center'
  },
  {
    title: '存放位置',
    dataIndex: 'location',
    key: 'location',
    width: 120,
    ellipsis: true
  },
  {
    title: '所属部门',
    dataIndex: 'department_name',
    key: 'department_name',
    width: 120
  },
  {
    title: '资产状态',
    key: 'status',
    width: 100
  },
  {
    title: '购置日期',
    dataIndex: 'purchase_date',
    key: 'purchase_date',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right'
  }
]

// 表单验证规则
const rules: Record<string, Rule[]> = {
  name: [
    { required: true, message: '请输入资产名称', trigger: 'blur' }
  ],
  category_id: [
    { required: true, message: '请选择资产分类', trigger: 'change' }
  ],
  purchase_date: [
    { required: true, message: '请选择购置日期', trigger: 'change' }
  ],
  purchase_price: [
    { required: true, message: '请输入购置价格', trigger: 'blur' }
  ],
  location: [
    { required: true, message: '请输入存放位置', trigger: 'blur' }
  ],
  department_id: [
    { required: true, message: '请选择所属部门', trigger: 'change' }
  ]
}

// 弹窗标题
const modalTitle = computed(() => {
  return editingId.value ? '编辑资产' : '新增资产'
})

// 分类树形选择器选项
const categoryTreeOptions = computed(() => {
  const convertToTreeData = (categories: AssetCategory[]): any[] => {
    return categories.map(category => ({
      title: `${category.code} - ${category.name}`,
      value: category.id,
      key: category.id,
      children: category.children ? convertToTreeData(category.children) : undefined
    }))
  }
  return convertToTreeData(categoryList.value)
})

// 部门树形选择器选项
const departmentTreeOptions = computed(() => {
  const convertToTreeData = (departments: Department[]): any[] => {
    return departments.map(dept => ({
      title: dept.name,
      value: dept.id,
      key: dept.id,
      children: dept.children ? convertToTreeData(dept.children) : undefined
    }))
  }
  return convertToTreeData(departmentList.value)
})

// 供应商选项
const supplierOptions = computed(() => {
  return supplierList.value
    .filter(supplier => supplier.status === 'active')
    .map(supplier => ({
      label: supplier.name,
      value: supplier.id
    }))
})

// 用户选项
const userOptions = computed(() => {
  return userList.value.map(user => ({
    label: `${user.name} (${user.department_name})`,
    value: user.id
  }))
})
</script>
