<template>
  <div class="users-page">
    <a-card :bordered="false">
      <template #title>
        <span>用户管理</span>
      </template>

      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <PlusOutlined />
            </template>
            新增用户
          </a-button>
          <a-button @click="handleImport">
            <template #icon>
              <ImportOutlined />
            </template>
            批量导入
          </a-button>
          <a-button @click="handleExport">
            <template #icon>
              <ExportOutlined />
            </template>
            导出数据
          </a-button>
        </a-space>
      </template>

      <!-- 搜索区域 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="关键词">
            <a-input v-model:value="searchForm.keyword" placeholder="用户名/工号/手机号" allow-clear style="width: 200px" />
          </a-form-item>

          <a-form-item label="部门">
            <a-tree-select v-model:value="searchForm.department_id" :tree-data="departmentTreeOptions"
              placeholder="请选择部门" allow-clear style="width: 200px" />
          </a-form-item>

          <a-form-item label="状态">
            <a-select v-model:value="searchForm.is_active" placeholder="请选择状态" allow-clear style="width: 120px">
              <a-select-option :value="true">启用</a-select-option>
              <a-select-option :value="false">禁用</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon>
                  <SearchOutlined />
                </template>
                搜索
              </a-button>
              <a-button @click="handleReset">
                <template #icon>
                  <ReloadOutlined />
                </template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 用户表格 -->
      <a-table :columns="columns" :data-source="userList" :loading="loading" :pagination="pagination" row-key="id"
        size="middle" @change="handleTableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'is_active'">
            <a-tag :color="record.is_active ? 'green' : 'red'">
              {{ record.is_active ? '启用' : '禁用' }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'roles'">
            <a-space>
              <a-tag v-for="role in record.roles" :key="role.id" color="blue">
                {{ role.role_name }}
              </a-tag>
            </a-space>
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)">
                编辑
              </a-button>
              <a-button type="link" size="small" @click="handleResetPassword(record)">
                重置密码
              </a-button>
              <a-popconfirm title="确定要删除这个用户吗？" @confirm="handleDelete(record)">
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑用户弹窗 -->
    <a-modal v-model:open="modalVisible" :title="modalTitle" :confirm-loading="modalLoading" width="600px"
      @ok="handleSubmit" @cancel="handleCancel">
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="用户名" name="user_name">
          <a-input v-model:value="formData.user_name" placeholder="请输入用户名" :disabled="!!editingId" />
        </a-form-item>

        <a-form-item label="工号" name="employee_id">
          <a-input v-model:value="formData.employee_id" placeholder="请输入工号" />
        </a-form-item>

        <a-form-item label="邮箱" name="email">
          <a-input v-model:value="formData.email" placeholder="请输入邮箱" />
        </a-form-item>

        <a-form-item label="手机号" name="phone_number">
          <a-input v-model:value="formData.phone_number" placeholder="请输入手机号" />
        </a-form-item>

        <a-form-item label="职位" name="job_title">
          <a-input v-model:value="formData.job_title" placeholder="请输入职位" />
        </a-form-item>

        <a-form-item label="所属部门" name="department_id">
          <a-tree-select v-model:value="formData.department_id" :tree-data="departmentTreeOptions" placeholder="请选择部门"
            tree-default-expand-all />
        </a-form-item>

        <a-form-item v-if="!editingId" label="密码" name="password">
          <a-input-password v-model:value="formData.password" placeholder="请输入密码" />
        </a-form-item>

        <a-form-item label="角色" name="role_ids">
          <a-select v-model:value="formData.role_ids" mode="multiple" placeholder="请选择角色" :options="roleOptions" />
        </a-form-item>

        <a-form-item label="状态" name="is_active">
          <a-radio-group v-model:value="formData.is_active">
            <a-radio :value="true">启用</a-radio>
            <a-radio :value="false">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 导入用户弹窗 -->
    <a-modal v-model:open="importModalVisible" title="批量导入用户" :confirm-loading="importLoading" @ok="handleImportSubmit"
      @cancel="importModalVisible = false">
      <a-upload-dragger v-model:file-list="fileList" :before-upload="beforeUpload" :remove="handleRemoveFile"
        accept=".xlsx,.xls">
        <p class="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
        <p class="ant-upload-hint">
          支持单个文件上传，仅支持 .xlsx 和 .xls 格式
        </p>
      </a-upload-dragger>

      <div class="import-template">
        <a-button type="link" @click="downloadTemplate">
          下载导入模板
        </a-button>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  ImportOutlined,
  ExportOutlined,
  SearchOutlined,
  ReloadOutlined,
  InboxOutlined
} from '@ant-design/icons-vue'
import {
  getUsers,
  createUser,
  updateUser,
  deleteUser,
  importUsers,
  exportUsers,
  getDepartmentTree,
  getRoles,
  type User,
  type UserForm,
  type UserQuery,
  type Department,
  type Role
} from '@/api/system'
import type { Rule } from 'ant-design-vue/es/form'
import type { FormInstance } from 'ant-design-vue'
import type { UploadFile } from 'ant-design-vue'

const loading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const importModalVisible = ref(false)
const importLoading = ref(false)
const formRef = ref<FormInstance>()
const editingId = ref<string | null>(null)

const userList = ref<User[]>([])
const departmentList = ref<Department[]>([])
const roleList = ref<Role[]>([])
const fileList = ref<UploadFile[]>([])

const searchForm = reactive<UserQuery>({
  keyword: '',
  department_id: undefined,
  is_active: undefined,
  page: 1,
  page_size: 10
})

const formData = reactive<UserForm>({
  department_id: '',
  user_name: '',
  employee_id: '',
  email: '',
  phone_number: '',
  password: '',
  job_title: '',
  is_active: true,
  role_ids: []
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '用户名',
    dataIndex: 'user_name',
    key: 'user_name',
    width: 120
  },
  {
    title: '工号',
    dataIndex: 'employee_id',
    key: 'employee_id',
    width: 100
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
    width: 180
  },
  {
    title: '手机号',
    dataIndex: 'phone_number',
    key: 'phone_number',
    width: 120
  },
  {
    title: '职位',
    dataIndex: 'job_title',
    key: 'job_title',
    width: 120
  },
  {
    title: '部门',
    dataIndex: ['department', 'dept_name'],
    key: 'department_name',
    width: 150
  },
  {
    title: '角色',
    key: 'roles',
    width: 200
  },
  {
    title: '状态',
    key: 'is_active',
    width: 80
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 表单验证规则
const rules: Record<string, Rule[]> = {
  user_name: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }
  ],
  department_id: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone_number: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

// 弹窗标题
const modalTitle = computed(() => {
  return editingId.value ? '编辑用户' : '新增用户'
})

// 部门树形选择器选项
const departmentTreeOptions = computed(() => {
  const convertToTreeData = (departments: Department[]): any[] => {
    return departments.map(dept => ({
      title: dept.dept_name,
      value: dept.id,
      key: dept.id,
      children: dept.children ? convertToTreeData(dept.children) : undefined
    }))
  }
  return convertToTreeData(departmentList.value)
})

// 角色选择器选项
const roleOptions = computed(() => {
  return roleList.value.map(role => ({
    label: role.name,
    value: role.id
  }))
})

// 加载用户数据
const loadUsers = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.current,
      page_size: pagination.pageSize
    }
    const response = await getUsers(params)
    userList.value = response.list
    pagination.total = response.total
  } catch (error) {
    message.error('加载用户数据失败')
  } finally {
    loading.value = false
  }
}

// 加载部门数据
const loadDepartments = async () => {
  try {
    departmentList.value = await getDepartmentTree()
  } catch (error) {
    message.error('加载部门数据失败')
  }
}

// 加载角色数据
const loadRoles = async () => {
  try {
    roleList.value = await getRoles()
  } catch (error) {
    message.error('加载角色数据失败')
  }
}

// 搜索用户
const handleSearch = () => {
  pagination.current = 1
  loadUsers()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    department_id: undefined,
    status: undefined
  })
  pagination.current = 1
  loadUsers()
}

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadUsers()
}

// 新增用户
const handleAdd = () => {
  editingId.value = null
  resetForm()
  modalVisible.value = true
}

// 编辑用户
const handleEdit = (record: User) => {
  editingId.value = record.id
  Object.assign(formData, {
    department_id: record.department_id,
    user_name: record.user_name,
    employee_id: record.employee_id,
    email: record.email,
    phone_number: record.phone_number,
    job_title: record.job_title,
    is_active: record.is_active,
    role_ids: record.roles?.map(role => role.id) || [], // 获取用户的角色ID
    password: '' // 编辑时不显示密码
  })
  modalVisible.value = true
}

// 删除用户
const handleDelete = async (record: User) => {
  try {
    await deleteUser(record.id)
    message.success('删除成功')
    loadUsers()
  } catch (error) {
    message.error('删除失败')
  }
}

// 重置密码
const handleResetPassword = (record: User) => {
  // TODO: 实现重置密码功能
  message.info('重置密码功能待实现')
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    modalLoading.value = true

    if (editingId.value) {
      await updateUser(editingId.value, formData)
      message.success('更新成功')
    } else {
      await createUser(formData)
      message.success('创建成功')
    }

    modalVisible.value = false
    loadUsers()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    modalLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    department_id: '',
    user_name: '',
    employee_id: '',
    email: '',
    phone_number: '',
    password: '',
    job_title: '',
    is_active: true,
    role_ids: []
  })
  formRef.value?.resetFields()
}

// 导入用户
const handleImport = () => {
  fileList.value = []
  importModalVisible.value = true
}

// 文件上传前处理
const beforeUpload = (file: UploadFile) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    message.error('只能上传 Excel 文件!')
    return false
  }
  const isLt10M = (file.size || 0) / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过 10MB!')
    return false
  }
  return false // 阻止自动上传
}

// 移除文件
const handleRemoveFile = () => {
  fileList.value = []
}

// 提交导入
const handleImportSubmit = async () => {
  if (fileList.value.length === 0) {
    message.error('请选择要导入的文件')
    return
  }

  importLoading.value = true
  try {
    const file = fileList.value[0].originFileObj as File
    const result = await importUsers(file)
    message.success(`导入成功 ${result.success_count} 条，失败 ${result.error_count} 条`)
    if (result.errors.length > 0) {
      console.error('导入错误:', result.errors)
    }
    importModalVisible.value = false
    loadUsers()
  } catch (error) {
    message.error('导入失败')
  } finally {
    importLoading.value = false
  }
}

// 导出用户
const handleExport = async () => {
  try {
    const blob = await exportUsers(searchForm)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `用户数据_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    message.success('导出成功')
  } catch (error) {
    message.error('导出失败')
  }
}

// 下载模板
const downloadTemplate = () => {
  // TODO: 实现下载模板功能
  message.info('下载模板功能待实现')
}

onMounted(() => {
  loadUsers()
  loadDepartments()
  loadRoles()
})
</script>

<style scoped>
.users-page {
  padding: 24px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.import-template {
  margin-top: 16px;
  text-align: center;
}
</style>
