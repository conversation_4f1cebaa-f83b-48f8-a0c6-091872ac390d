import request from './request'

// 字典管理相关接口
export interface Dictionary {
  id: string
  dict_type: string
  dict_name: string
  dict_value: string
  dict_label: string
  sort_order: number
  status: 'active' | 'inactive'
  remark?: string
  created_at: string
  updated_at: string
}

export interface DictionaryQuery {
  dict_type?: string
  status?: string
  keyword?: string
}

// 获取字典列表
export const getDictionaries = (params?: DictionaryQuery): Promise<Dictionary[]> => {
  return request.get('/api/v1/dictionaries', { params })
}

// 根据字典类型获取字典项
export const getDictionaryByType = (dictType: string): Promise<Dictionary[]> => {
  return request.get(`/api/v1/dictionaries/${dictType}`)
}

// 仪表板统计相关接口
export interface BudgetStats {
  total_budget: number
  used_budget: number
  frozen_budget: number
  available_budget: number
  usage_rate: number
}

export interface ExpenseStats {
  total_expense_applications: number
  total_expense_amount: number
  pending_expense_applications: number
  total_payments: number
  total_payment_amount: number
}

export interface ProcurementStats {
  total_suppliers: number
  total_purchase_requisitions: number
  total_purchase_amount: number
  pending_purchase_requisitions: number
}

export interface ContractStats {
  total_contracts: number
  total_contract_amount: number
  active_contracts: number
  total_payment_schedules: number
  total_schedule_amount: number
}

export interface AssetStats {
  total_assets: number
  total_asset_value: number
  in_use_assets: number
  idle_assets: number
  maintenance_assets: number
  scrapped_assets: number
  total_categories: number
}

export interface DashboardStatistics {
  budget_stats: BudgetStats
  expense_stats: ExpenseStats
  procurement_stats: ProcurementStats
  contract_stats: ContractStats
  asset_stats: AssetStats
}

// 获取仪表板统计数据
export const getDashboardStatistics = (): Promise<DashboardStatistics> => {
  return request.get('/api/v1/dashboard/statistics')
}

// 系统信息相关接口
export interface SystemInfo {
  system_name: string
  system_version: string
  build_time: string
  go_version: string
  git_commit: string
  
  // 运行时信息
  uptime: string
  cpu_usage: number
  memory_usage: number
  disk_usage: number
  
  // 数据库信息
  database_type: string
  database_version: string
  total_connections: number
  active_connections: number
  
  // 缓存信息
  redis_status: string
  redis_memory: string
  redis_keys: number
  
  // 业务统计
  total_records: {
    users: number
    departments: number
    expenses: number
    contracts: number
    assets: number
  }
}

// 获取系统信息
export const getSystemInfo = (): Promise<SystemInfo> => {
  return request.get('/api/v1/system/info')
}

// 通用查询接口
export interface CommonQuery {
  keyword?: string
  status?: string
  start_date?: string
  end_date?: string
  page?: number
  page_size?: number
  sort_field?: string
  sort_order?: 'asc' | 'desc'
}

// 通用响应接口
export interface CommonResponse<T> {
  list: T[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

// 通用操作结果接口
export interface OperationResult {
  success: boolean
  message: string
  data?: any
}

// 批量操作接口
export interface BatchOperation {
  ids: string[]
  operation: string
  params?: Record<string, any>
}

// 执行批量操作
export const executeBatchOperation = (data: BatchOperation): Promise<OperationResult> => {
  return request.post('/api/v1/batch-operation', data)
}

// 导出数据接口
export interface ExportRequest {
  type: string
  format: 'excel' | 'csv' | 'pdf'
  filters?: Record<string, any>
  fields?: string[]
}

// 导出数据
export const exportData = (data: ExportRequest): Promise<Blob> => {
  return request.post('/api/v1/export', data, {
    responseType: 'blob'
  })
}

// 导入数据接口
export const importData = (file: File, type: string): Promise<{
  success_count: number
  failed_count: number
  errors: Array<{
    row: number
    message: string
  }>
}> => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('type', type)
  
  return request.post('/api/v1/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取操作日志
export interface OperationLog {
  id: string
  user_id: string
  user_name: string
  operation: string
  resource_type: string
  resource_id: string
  details: string
  ip_address: string
  user_agent: string
  created_at: string
}

export const getOperationLogs = (params: {
  user_id?: string
  operation?: string
  resource_type?: string
  start_date?: string
  end_date?: string
  page?: number
  page_size?: number
}): Promise<CommonResponse<OperationLog>> => {
  return request.get('/api/v1/operation-logs', { params })
}

// 健康检查
export const healthCheck = (): Promise<{
  status: 'healthy' | 'unhealthy'
  timestamp: string
  services: Record<string, {
    status: 'up' | 'down'
    response_time?: number
    error?: string
  }>
}> => {
  return request.get('/api/v1/health')
}
