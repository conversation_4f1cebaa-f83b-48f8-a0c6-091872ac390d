<template>
  <div class="asset-categories-page">
    <a-card :bordered="false">
      <template #title>
        <span>资产分类管理</span>
      </template>
      
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon><PlusOutlined /></template>
            新增分类
          </a-button>
          <a-button @click="handleRefresh">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="categoryList"
        :loading="loading"
        :pagination="false"
        row-key="id"
        :default-expand-all-rows="true"
        size="middle"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'depreciation_method'">
            <a-tag :color="getDepreciationMethodColor(record.depreciation_method)">
              {{ getDepreciationMethodText(record.depreciation_method) }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)">
                编辑
              </a-button>
              <a-button type="link" size="small" @click="handleAddChild(record)">
                添加子分类
              </a-button>
              <a-popconfirm
                title="确定要删除这个分类吗？"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑分类弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :confirm-loading="modalLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="上级分类" name="parent_id">
          <a-tree-select
            v-model:value="formData.parent_id"
            :tree-data="categoryTreeOptions"
            placeholder="请选择上级分类（不选则为顶级分类）"
            allow-clear
            tree-default-expand-all
          />
        </a-form-item>
        
        <a-form-item label="分类编码" name="code">
          <a-input v-model:value="formData.code" placeholder="请输入分类编码" />
        </a-form-item>
        
        <a-form-item label="分类名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入分类名称" />
        </a-form-item>
        
        <a-form-item label="折旧方法" name="depreciation_method">
          <a-select v-model:value="formData.depreciation_method" placeholder="请选择折旧方法">
            <a-select-option value="straight_line">直线法</a-select-option>
            <a-select-option value="declining_balance">余额递减法</a-select-option>
            <a-select-option value="sum_of_years">年数总和法</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="折旧年限" name="depreciation_years">
          <a-input-number
            v-model:value="formData.depreciation_years"
            :min="1"
            :max="50"
            placeholder="请输入折旧年限"
            style="width: 100%"
            addon-after="年"
          />
        </a-form-item>
        
        <a-form-item label="分类描述" name="description">
          <a-textarea
            v-model:value="formData.description"
            placeholder="请输入分类描述"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import {
  getAssetCategoryTree,
  createAssetCategory,
  updateAssetCategory,
  deleteAssetCategory,
  type AssetCategory,
  type AssetCategoryForm
} from '@/api/asset'
import type { Rule } from 'ant-design-vue/es/form'
import type { FormInstance } from 'ant-design-vue'

const loading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const formRef = ref<FormInstance>()
const editingId = ref<number | null>(null)

const categoryList = ref<AssetCategory[]>([])

const formData = reactive<AssetCategoryForm>({
  name: '',
  code: '',
  parent_id: undefined,
  depreciation_method: 'straight_line',
  depreciation_years: 10,
  description: ''
})

// 表格列配置
const columns = [
  {
    title: '分类编码',
    dataIndex: 'code',
    key: 'code',
    width: 150
  },
  {
    title: '分类名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '折旧方法',
    key: 'depreciation_method',
    width: 120
  },
  {
    title: '折旧年限',
    dataIndex: 'depreciation_years',
    key: 'depreciation_years',
    width: 100,
    customRender: ({ text }: any) => `${text}年`
  },
  {
    title: '层级',
    dataIndex: 'level',
    key: 'level',
    width: 80
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 表单验证规则
const rules: Record<string, Rule[]> = {
  code: [
    { required: true, message: '请输入分类编码', trigger: 'blur' },
    { pattern: /^[A-Z0-9]+$/, message: '分类编码只能包含大写字母和数字', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' }
  ],
  depreciation_method: [
    { required: true, message: '请选择折旧方法', trigger: 'change' }
  ],
  depreciation_years: [
    { required: true, message: '请输入折旧年限', trigger: 'blur' }
  ]
}

// 弹窗标题
const modalTitle = computed(() => {
  return editingId.value ? '编辑资产分类' : '新增资产分类'
})

// 分类树形选择器选项
const categoryTreeOptions = computed(() => {
  const convertToTreeData = (categories: AssetCategory[]): any[] => {
    return categories.map(category => ({
      title: `${category.code} - ${category.name}`,
      value: category.id,
      key: category.id,
      children: category.children ? convertToTreeData(category.children) : undefined
    }))
  }
  
  // 编辑时需要过滤掉当前分类及其子分类
  let filteredCategories = categoryList.value
  if (editingId.value) {
    const filterCategories = (categories: AssetCategory[], excludeId: number): AssetCategory[] => {
      return categories.filter(category => {
        if (category.id === excludeId) return false
        if (category.children) {
          category.children = filterCategories(category.children, excludeId)
        }
        return true
      })
    }
    filteredCategories = filterCategories([...categoryList.value], editingId.value)
  }
  
  return convertToTreeData(filteredCategories)
})

// 获取折旧方法颜色
const getDepreciationMethodColor = (method: string) => {
  const colorMap: Record<string, string> = {
    'straight_line': 'blue',
    'declining_balance': 'green',
    'sum_of_years': 'orange'
  }
  return colorMap[method] || 'default'
}

// 获取折旧方法文本
const getDepreciationMethodText = (method: string) => {
  const textMap: Record<string, string> = {
    'straight_line': '直线法',
    'declining_balance': '余额递减法',
    'sum_of_years': '年数总和法'
  }
  return textMap[method] || method
}

// 加载分类数据
const loadCategories = async () => {
  loading.value = true
  try {
    categoryList.value = await getAssetCategoryTree()
  } catch (error) {
    message.error('加载资产分类数据失败')
  } finally {
    loading.value = false
  }
}

// 新增分类
const handleAdd = () => {
  editingId.value = null
  resetForm()
  modalVisible.value = true
}

// 添加子分类
const handleAddChild = (parent: AssetCategory) => {
  editingId.value = null
  resetForm()
  formData.parent_id = parent.id
  formData.depreciation_method = parent.depreciation_method // 继承父分类折旧方法
  formData.depreciation_years = parent.depreciation_years // 继承父分类折旧年限
  modalVisible.value = true
}

// 编辑分类
const handleEdit = (record: AssetCategory) => {
  editingId.value = record.id
  Object.assign(formData, {
    code: record.code,
    name: record.name,
    parent_id: record.parent_id,
    depreciation_method: record.depreciation_method,
    depreciation_years: record.depreciation_years,
    description: record.description
  })
  modalVisible.value = true
}

// 删除分类
const handleDelete = async (record: AssetCategory) => {
  try {
    await deleteAssetCategory(record.id)
    message.success('删除成功')
    loadCategories()
  } catch (error) {
    message.error('删除失败')
  }
}

// 刷新数据
const handleRefresh = () => {
  loadCategories()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    modalLoading.value = true
    
    if (editingId.value) {
      await updateAssetCategory(editingId.value, formData)
      message.success('更新成功')
    } else {
      await createAssetCategory(formData)
      message.success('创建成功')
    }
    
    modalVisible.value = false
    loadCategories()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    modalLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    code: '',
    parent_id: undefined,
    depreciation_method: 'straight_line',
    depreciation_years: 10,
    description: ''
  })
  formRef.value?.resetFields()
}

onMounted(() => {
  loadCategories()
})
</script>

<style scoped>
.asset-categories-page {
  padding: 24px;
}
</style>
