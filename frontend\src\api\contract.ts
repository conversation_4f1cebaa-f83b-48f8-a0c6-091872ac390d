import request from './request'

// 合同管理相关接口
export interface Contract {
  id: number
  contract_no: string
  title: string
  type: 'purchase' | 'service' | 'lease' | 'other'
  party_a: string // 甲方
  party_b: string // 乙方
  party_b_contact: string
  party_b_phone: string
  contract_amount: number
  currency: 'CNY' | 'USD' | 'EUR'
  sign_date: string
  start_date: string
  end_date: string
  payment_method: 'lump_sum' | 'installment' | 'monthly' | 'quarterly'
  status: 'draft' | 'signed' | 'executing' | 'completed' | 'terminated'
  department_id: number
  department_name: string
  manager_id: number
  manager_name: string
  description?: string
  attachment_urls?: string[]
  created_at: string
  updated_at: string
}

export interface ContractForm {
  title: string
  type: 'purchase' | 'service' | 'lease' | 'other'
  party_a: string
  party_b: string
  party_b_contact: string
  party_b_phone: string
  contract_amount: number
  currency: 'CNY' | 'USD' | 'EUR'
  sign_date: string
  start_date: string
  end_date: string
  payment_method: 'lump_sum' | 'installment' | 'monthly' | 'quarterly'
  department_id: number
  manager_id: number
  description?: string
  attachment_urls?: string[]
}

export interface ContractQuery {
  keyword?: string
  type?: string
  status?: string
  department_id?: number
  manager_id?: number
  start_date?: string
  end_date?: string
  page?: number
  page_size?: number
}

// 获取合同列表
export const getContracts = (params: ContractQuery): Promise<{
  list: Contract[]
  total: number
  page: number
  page_size: number
}> => {
  return request.get('/api/v1/contracts', { params })
}

// 获取合同详情
export const getContractDetail = (id: number): Promise<Contract> => {
  return request.get(`/api/v1/contracts/${id}`)
}

// 创建合同
export const createContract = (data: ContractForm): Promise<Contract> => {
  return request.post('/api/v1/contracts', data)
}

// 更新合同
export const updateContract = (id: number, data: ContractForm): Promise<Contract> => {
  return request.put(`/api/v1/contracts/${id}`, data)
}

// 删除合同
export const deleteContract = (id: number): Promise<void> => {
  return request.delete(`/api/v1/contracts/${id}`)
}

// 更新合同状态
export const updateContractStatus = (id: number, status: string): Promise<void> => {
  return request.put(`/api/v1/contracts/${id}/status`, { status })
}

// 付款计划相关接口
export interface PaymentSchedule {
  id: number
  contract_id: number
  contract_title: string
  phase: number
  phase_name: string
  amount: number
  due_date: string
  status: 'pending' | 'paid' | 'overdue'
  actual_pay_date?: string
  actual_amount?: number
  remark?: string
  created_at: string
  updated_at: string
}

export interface PaymentScheduleForm {
  contract_id: number
  phase: number
  phase_name: string
  amount: number
  due_date: string
  remark?: string
}

export interface PaymentScheduleQuery {
  contract_id?: number
  status?: string
  start_date?: string
  end_date?: string
  page?: number
  page_size?: number
}

// 获取付款计划列表
export const getPaymentSchedules = (params: PaymentScheduleQuery): Promise<{
  list: PaymentSchedule[]
  total: number
  page: number
  page_size: number
}> => {
  return request.get('/api/v1/payment-schedules', { params })
}

// 获取合同的付款计划
export const getContractPaymentSchedules = (contractId: number): Promise<PaymentSchedule[]> => {
  return request.get(`/api/v1/contracts/${contractId}/payment-schedules`)
}

// 创建付款计划
export const createPaymentSchedule = (data: PaymentScheduleForm): Promise<PaymentSchedule> => {
  return request.post('/api/v1/payment-schedules', data)
}

// 更新付款计划
export const updatePaymentSchedule = (id: number, data: PaymentScheduleForm): Promise<PaymentSchedule> => {
  return request.put(`/api/v1/payment-schedules/${id}`, data)
}

// 删除付款计划
export const deletePaymentSchedule = (id: number): Promise<void> => {
  return request.delete(`/api/v1/payment-schedules/${id}`)
}

// 标记付款完成
export const markPaymentCompleted = (id: number, data: {
  actual_pay_date: string
  actual_amount: number
  remark?: string
}): Promise<void> => {
  return request.post(`/api/v1/payment-schedules/${id}/complete`, data)
}

// 批量创建付款计划
export const batchCreatePaymentSchedules = (contractId: number, schedules: Array<{
  phase: number
  phase_name: string
  amount: number
  due_date: string
  remark?: string
}>): Promise<PaymentSchedule[]> => {
  return request.post(`/api/v1/contracts/${contractId}/payment-schedules/batch`, { schedules })
}

// 合同统计相关接口
export interface ContractStatistics {
  total_contracts: number
  active_contracts: number
  expired_contracts: number
  total_amount: number
  paid_amount: number
  pending_amount: number
  contract_types: Array<{
    type: string
    count: number
    amount: number
  }>
  monthly_statistics: Array<{
    month: string
    new_contracts: number
    amount: number
  }>
}

// 获取合同统计数据
export const getContractStatistics = (): Promise<ContractStatistics> => {
  return request.get('/api/v1/contracts/statistics')
}

// 合同分析相关接口
export interface ContractAnalysisQuery {
  start_date?: string
  end_date?: string
  department_id?: number
  type?: string
}

export interface ContractAnalysisData {
  period: string
  contract_count: number
  total_amount: number
  avg_amount: number
  completion_rate: number
}

// 获取合同趋势分析
export const getContractTrendAnalysis = (params: ContractAnalysisQuery): Promise<ContractAnalysisData[]> => {
  return request.get('/api/v1/contracts/analysis/trend', { params })
}

// 获取部门合同分析
export const getDepartmentContractAnalysis = (params: ContractAnalysisQuery): Promise<Array<{
  department_name: string
  contract_count: number
  total_amount: number
  avg_amount: number
  completion_rate: number
}>> => {
  return request.get('/api/v1/contracts/analysis/department', { params })
}

// 获取合同类型分析
export const getContractTypeAnalysis = (params: ContractAnalysisQuery): Promise<Array<{
  type: string
  type_name: string
  contract_count: number
  total_amount: number
  avg_amount: number
}>> => {
  return request.get('/api/v1/contracts/analysis/type', { params })
}

// 文件上传相关接口
export const uploadContractFile = (file: File): Promise<{
  url: string
  filename: string
  size: number
}> => {
  const formData = new FormData()
  formData.append('file', file)
  return request.post('/api/v1/contracts/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
