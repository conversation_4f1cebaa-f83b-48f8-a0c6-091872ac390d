import request from './request'

// 消息相关接口
export interface Message {
  id: string
  message_type: 'notification' | 'announcement' | 'todo' | 'reminder'
  title: string
  content: string
  priority: 'low' | 'normal' | 'high' | 'urgent'
  status: 'draft' | 'sent'
  business_id?: string
  business_type?: string
  scheduled_at?: string
  sent_at?: string
  created_at: string
  updated_at: string
  created_by: string
  sender?: {
    id: string
    user_name: string
    job_title?: string
  }
  read_status?: string
  read_at?: string
}

export interface MessageForm {
  message_type: 'notification' | 'announcement' | 'todo' | 'reminder'
  title: string
  content: string
  priority: 'low' | 'normal' | 'high' | 'urgent'
  receiver_ids: string[]
  business_id?: string
  business_type?: string
  scheduled_at?: string
}

export interface MessageQuery {
  message_type?: string
  priority?: string
  status?: string
  is_read?: boolean
  business_type?: string
  sender_id?: string
  keyword?: string
  start_date?: string
  end_date?: string
  page?: number
  page_size?: number
}

// 获取消息列表
export const getMessages = (params: MessageQuery): Promise<{
  list: Message[]
  total: number
  page: number
  page_size: number
}> => {
  return request.get('/api/v1/messages', { params })
}

// 获取消息详情
export const getMessageDetail = (id: number): Promise<Message> => {
  return request.get(`/api/v1/messages/${id}`)
}

// 创建消息
export const createMessage = (data: MessageForm): Promise<Message> => {
  return request.post('/api/v1/messages', data)
}

// 发送消息
export const sendMessage = (data: MessageForm): Promise<Message> => {
  return request.post('/api/v1/messages/send', data)
}

// 广播消息
export const broadcastMessage = (data: {
  message_type: 'notification' | 'announcement'
  title: string
  content: string
  priority: 'low' | 'normal' | 'high' | 'urgent'
  target_type: 'all' | 'department' | 'role'
  target_ids?: string[]
  scheduled_at?: string
}): Promise<Message> => {
  return request.post('/api/v1/messages/broadcast', data)
}

// 标记消息为已读
export const markMessageAsRead = (messageIds: string[]): Promise<void> => {
  return request.put('/api/v1/messages/read', { message_ids: messageIds })
}

// 获取未读消息数量
export const getUnreadCount = (): Promise<{
  total_count: number
  notification_count: number
  announcement_count: number
  todo_count: number
  reminder_count: number
  by_priority: Record<string, number>
  by_business_type: Record<string, number>
}> => {
  return request.get('/api/v1/messages/unread-count')
}

// 获取消息统计
export const getMessageStatistics = (): Promise<{
  total_messages: number
  unread_messages: number
  today_messages: number
  type_statistics: Array<{
    type: string
    count: number
    unread_count: number
  }>
  priority_statistics: Array<{
    priority: string
    count: number
    unread_count: number
  }>
}> => {
  return request.get('/api/v1/messages/statistics')
}

// 系统通知相关接口
export interface SystemNotification {
  id: number
  title: string
  content: string
  type: 'system' | 'maintenance' | 'update' | 'security'
  level: 'info' | 'warning' | 'error' | 'success'
  status: 'active' | 'inactive'
  start_time?: string
  end_time?: string
  target_users?: string // 'all' | 'department' | 'role' | 'user'
  target_ids?: number[]
  created_at: string
  updated_at: string
}

export interface SystemNotificationForm {
  title: string
  content: string
  type: 'system' | 'maintenance' | 'update' | 'security'
  level: 'info' | 'warning' | 'error' | 'success'
  start_time?: string
  end_time?: string
  target_users: string
  target_ids?: number[]
}

// 获取系统通知列表
export const getSystemNotifications = (params: {
  status?: string
  type?: string
  level?: string
  page?: number
  page_size?: number
}): Promise<{
  list: SystemNotification[]
  total: number
  page: number
  page_size: number
}> => {
  return request.get('/api/v1/system-notifications', { params })
}

// 创建系统通知
export const createSystemNotification = (data: SystemNotificationForm): Promise<SystemNotification> => {
  return request.post('/api/v1/system-notifications', data)
}

// 更新系统通知
export const updateSystemNotification = (id: number, data: SystemNotificationForm): Promise<SystemNotification> => {
  return request.put(`/api/v1/system-notifications/${id}`, data)
}

// 删除系统通知
export const deleteSystemNotification = (id: number): Promise<void> => {
  return request.delete(`/api/v1/system-notifications/${id}`)
}

// 发布系统通知
export const publishSystemNotification = (id: number): Promise<void> => {
  return request.post(`/api/v1/system-notifications/${id}/publish`)
}

// 撤回系统通知
export const revokeSystemNotification = (id: number): Promise<void> => {
  return request.post(`/api/v1/system-notifications/${id}/revoke`)
}

// 获取当前用户的活跃通知
export const getActiveNotifications = (): Promise<SystemNotification[]> => {
  return request.get('/api/v1/system-notifications/active')
}
