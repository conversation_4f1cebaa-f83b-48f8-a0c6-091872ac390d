<template>
  <div class="asset-depreciation-page">
    <a-card :bordered="false">
      <template #title>
        <span>资产折旧管理</span>
      </template>
      
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleCalculate">
            <template #icon><CalculatorOutlined /></template>
            计算折旧
          </a-button>
          <a-button @click="handleRefresh">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 搜索区域 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="资产">
            <a-select
              v-model:value="searchForm.asset_id"
              placeholder="请选择资产"
              allow-clear
              style="width: 200px"
              show-search
              :filter-option="filterAssetOption"
              :options="assetOptions"
            />
          </a-form-item>
          
          <a-form-item label="折旧期间">
            <a-month-picker
              v-model:value="searchForm.period"
              placeholder="请选择期间"
              allow-clear
              style="width: 150px"
              @change="handlePeriodChange"
            />
          </a-form-item>
          
          <a-form-item label="时间范围">
            <a-range-picker
              v-model:value="dateRange"
              style="width: 240px"
              @change="handleDateChange"
            />
          </a-form-item>
          
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon><SearchOutlined /></template>
                搜索
              </a-button>
              <a-button @click="handleReset">
                <template #icon><ReloadOutlined /></template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 折旧记录列表 -->
      <a-table
        :columns="columns"
        :data-source="depreciationList"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        size="middle"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'asset_name'">
            <a @click="handleViewAsset(record)">{{ record.asset_name }}</a>
          </template>
          
          <template v-else-if="column.key === 'depreciation_amount'">
            <span class="amount-text">{{ formatAmount(record.depreciation_amount) }}</span>
          </template>
          
          <template v-else-if="column.key === 'accumulated_depreciation'">
            <span class="amount-text">{{ formatAmount(record.accumulated_depreciation) }}</span>
          </template>
          
          <template v-else-if="column.key === 'book_value'">
            <span class="amount-text">{{ formatAmount(record.book_value) }}</span>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 计算折旧弹窗 -->
    <a-modal
      v-model:open="calculateModalVisible"
      title="计算折旧"
      :confirm-loading="calculateLoading"
      @ok="handleCalculateSubmit"
      @cancel="calculateModalVisible = false"
    >
      <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="折旧期间">
          <a-month-picker
            v-model:value="calculateForm.period"
            placeholder="请选择折旧期间"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="计算说明">
          <a-textarea
            v-model:value="calculateForm.description"
            placeholder="请输入计算说明（可选）"
            :rows="3"
          />
        </a-form-item>
      </a-form>
      
      <a-divider>预计算结果</a-divider>
      
      <div v-if="previewData">
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="涉及资产数量">
            {{ previewData.total_assets }}
          </a-descriptions-item>
          <a-descriptions-item label="总折旧金额">
            <span class="amount-text">{{ formatAmount(previewData.total_depreciation) }}</span>
          </a-descriptions-item>
        </a-descriptions>
        
        <a-table
          :columns="previewColumns"
          :data-source="previewData.records"
          :pagination="false"
          row-key="asset_id"
          size="small"
          style="margin-top: 16px;"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'depreciation_amount'">
              <span class="amount-text">{{ formatAmount(record.depreciation_amount) }}</span>
            </template>
            
            <template v-else-if="column.key === 'book_value'">
              <span class="amount-text">{{ formatAmount(record.book_value) }}</span>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>

    <!-- 资产详情弹窗 -->
    <a-modal
      v-model:open="assetModalVisible"
      title="资产详情"
      :footer="null"
      width="800px"
    >
      <div v-if="currentAsset">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="资产编号">
            {{ currentAsset.asset_no }}
          </a-descriptions-item>
          <a-descriptions-item label="资产名称">
            {{ currentAsset.name }}
          </a-descriptions-item>
          <a-descriptions-item label="资产分类">
            {{ currentAsset.category_name }}
          </a-descriptions-item>
          <a-descriptions-item label="购置价格">
            <span class="amount-text">{{ formatAmount(currentAsset.purchase_price) }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="原值">
            <span class="amount-text">{{ formatAmount(currentAsset.original_value) }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="现值">
            <span class="amount-text">{{ formatAmount(currentAsset.current_value) }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="累计折旧">
            <span class="amount-text">{{ formatAmount(currentAsset.accumulated_depreciation) }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="折旧率">
            {{ (currentAsset.depreciation_rate * 100).toFixed(2) }}%
          </a-descriptions-item>
          <a-descriptions-item label="使用年限">
            {{ currentAsset.useful_life }}年
          </a-descriptions-item>
          <a-descriptions-item label="购置日期">
            {{ currentAsset.purchase_date }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  CalculatorOutlined,
  ReloadOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'
import dayjs, { type Dayjs } from 'dayjs'
import {
  getDepreciationRecords,
  batchCalculateDepreciation,
  getAssets,
  getAssetDetail,
  type DepreciationRecord,
  type DepreciationQuery,
  type Asset
} from '@/api/asset'
import type { FormInstance } from 'ant-design-vue'

const loading = ref(false)
const calculateModalVisible = ref(false)
const calculateLoading = ref(false)
const assetModalVisible = ref(false)

const depreciationList = ref<DepreciationRecord[]>([])
const assetList = ref<Asset[]>([])
const currentAsset = ref<Asset | null>(null)
const previewData = ref<any>(null)
const dateRange = ref<[Dayjs, Dayjs] | null>(null)

const searchForm = reactive<DepreciationQuery>({
  asset_id: undefined,
  period: undefined,
  start_date: undefined,
  end_date: undefined,
  page: 1,
  page_size: 10
})

const calculateForm = reactive({
  period: null as Dayjs | null,
  description: ''
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '资产名称',
    key: 'asset_name',
    width: 200,
    ellipsis: true
  },
  {
    title: '折旧期间',
    dataIndex: 'period',
    key: 'period',
    width: 120
  },
  {
    title: '折旧金额',
    key: 'depreciation_amount',
    width: 120,
    align: 'right'
  },
  {
    title: '累计折旧',
    key: 'accumulated_depreciation',
    width: 120,
    align: 'right'
  },
  {
    title: '账面价值',
    key: 'book_value',
    width: 120,
    align: 'right'
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 160
  }
]

// 预览表格列配置
const previewColumns = [
  {
    title: '资产名称',
    dataIndex: 'asset_name',
    key: 'asset_name',
    width: 200
  },
  {
    title: '折旧金额',
    key: 'depreciation_amount',
    width: 120,
    align: 'right'
  },
  {
    title: '账面价值',
    key: 'book_value',
    width: 120,
    align: 'right'
  }
]

// 资产选项
const assetOptions = computed(() => {
  return assetList.value.map(asset => ({
    label: `${asset.asset_no} - ${asset.name}`,
    value: asset.id
  }))
})

// 资产筛选
const filterAssetOption = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase())
}

// 格式化金额
const formatAmount = (amount: number) => {
  return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`
}

// 期间变化处理
const handlePeriodChange = (date: Dayjs | null) => {
  if (date) {
    searchForm.period = date.format('YYYY-MM')
  } else {
    searchForm.period = undefined
  }
}

// 日期范围变化处理
const handleDateChange = (dates: [Dayjs, Dayjs] | null) => {
  if (dates) {
    searchForm.start_date = dates[0].format('YYYY-MM-DD')
    searchForm.end_date = dates[1].format('YYYY-MM-DD')
  } else {
    searchForm.start_date = undefined
    searchForm.end_date = undefined
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadDepreciationRecords()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    asset_id: undefined,
    period: undefined,
    start_date: undefined,
    end_date: undefined
  })
  dateRange.value = null
  pagination.current = 1
  loadDepreciationRecords()
}

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadDepreciationRecords()
}

// 计算折旧
const handleCalculate = () => {
  calculateForm.period = dayjs()
  calculateForm.description = ''
  previewData.value = null
  calculateModalVisible.value = true
}

// 提交计算
const handleCalculateSubmit = async () => {
  if (!calculateForm.period) {
    message.error('请选择折旧期间')
    return
  }
  
  calculateLoading.value = true
  try {
    await batchCalculateDepreciation({
      period: calculateForm.period.format('YYYY-MM'),
      description: calculateForm.description
    })
    message.success('折旧计算完成')
    calculateModalVisible.value = false
    loadDepreciationRecords()
  } catch (error) {
    message.error('折旧计算失败')
  } finally {
    calculateLoading.value = false
  }
}

// 查看资产详情
const handleViewAsset = async (record: DepreciationRecord) => {
  try {
    currentAsset.value = await getAssetDetail(record.asset_id)
    assetModalVisible.value = true
  } catch (error) {
    message.error('加载资产详情失败')
  }
}

// 刷新数据
const handleRefresh = () => {
  loadDepreciationRecords()
}

// 加载折旧记录
const loadDepreciationRecords = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.current,
      page_size: pagination.pageSize
    }
    const response = await getDepreciationRecords(params)
    depreciationList.value = response.list
    pagination.total = response.total
  } catch (error) {
    message.error('加载折旧记录失败')
  } finally {
    loading.value = false
  }
}

// 加载基础数据
const loadBaseData = async () => {
  try {
    const response = await getAssets({ page: 1, page_size: 100 })
    assetList.value = response.list
  } catch (error) {
    message.error('加载资产数据失败')
  }
}

onMounted(() => {
  loadBaseData()
  loadDepreciationRecords()
})
</script>

<style scoped>
.asset-depreciation-page {
  padding: 24px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.amount-text {
  font-weight: 500;
  color: #1890ff;
}
</style>
