<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>工作台</h1>
      <p>欢迎使用医院内部控制与运营管理系统</p>
    </div>

    <a-row :gutter="16" class="stats-row">
      <a-col :span="6">
        <a-card>
          <a-statistic title="待办任务" :value="stats.expense_stats?.pending_expense_applications || 0" :value-style="{ color: '#1890ff' }">
            <template #prefix>
              <ClockCircleOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>

      <a-col :span="6">
        <a-card>
          <a-statistic title="本月报销" :value="stats.expense_stats?.total_expense_amount || 0" suffix="元" :value-style="{ color: '#52c41a' }">
            <template #prefix>
              <DollarOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>

      <a-col :span="6">
        <a-card>
          <a-statistic title="预算执行率" :value="stats.budget_stats?.usage_rate || 0" suffix="%" :value-style="{ color: '#faad14' }">
            <template #prefix>
              <PieChartOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>

      <a-col :span="6">
        <a-card>
          <a-statistic title="合同数量" :value="stats.contract_stats?.active_contracts || 0" :value-style="{ color: '#722ed1' }">
            <template #prefix>
              <FileTextOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="16" class="content-row">
      <a-col :span="12">
        <a-card title="待办任务" :bordered="false">
          <a-list :data-source="todoList" :loading="loading">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta :title="item.title" :description="item.description" />
                <template #actions>
                  <a-button type="link" size="small">处理</a-button>
                </template>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>

      <a-col :span="12">
        <a-card title="最近申请" :bordered="false">
          <a-list :data-source="recentApplications" :loading="loading">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta :title="item.title" :description="item.description" />
                <template #actions>
                  <a-tag :color="getStatusColor(item.status)">
                    {{ item.status }}
                  </a-tag>
                </template>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="16" class="content-row">
      <a-col :span="24">
        <a-card title="快捷操作" :bordered="false">
          <div class="quick-actions">
            <a-button type="primary" size="large" @click="goToExpense">
              <PlusOutlined />
              新建报销
            </a-button>
            <a-button size="large" @click="goToBudget">
              <BarChartOutlined />
              预算查询
            </a-button>
            <a-button size="large" @click="goToContract">
              <FileAddOutlined />
              合同管理
            </a-button>
            <a-button size="large" @click="goToAsset">
              <DatabaseOutlined />
              资产管理
            </a-button>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  ClockCircleOutlined,
  DollarOutlined,
  PieChartOutlined,
  FileTextOutlined,
  PlusOutlined,
  BarChartOutlined,
  FileAddOutlined,
  DatabaseOutlined
} from '@ant-design/icons-vue'
import { getDashboardStatistics, type DashboardStatistics } from '@/api/common'

const router = useRouter()
const loading = ref(false)

const stats = ref<DashboardStatistics>({
  budget_stats: {
    total_budget: 0,
    used_budget: 0,
    frozen_budget: 0,
    available_budget: 0,
    usage_rate: 0
  },
  expense_stats: {
    total_expense_applications: 0,
    total_expense_amount: 0,
    pending_expense_applications: 0,
    total_payments: 0,
    total_payment_amount: 0
  },
  procurement_stats: {
    total_suppliers: 0,
    total_purchase_requisitions: 0,
    total_purchase_amount: 0,
    pending_purchase_requisitions: 0
  },
  contract_stats: {
    total_contracts: 0,
    total_contract_amount: 0,
    active_contracts: 0,
    total_payment_schedules: 0,
    total_schedule_amount: 0
  },
  asset_stats: {
    total_assets: 0,
    total_asset_value: 0,
    in_use_assets: 0,
    idle_assets: 0,
    maintenance_assets: 0,
    scrapped_assets: 0,
    total_categories: 0
  }
})

const todoList = ref([
  {
    title: '张三的差旅费报销申请',
    description: '2025-07-30 提交，金额：1,250.00元'
  },
  {
    title: '李四的办公用品采购申请',
    description: '2025-07-29 提交，金额：3,500.00元'
  },
  {
    title: '王五的培训费用申请',
    description: '2025-07-28 提交，金额：2,800.00元'
  }
])

const recentApplications = ref([
  {
    title: '医疗设备采购合同',
    description: '2025-07-30 创建',
    status: '审批中'
  },
  {
    title: '办公室装修费用',
    description: '2025-07-29 创建',
    status: '已完成'
  },
  {
    title: '员工培训费用',
    description: '2025-07-28 创建',
    status: '待审批'
  }
])

const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    '待审批': 'orange',
    '审批中': 'blue',
    '已完成': 'green',
    '已驳回': 'red'
  }
  return colorMap[status] || 'default'
}

const goToExpense = () => {
  router.push('/expense/applications')
}

const goToBudget = () => {
  router.push('/budget/items')
}

const goToContract = () => {
  router.push('/contract/contracts')
}

const goToAsset = () => {
  router.push('/asset/assets')
}

const loadDashboardData = async () => {
  try {
    loading.value = true
    const data = await getDashboardStatistics()
    stats.value = data
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
    message.error('加载仪表板数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard {
  padding: 24px;
}

.dashboard-header {
  margin-bottom: 24px;
}

.dashboard-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #262626;
}

.dashboard-header p {
  margin: 0;
  color: #8c8c8c;
}

.stats-row {
  margin-bottom: 24px;
}

.content-row {
  margin-bottom: 24px;
}

.quick-actions {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.quick-actions .ant-btn {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
