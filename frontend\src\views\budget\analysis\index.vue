<template>
  <div class="budget-analysis-page">
    <a-card :bordered="false">
      <template #title>
        <span>预算分析</span>
      </template>
      
      <template #extra>
        <a-space>
          <a-range-picker
            v-model:value="dateRange"
            picker="month"
            @change="handleDateChange"
          />
          <a-button @click="handleRefresh">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 预算执行概览 -->
      <div class="budget-overview">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="预算总额"
                :value="overview.total_budget"
                :precision="2"
                suffix="万元"
                :value-style="{ color: '#1890ff' }"
              />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="已执行金额"
                :value="overview.executed_amount"
                :precision="2"
                suffix="万元"
                :value-style="{ color: '#52c41a' }"
              />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="执行率"
                :value="overview.execution_rate"
                :precision="2"
                suffix="%"
                :value-style="{ color: getExecutionRateColor(overview.execution_rate) }"
              />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="剩余预算"
                :value="overview.remaining_budget"
                :precision="2"
                suffix="万元"
                :value-style="{ color: '#fa8c16' }"
              />
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 图表区域 -->
      <a-row :gutter="16" class="chart-section">
        <!-- 预算执行趋势图 -->
        <a-col :span="12">
          <a-card title="预算执行趋势" size="small">
            <div ref="trendChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
        
        <!-- 部门预算分布 -->
        <a-col :span="12">
          <a-card title="部门预算分布" size="small">
            <div ref="departmentChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" class="chart-section">
        <!-- 科目预算分析 -->
        <a-col :span="12">
          <a-card title="科目预算分析" size="small">
            <div ref="subjectChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
        
        <!-- 预算执行率对比 -->
        <a-col :span="12">
          <a-card title="预算执行率对比" size="small">
            <div ref="executionChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 预算预警 -->
      <a-card title="预算预警" size="small" class="warning-section">
        <a-table
          :columns="warningColumns"
          :data-source="warningList"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'warning_type'">
              <a-tag :color="getWarningTypeColor(record.warning_type)">
                {{ getWarningTypeText(record.warning_type) }}
              </a-tag>
            </template>
            
            <template v-else-if="column.key === 'execution_rate'">
              <span :style="{ color: getExecutionRateColor(record.execution_rate) }">
                {{ record.execution_rate.toFixed(2) }}%
              </span>
            </template>
            
            <template v-else-if="column.key === 'budget_amount'">
              <span class="amount-text">{{ formatAmount(record.budget_amount) }}</span>
            </template>
            
            <template v-else-if="column.key === 'used_amount'">
              <span class="amount-text">{{ formatAmount(record.used_amount) }}</span>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 详细分析表格 -->
      <a-card title="详细分析" size="small" class="detail-section">
        <a-table
          :columns="detailColumns"
          :data-source="detailList"
          :loading="loading"
          :pagination="pagination"
          row-key="id"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'budget_amount'">
              <span class="amount-text">{{ formatAmount(record.budget_amount) }}</span>
            </template>
            
            <template v-else-if="column.key === 'used_amount'">
              <span class="amount-text">{{ formatAmount(record.used_amount) }}</span>
            </template>
            
            <template v-else-if="column.key === 'remaining_amount'">
              <span class="amount-text">{{ formatAmount(record.remaining_amount) }}</span>
            </template>
            
            <template v-else-if="column.key === 'execution_rate'">
              <a-progress
                :percent="record.execution_rate"
                :status="getProgressStatus(record.execution_rate)"
                size="small"
              />
            </template>
          </template>
        </a-table>
      </a-card>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { ReloadOutlined } from '@ant-design/icons-vue'
import dayjs, { type Dayjs } from 'dayjs'
import * as echarts from 'echarts'
import {
  getBudgetAnalysis,
  getBudgetTrendAnalysis,
  getDepartmentBudgetAnalysis,
  getSubjectBudgetAnalysis,
  getBudgetWarnings,
  type BudgetAnalysisQuery
} from '@/api/budget'

const loading = ref(false)
const dateRange = ref<[Dayjs, Dayjs]>([
  dayjs().startOf('year'),
  dayjs().endOf('year')
])

const trendChartRef = ref<HTMLDivElement>()
const departmentChartRef = ref<HTMLDivElement>()
const subjectChartRef = ref<HTMLDivElement>()
const executionChartRef = ref<HTMLDivElement>()

let trendChart: echarts.ECharts | null = null
let departmentChart: echarts.ECharts | null = null
let subjectChart: echarts.ECharts | null = null
let executionChart: echarts.ECharts | null = null

const overview = ref({
  total_budget: 0,
  executed_amount: 0,
  execution_rate: 0,
  remaining_budget: 0
})

const warningList = ref<any[]>([])
const detailList = ref<any[]>([])

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 预警表格列配置
const warningColumns = [
  {
    title: '预警类型',
    key: 'warning_type',
    width: 100
  },
  {
    title: '部门',
    dataIndex: 'department_name',
    key: 'department_name',
    width: 150
  },
  {
    title: '预算科目',
    dataIndex: 'subject_name',
    key: 'subject_name',
    width: 200
  },
  {
    title: '预算金额',
    key: 'budget_amount',
    width: 120,
    align: 'right'
  },
  {
    title: '已用金额',
    key: 'used_amount',
    width: 120,
    align: 'right'
  },
  {
    title: '执行率',
    key: 'execution_rate',
    width: 100,
    align: 'center'
  },
  {
    title: '预警说明',
    dataIndex: 'warning_message',
    key: 'warning_message'
  }
]

// 详细分析表格列配置
const detailColumns = [
  {
    title: '部门',
    dataIndex: 'department_name',
    key: 'department_name',
    width: 150
  },
  {
    title: '预算科目',
    dataIndex: 'subject_name',
    key: 'subject_name',
    width: 200
  },
  {
    title: '预算金额',
    key: 'budget_amount',
    width: 120,
    align: 'right'
  },
  {
    title: '已用金额',
    key: 'used_amount',
    width: 120,
    align: 'right'
  },
  {
    title: '剩余金额',
    key: 'remaining_amount',
    width: 120,
    align: 'right'
  },
  {
    title: '执行率',
    key: 'execution_rate',
    width: 150,
    align: 'center'
  },
  {
    title: '更新时间',
    dataIndex: 'updated_at',
    key: 'updated_at',
    width: 160
  }
]

// 获取执行率颜色
const getExecutionRateColor = (rate: number) => {
  if (rate >= 90) return '#f5222d'
  if (rate >= 80) return '#fa8c16'
  if (rate >= 60) return '#52c41a'
  return '#1890ff'
}

// 获取预警类型颜色
const getWarningTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'over_budget': 'red',
    'near_limit': 'orange',
    'low_usage': 'blue'
  }
  return colorMap[type] || 'default'
}

// 获取预警类型文本
const getWarningTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    'over_budget': '超预算',
    'near_limit': '接近上限',
    'low_usage': '使用率低'
  }
  return textMap[type] || type
}

// 获取进度条状态
const getProgressStatus = (rate: number) => {
  if (rate >= 100) return 'exception'
  if (rate >= 80) return 'active'
  return 'normal'
}

// 格式化金额
const formatAmount = (amount: number) => {
  return `¥${(amount / 10000).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}万`
}

// 日期范围变化处理
const handleDateChange = () => {
  loadAnalysisData()
}

// 刷新数据
const handleRefresh = () => {
  loadAnalysisData()
}

// 加载分析数据
const loadAnalysisData = async () => {
  loading.value = true
  try {
    const params: BudgetAnalysisQuery = {
      start_date: dateRange.value[0].format('YYYY-MM-DD'),
      end_date: dateRange.value[1].format('YYYY-MM-DD')
    }
    
    const [analysisData, trendData, departmentData, subjectData, warningData] = await Promise.all([
      getBudgetAnalysis(params),
      getBudgetTrendAnalysis(params),
      getDepartmentBudgetAnalysis(params),
      getSubjectBudgetAnalysis(params),
      getBudgetWarnings(params)
    ])
    
    // 更新概览数据
    overview.value = analysisData.overview
    
    // 更新预警数据
    warningList.value = warningData
    
    // 更新详细数据
    detailList.value = analysisData.details
    pagination.total = analysisData.details.length
    
    // 更新图表
    nextTick(() => {
      updateTrendChart(trendData)
      updateDepartmentChart(departmentData)
      updateSubjectChart(subjectData)
      updateExecutionChart(analysisData.execution_comparison)
    })
  } catch (error) {
    message.error('加载预算分析数据失败')
  } finally {
    loading.value = false
  }
}

// 更新趋势图表
const updateTrendChart = (data: any[]) => {
  if (!trendChart) return
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['预算金额', '执行金额']
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.period)
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}万'
      }
    },
    series: [
      {
        name: '预算金额',
        type: 'line',
        data: data.map(item => (item.budget_amount / 10000).toFixed(2))
      },
      {
        name: '执行金额',
        type: 'line',
        data: data.map(item => (item.executed_amount / 10000).toFixed(2))
      }
    ]
  }
  
  trendChart.setOption(option)
}

// 更新部门图表
const updateDepartmentChart = (data: any[]) => {
  if (!departmentChart) return
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}万 ({d}%)'
    },
    series: [
      {
        name: '部门预算',
        type: 'pie',
        radius: '50%',
        data: data.map(item => ({
          name: item.department_name,
          value: (item.budget_amount / 10000).toFixed(2)
        }))
      }
    ]
  }
  
  departmentChart.setOption(option)
}

// 更新科目图表
const updateSubjectChart = (data: any[]) => {
  if (!subjectChart) return
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.subject_name),
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}万'
      }
    },
    series: [
      {
        name: '预算金额',
        type: 'bar',
        data: data.map(item => (item.budget_amount / 10000).toFixed(2))
      }
    ]
  }
  
  subjectChart.setOption(option)
}

// 更新执行率图表
const updateExecutionChart = (data: any[]) => {
  if (!executionChart) return
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.department_name)
    },
    yAxis: {
      type: 'value',
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: '执行率',
        type: 'bar',
        data: data.map(item => ({
          value: item.execution_rate.toFixed(2),
          itemStyle: {
            color: getExecutionRateColor(item.execution_rate)
          }
        }))
      }
    ]
  }
  
  executionChart.setOption(option)
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    if (trendChartRef.value) {
      trendChart = echarts.init(trendChartRef.value)
    }
    if (departmentChartRef.value) {
      departmentChart = echarts.init(departmentChartRef.value)
    }
    if (subjectChartRef.value) {
      subjectChart = echarts.init(subjectChartRef.value)
    }
    if (executionChartRef.value) {
      executionChart = echarts.init(executionChartRef.value)
    }
    
    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      trendChart?.resize()
      departmentChart?.resize()
      subjectChart?.resize()
      executionChart?.resize()
    })
  })
}

onMounted(() => {
  initCharts()
  loadAnalysisData()
})
</script>

<style scoped>
.budget-analysis-page {
  padding: 24px;
}

.budget-overview {
  margin-bottom: 24px;
}

.chart-section {
  margin-bottom: 24px;
}

.warning-section {
  margin-bottom: 24px;
}

.detail-section {
  margin-bottom: 24px;
}

.amount-text {
  font-weight: 500;
  color: #1890ff;
}
</style>
