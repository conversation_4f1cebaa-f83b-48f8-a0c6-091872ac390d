<template>
  <div class="budget-subjects-page">
    <a-card :bordered="false">
      <template #title>
        <span>预算科目管理</span>
      </template>
      
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon><PlusOutlined /></template>
            新增科目
          </a-button>
          <a-button @click="handleRefresh">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="subjectList"
        :loading="loading"
        :pagination="false"
        row-key="id"
        :default-expand-all-rows="true"
        size="middle"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'type'">
            <a-tag :color="record.type === 'income' ? 'green' : 'blue'">
              {{ record.type === 'income' ? '收入' : '支出' }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)">
                编辑
              </a-button>
              <a-button type="link" size="small" @click="handleAddChild(record)">
                添加子科目
              </a-button>
              <a-popconfirm
                title="确定要删除这个科目吗？"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑科目弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :confirm-loading="modalLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="上级科目" name="parent_id">
          <a-tree-select
            v-model:value="formData.parent_id"
            :tree-data="subjectTreeOptions"
            placeholder="请选择上级科目（不选则为顶级科目）"
            allow-clear
            tree-default-expand-all
          />
        </a-form-item>
        
        <a-form-item label="科目编码" name="code">
          <a-input v-model:value="formData.code" placeholder="请输入科目编码" />
        </a-form-item>
        
        <a-form-item label="科目名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入科目名称" />
        </a-form-item>
        
        <a-form-item label="科目类型" name="type">
          <a-radio-group v-model:value="formData.type">
            <a-radio value="INCOME">收入</a-radio>
            <a-radio value="EXPENSE">支出</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="会计科目代码" name="accounting_subject_code">
          <a-input
            v-model:value="formData.accounting_subject_code"
            placeholder="请输入会计科目代码（可选）"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import {
  getBudgetSubjectTree,
  createBudgetSubject,
  updateBudgetSubject,
  type BudgetSubject,
  type BudgetSubjectForm
} from '@/api/budget'
import type { Rule } from 'ant-design-vue/es/form'
import type { FormInstance } from 'ant-design-vue'

const loading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const formRef = ref<FormInstance>()
const editingId = ref<number | null>(null)

const subjectList = ref<BudgetSubject[]>([])

const formData = reactive<BudgetSubjectForm>({
  code: '',
  name: '',
  parent_id: undefined,
  type: 'EXPENSE',
  accounting_subject_code: ''
})

// 表格列配置
const columns = [
  {
    title: '科目编码',
    dataIndex: 'code',
    key: 'code',
    width: 150
  },
  {
    title: '科目名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '科目类型',
    key: 'type',
    width: 100
  },
  {
    title: '层级',
    dataIndex: 'level',
    key: 'level',
    width: 80
  },
  {
    title: '会计科目代码',
    dataIndex: 'accounting_subject_code',
    key: 'accounting_subject_code',
    width: 150,
    ellipsis: true
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 表单验证规则
const rules: Record<string, Rule[]> = {
  code: [
    { required: true, message: '请输入科目编码', trigger: 'blur' },
    { pattern: /^[A-Z0-9]+$/, message: '科目编码只能包含大写字母和数字', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入科目名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择科目类型', trigger: 'change' }
  ]
}

// 弹窗标题
const modalTitle = computed(() => {
  return editingId.value ? '编辑预算科目' : '新增预算科目'
})

// 科目树形选择器选项
const subjectTreeOptions = computed(() => {
  const convertToTreeData = (subjects: BudgetSubject[]): any[] => {
    return subjects.map(subject => ({
      title: `${subject.code} - ${subject.name}`,
      value: subject.id,
      key: subject.id,
      children: subject.children ? convertToTreeData(subject.children) : undefined
    }))
  }
  
  // 编辑时需要过滤掉当前科目及其子科目
  let filteredSubjects = subjectList.value
  if (editingId.value) {
    const filterSubjects = (subjects: BudgetSubject[], excludeId: number): BudgetSubject[] => {
      return subjects.filter(subject => {
        if (subject.id === excludeId) return false
        if (subject.children) {
          subject.children = filterSubjects(subject.children, excludeId)
        }
        return true
      })
    }
    filteredSubjects = filterSubjects([...subjectList.value], editingId.value)
  }
  
  return convertToTreeData(filteredSubjects)
})

// 加载科目数据
const loadSubjects = async () => {
  loading.value = true
  try {
    subjectList.value = await getBudgetSubjectTree()
  } catch (error) {
    message.error('加载预算科目数据失败')
  } finally {
    loading.value = false
  }
}

// 新增科目
const handleAdd = () => {
  editingId.value = null
  resetForm()
  modalVisible.value = true
}

// 添加子科目
const handleAddChild = (parent: BudgetSubject) => {
  editingId.value = null
  resetForm()
  formData.parent_id = parent.id
  formData.type = parent.type // 继承父科目类型
  modalVisible.value = true
}

// 编辑科目
const handleEdit = (record: BudgetSubject) => {
  editingId.value = record.id
  Object.assign(formData, {
    code: record.code,
    name: record.name,
    parent_id: record.parent_id,
    type: record.type,
    accounting_subject_code: record.accounting_subject_code || ''
  })
  modalVisible.value = true
}

// 删除科目
const handleDelete = async (record: BudgetSubject) => {
  try {
    // 注意：这里需要实现删除API
    // await deleteBudgetSubject(record.id)
    message.success('删除成功')
    loadSubjects()
  } catch (error) {
    message.error('删除失败')
  }
}

// 刷新数据
const handleRefresh = () => {
  loadSubjects()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    modalLoading.value = true
    
    if (editingId.value) {
      await updateBudgetSubject(editingId.value, formData)
      message.success('更新成功')
    } else {
      await createBudgetSubject(formData)
      message.success('创建成功')
    }
    
    modalVisible.value = false
    loadSubjects()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    modalLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    code: '',
    name: '',
    parent_id: undefined,
    type: 'EXPENSE',
    accounting_subject_code: ''
  })
  formRef.value?.resetFields()
}

onMounted(() => {
  loadSubjects()
})
</script>

<style scoped>
.budget-subjects-page {
  padding: 24px;
}
</style>
