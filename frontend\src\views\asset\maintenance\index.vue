<template>
  <div class="asset-maintenance-page">
    <a-card :bordered="false">
      <template #title>
        <span>资产维护管理</span>
      </template>
      
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon><PlusOutlined /></template>
            新增维护记录
          </a-button>
          <a-button @click="handleRefresh">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 搜索区域 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="资产">
            <a-select
              v-model:value="searchForm.asset_id"
              placeholder="请选择资产"
              allow-clear
              style="width: 200px"
              show-search
              :filter-option="filterAssetOption"
              :options="assetOptions"
            />
          </a-form-item>
          
          <a-form-item label="维护类型">
            <a-select
              v-model:value="searchForm.maintenance_type"
              placeholder="请选择类型"
              allow-clear
              style="width: 120px"
            >
              <a-select-option value="routine">例行维护</a-select-option>
              <a-select-option value="repair">故障维修</a-select-option>
              <a-select-option value="upgrade">升级改造</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="维护状态">
            <a-select
              v-model:value="searchForm.status"
              placeholder="请选择状态"
              allow-clear
              style="width: 120px"
            >
              <a-select-option value="planned">计划中</a-select-option>
              <a-select-option value="in_progress">进行中</a-select-option>
              <a-select-option value="completed">已完成</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="维护时间">
            <a-range-picker
              v-model:value="dateRange"
              style="width: 240px"
              @change="handleDateChange"
            />
          </a-form-item>
          
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon><SearchOutlined /></template>
                搜索
              </a-button>
              <a-button @click="handleReset">
                <template #icon><ReloadOutlined /></template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 维护记录列表 -->
      <a-table
        :columns="columns"
        :data-source="maintenanceList"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        size="middle"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'asset_name'">
            <a @click="handleViewAsset(record)">{{ record.asset_name }}</a>
          </template>
          
          <template v-else-if="column.key === 'maintenance_type'">
            <a-tag :color="getTypeColor(record.maintenance_type)">
              {{ getTypeText(record.maintenance_type) }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'maintenance_cost'">
            <span class="amount-text">{{ formatAmount(record.maintenance_cost) }}</span>
          </template>
          
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)">
                编辑
              </a-button>
              <a-button 
                type="link" 
                size="small" 
                @click="handleComplete(record)"
                v-if="record.status !== 'completed'"
              >
                完成
              </a-button>
              <a-popconfirm
                title="确定要删除这条维护记录吗？"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑维护记录弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :confirm-loading="modalLoading"
      width="800px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="关联资产" name="asset_id">
          <a-select
            v-model:value="formData.asset_id"
            placeholder="请选择资产"
            show-search
            :filter-option="filterAssetOption"
            :options="assetOptions"
          />
        </a-form-item>
        
        <a-form-item label="维护类型" name="maintenance_type">
          <a-radio-group v-model:value="formData.maintenance_type">
            <a-radio value="routine">例行维护</a-radio>
            <a-radio value="repair">故障维修</a-radio>
            <a-radio value="upgrade">升级改造</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="维护日期" name="maintenance_date">
          <a-date-picker
            v-model:value="formData.maintenance_date"
            placeholder="请选择维护日期"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="维护费用" name="maintenance_cost">
          <a-input-number
            v-model:value="formData.maintenance_cost"
            :min="0"
            :precision="2"
            placeholder="请输入维护费用"
            style="width: 100%"
            :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/¥\s?|(,*)/g, '')"
          />
        </a-form-item>
        
        <a-form-item label="维护公司" name="maintenance_company">
          <a-input v-model:value="formData.maintenance_company" placeholder="请输入维护公司" />
        </a-form-item>
        
        <a-form-item label="维护人员" name="maintenance_person">
          <a-input v-model:value="formData.maintenance_person" placeholder="请输入维护人员" />
        </a-form-item>
        
        <a-form-item label="下次维护日期" name="next_maintenance_date">
          <a-date-picker
            v-model:value="formData.next_maintenance_date"
            placeholder="请选择下次维护日期"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="维护描述" name="description">
          <a-textarea
            v-model:value="formData.description"
            placeholder="请输入维护描述"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 资产详情弹窗 -->
    <a-modal
      v-model:open="assetModalVisible"
      title="资产详情"
      :footer="null"
      width="800px"
    >
      <div v-if="currentAsset">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="资产编号">
            {{ currentAsset.asset_no }}
          </a-descriptions-item>
          <a-descriptions-item label="资产名称">
            {{ currentAsset.name }}
          </a-descriptions-item>
          <a-descriptions-item label="资产分类">
            {{ currentAsset.category_name }}
          </a-descriptions-item>
          <a-descriptions-item label="存放位置">
            {{ currentAsset.location }}
          </a-descriptions-item>
          <a-descriptions-item label="所属部门">
            {{ currentAsset.department_name }}
          </a-descriptions-item>
          <a-descriptions-item label="资产管理员" v-if="currentAsset.custodian_name">
            {{ currentAsset.custodian_name }}
          </a-descriptions-item>
          <a-descriptions-item label="维护周期" v-if="currentAsset.maintenance_cycle">
            {{ currentAsset.maintenance_cycle }}月
          </a-descriptions-item>
          <a-descriptions-item label="上次维护" v-if="currentAsset.last_maintenance_date">
            {{ currentAsset.last_maintenance_date }}
          </a-descriptions-item>
          <a-descriptions-item label="下次维护" v-if="currentAsset.next_maintenance_date">
            {{ currentAsset.next_maintenance_date }}
          </a-descriptions-item>
          <a-descriptions-item label="资产状态">
            <a-tag :color="getAssetStatusColor(currentAsset.status)">
              {{ getAssetStatusText(currentAsset.status) }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'
import dayjs, { type Dayjs } from 'dayjs'
import {
  getMaintenanceRecords,
  createMaintenanceRecord,
  updateMaintenanceRecord,
  getAssets,
  getAssetDetail,
  type MaintenanceRecord,
  type MaintenanceRecordForm,
  type MaintenanceQuery,
  type Asset
} from '@/api/asset'
import type { Rule } from 'ant-design-vue/es/form'
import type { FormInstance } from 'ant-design-vue'

const loading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const assetModalVisible = ref(false)
const formRef = ref<FormInstance>()
const editingId = ref<number | null>(null)

const maintenanceList = ref<MaintenanceRecord[]>([])
const assetList = ref<Asset[]>([])
const currentAsset = ref<Asset | null>(null)
const dateRange = ref<[Dayjs, Dayjs] | null>(null)

const searchForm = reactive<MaintenanceQuery>({
  asset_id: undefined,
  maintenance_type: undefined,
  status: undefined,
  start_date: undefined,
  end_date: undefined,
  page: 1,
  page_size: 10
})

const formData = reactive<MaintenanceRecordForm & {
  maintenance_date: Dayjs | null
  next_maintenance_date: Dayjs | null
}>({
  asset_id: 0,
  maintenance_type: 'routine',
  maintenance_date: null,
  maintenance_cost: 0,
  maintenance_company: '',
  maintenance_person: '',
  description: '',
  next_maintenance_date: null
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '资产名称',
    key: 'asset_name',
    width: 200,
    ellipsis: true
  },
  {
    title: '维护类型',
    key: 'maintenance_type',
    width: 100
  },
  {
    title: '维护日期',
    dataIndex: 'maintenance_date',
    key: 'maintenance_date',
    width: 120
  },
  {
    title: '维护费用',
    key: 'maintenance_cost',
    width: 120,
    align: 'right'
  },
  {
    title: '维护公司',
    dataIndex: 'maintenance_company',
    key: 'maintenance_company',
    width: 150,
    ellipsis: true
  },
  {
    title: '维护人员',
    dataIndex: 'maintenance_person',
    key: 'maintenance_person',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '下次维护',
    dataIndex: 'next_maintenance_date',
    key: 'next_maintenance_date',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right'
  }
]

// 表单验证规则
const rules: Record<string, Rule[]> = {
  asset_id: [
    { required: true, message: '请选择关联资产', trigger: 'change' }
  ],
  maintenance_type: [
    { required: true, message: '请选择维护类型', trigger: 'change' }
  ],
  maintenance_date: [
    { required: true, message: '请选择维护日期', trigger: 'change' }
  ],
  maintenance_cost: [
    { required: true, message: '请输入维护费用', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入维护描述', trigger: 'blur' }
  ]
}

// 弹窗标题
const modalTitle = computed(() => {
  return editingId.value ? '编辑维护记录' : '新增维护记录'
})

// 资产选项
const assetOptions = computed(() => {
  return assetList.value.map(asset => ({
    label: `${asset.asset_no} - ${asset.name}`,
    value: asset.id
  }))
})

// 资产筛选
const filterAssetOption = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase())
}

// 获取维护类型颜色
const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'routine': 'blue',
    'repair': 'orange',
    'upgrade': 'green'
  }
  return colorMap[type] || 'default'
}

// 获取维护类型文本
const getTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    'routine': '例行维护',
    'repair': '故障维修',
    'upgrade': '升级改造'
  }
  return textMap[type] || type
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'planned': 'default',
    'in_progress': 'processing',
    'completed': 'success'
  }
  return colorMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'planned': '计划中',
    'in_progress': '进行中',
    'completed': '已完成'
  }
  return textMap[status] || status
}

// 获取资产状态颜色
const getAssetStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'normal': 'green',
    'maintenance': 'orange',
    'idle': 'blue',
    'scrapped': 'red'
  }
  return colorMap[status] || 'default'
}

// 获取资产状态文本
const getAssetStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'normal': '正常',
    'maintenance': '维护中',
    'idle': '闲置',
    'scrapped': '报废'
  }
  return textMap[status] || status
}

// 格式化金额
const formatAmount = (amount: number) => {
  return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`
}
</script>

<style scoped>
.asset-maintenance-page {
  padding: 24px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.amount-text {
  font-weight: 500;
  color: #1890ff;
}
</style>
