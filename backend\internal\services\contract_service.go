package services

import (
	"errors"
	"fmt"
	"hospital-management/internal/dto"
	"hospital-management/internal/models"
	"hospital-management/pkg/logger"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type ContractService struct {
	db *gorm.DB
}

func NewContractService(db *gorm.DB) *ContractService {
	return &ContractService{db: db}
}

// ===== 合同管理 =====

// GetContracts 获取合同列表
func (s *ContractService) GetContracts(req *dto.ContractListRequest) ([]dto.ContractResponse, int64, error) {
	var contracts []models.Contract
	var total int64

	query := s.db.Model(&models.Contract{}).
		Preload("Counterparty").
		Preload("PaymentSchedules")

	// 搜索条件
	if req.Keyword != "" {
		query = query.Where("contract_name ILIKE ? OR contract_code ILIKE ? OR counterparty_name ILIKE ?", 
			"%"+req.Keyword+"%", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}
	if req.ContractType != "" {
		query = query.Where("contract_type = ?", req.ContractType)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.CounterpartyID != nil {
		query = query.Where("counterparty_id = ?", *req.CounterpartyID)
	}
	if req.StartDateFrom != nil {
		query = query.Where("start_date >= ?", *req.StartDateFrom)
	}
	if req.StartDateTo != nil {
		query = query.Where("start_date <= ?", *req.StartDateTo)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count contracts:", err)
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&contracts).Error; err != nil {
		logger.Error("Failed to get contracts:", err)
		return nil, 0, err
	}

	// 转换响应
	responses := make([]dto.ContractResponse, len(contracts))
	for i, contract := range contracts {
		responses[i] = s.convertContractToResponse(contract)
	}

	return responses, total, nil
}

// GetContract 获取合同详情
func (s *ContractService) GetContract(id uuid.UUID) (*dto.ContractResponse, error) {
	var contract models.Contract
	if err := s.db.Preload("Counterparty").Preload("PaymentSchedules").First(&contract, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("合同不存在")
		}
		return nil, err
	}

	response := s.convertContractToResponse(contract)
	return &response, nil
}

// CreateContract 创建合同
func (s *ContractService) CreateContract(req *dto.CreateContractRequest, userID uuid.UUID) (*dto.ContractResponse, error) {
	// 验证对方单位是否存在
	if req.CounterpartyID != nil {
		var supplier models.Supplier
		if err := s.db.First(&supplier, *req.CounterpartyID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errors.New("对方单位不存在")
			}
			return nil, err
		}
	}

	// 生成合同编号
	contractCode, err := s.generateContractCode()
	if err != nil {
		return nil, err
	}

	// 解析日期
	var startDate, endDate *time.Time
	if req.StartDate != nil {
		if parsed, err := time.Parse("2006-01-02", *req.StartDate); err != nil {
			return nil, errors.New("开始日期格式错误")
		} else {
			startDate = &parsed
		}
	}
	if req.EndDate != nil {
		if parsed, err := time.Parse("2006-01-02", *req.EndDate); err != nil {
			return nil, errors.New("结束日期格式错误")
		} else {
			endDate = &parsed
		}
	}

	// 验证日期逻辑
	if startDate != nil && endDate != nil && startDate.After(*endDate) {
		return nil, errors.New("开始日期不能晚于结束日期")
	}

	contract := models.Contract{
		ContractCode:     contractCode,
		ContractName:     req.ContractName,
		ContractType:     req.ContractType,
		CounterpartyID:   req.CounterpartyID,
		CounterpartyName: req.CounterpartyName,
		TotalAmount:      req.TotalAmount,
		StartDate:        startDate,
		EndDate:          endDate,
		Status:           "DRAFT",
	}
	contract.CreatedBy = &userID

	if err := s.db.Create(&contract).Error; err != nil {
		logger.Error("Failed to create contract:", err)
		return nil, err
	}

	// 预加载关联数据
	if err := s.db.Preload("Counterparty").Preload("PaymentSchedules").First(&contract, contract.ID).Error; err != nil {
		logger.Error("Failed to preload contract data:", err)
		return nil, err
	}

	response := s.convertContractToResponse(contract)
	return &response, nil
}

// UpdateContract 更新合同
func (s *ContractService) UpdateContract(id uuid.UUID, req *dto.UpdateContractRequest, userID uuid.UUID) (*dto.ContractResponse, error) {
	var contract models.Contract
	if err := s.db.First(&contract, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("合同不存在")
		}
		return nil, err
	}

	// 检查是否可以修改
	if contract.Status != "DRAFT" && contract.Status != "PENDING" {
		return nil, errors.New("只能修改草稿或待审批状态的合同")
	}

	// 验证对方单位是否存在
	if req.CounterpartyID != nil {
		var supplier models.Supplier
		if err := s.db.First(&supplier, *req.CounterpartyID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errors.New("对方单位不存在")
			}
			return nil, err
		}
	}

	// 更新字段
	if req.ContractName != nil {
		contract.ContractName = *req.ContractName
	}
	if req.ContractType != nil {
		contract.ContractType = *req.ContractType
	}
	if req.CounterpartyID != nil {
		contract.CounterpartyID = req.CounterpartyID
	}
	if req.CounterpartyName != nil {
		contract.CounterpartyName = *req.CounterpartyName
	}
	if req.TotalAmount != nil {
		contract.TotalAmount = *req.TotalAmount
	}

	// 处理日期更新
	if req.StartDate != nil {
		if parsed, err := time.Parse("2006-01-02", *req.StartDate); err != nil {
			return nil, errors.New("开始日期格式错误")
		} else {
			contract.StartDate = &parsed
		}
	}
	if req.EndDate != nil {
		if parsed, err := time.Parse("2006-01-02", *req.EndDate); err != nil {
			return nil, errors.New("结束日期格式错误")
		} else {
			contract.EndDate = &parsed
		}
	}

	// 验证日期逻辑
	if contract.StartDate != nil && contract.EndDate != nil && contract.StartDate.After(*contract.EndDate) {
		return nil, errors.New("开始日期不能晚于结束日期")
	}

	contract.UpdatedBy = &userID

	if err := s.db.Save(&contract).Error; err != nil {
		logger.Error("Failed to update contract:", err)
		return nil, err
	}

	// 预加载关联数据
	if err := s.db.Preload("Counterparty").Preload("PaymentSchedules").First(&contract, contract.ID).Error; err != nil {
		logger.Error("Failed to preload contract data:", err)
		return nil, err
	}

	response := s.convertContractToResponse(contract)
	return &response, nil
}

// UpdateContractStatus 更新合同状态
func (s *ContractService) UpdateContractStatus(id uuid.UUID, req *dto.UpdateContractStatusRequest, userID uuid.UUID) (*dto.ContractResponse, error) {
	var contract models.Contract
	if err := s.db.First(&contract, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("合同不存在")
		}
		return nil, err
	}

	// 验证状态转换的合法性
	if err := s.validateStatusTransition(contract.Status, req.Status); err != nil {
		return nil, err
	}

	contract.Status = req.Status
	contract.UpdatedBy = &userID

	if err := s.db.Save(&contract).Error; err != nil {
		logger.Error("Failed to update contract status:", err)
		return nil, err
	}

	// 预加载关联数据
	if err := s.db.Preload("Counterparty").Preload("PaymentSchedules").First(&contract, contract.ID).Error; err != nil {
		logger.Error("Failed to preload contract data:", err)
		return nil, err
	}

	response := s.convertContractToResponse(contract)
	return &response, nil
}

// DeleteContract 删除合同
func (s *ContractService) DeleteContract(id uuid.UUID) error {
	var contract models.Contract
	if err := s.db.First(&contract, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("合同不存在")
		}
		return err
	}

	// 检查是否可以删除
	if contract.Status == "ACTIVE" {
		return errors.New("不能删除已生效的合同")
	}

	// 检查是否有关联的付款计划
	var scheduleCount int64
	if err := s.db.Model(&models.ContractPaymentSchedule{}).Where("contract_id = ?", id).Count(&scheduleCount).Error; err != nil {
		return err
	}
	if scheduleCount > 0 {
		return errors.New("存在关联的付款计划，不能删除")
	}

	if err := s.db.Delete(&contract).Error; err != nil {
		logger.Error("Failed to delete contract:", err)
		return err
	}

	return nil
}

// ===== 辅助方法 =====

// generateContractCode 生成合同编号
func (s *ContractService) generateContractCode() (string, error) {
	now := time.Now()
	prefix := fmt.Sprintf("HT%04d%02d%02d", now.Year(), now.Month(), now.Day())
	
	var count int64
	if err := s.db.Model(&models.Contract{}).
		Where("contract_code LIKE ?", prefix+"%").
		Count(&count).Error; err != nil {
		return "", err
	}
	
	return fmt.Sprintf("%s%04d", prefix, count+1), nil
}

// validateStatusTransition 验证状态转换的合法性
func (s *ContractService) validateStatusTransition(currentStatus, newStatus string) error {
	validTransitions := map[string][]string{
		"DRAFT":      {"PENDING", "TERMINATED"},
		"PENDING":    {"ACTIVE", "DRAFT", "TERMINATED"},
		"ACTIVE":     {"COMPLETED", "TERMINATED"},
		"COMPLETED":  {},
		"TERMINATED": {},
	}

	validNext, exists := validTransitions[currentStatus]
	if !exists {
		return errors.New("无效的当前状态")
	}

	for _, status := range validNext {
		if status == newStatus {
			return nil
		}
	}

	return fmt.Errorf("不能从状态 %s 转换到 %s", currentStatus, newStatus)
}

// convertContractToResponse 转换合同为响应格式
func (s *ContractService) convertContractToResponse(contract models.Contract) dto.ContractResponse {
	response := dto.ContractResponse{
		ID:               contract.ID,
		ContractCode:     contract.ContractCode,
		ContractName:     contract.ContractName,
		ContractType:     contract.ContractType,
		CounterpartyID:   contract.CounterpartyID,
		CounterpartyName: contract.CounterpartyName,
		TotalAmount:      contract.TotalAmount,
		Status:           contract.Status,
		CreatedAt:        contract.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:        contract.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	// 处理日期
	if contract.StartDate != nil {
		startDateStr := contract.StartDate.Format("2006-01-02")
		response.StartDate = &startDateStr
	}
	if contract.EndDate != nil {
		endDateStr := contract.EndDate.Format("2006-01-02")
		response.EndDate = &endDateStr
	}

	// 关联数据
	if contract.Counterparty != nil {
		response.Counterparty = &dto.SupplierSimpleResponse{
			ID:         contract.Counterparty.ID,
			Name:       contract.Counterparty.Name,
			CreditCode: contract.Counterparty.CreditCode,
			Status:     contract.Counterparty.Status,
		}
	}

	// 付款计划
	if len(contract.PaymentSchedules) > 0 {
		schedules := make([]dto.PaymentScheduleResponse, len(contract.PaymentSchedules))
		for i, schedule := range contract.PaymentSchedules {
			schedules[i] = dto.PaymentScheduleResponse{
				ID:         schedule.ID,
				ContractID: schedule.ContractID,
				PhaseName:  schedule.PhaseName,
				Amount:     schedule.Amount,
				Status:     schedule.Status,
				PaymentID:  schedule.PaymentID,
				CreatedAt:  schedule.CreatedAt.Format("2006-01-02 15:04:05"),
				UpdatedAt:  schedule.UpdatedAt.Format("2006-01-02 15:04:05"),
			}

			if schedule.DueDate != nil {
				dueDateStr := schedule.DueDate.Format("2006-01-02")
				schedules[i].DueDate = &dueDateStr
			}
		}
		response.PaymentSchedules = schedules
	}

	return response
}

// ===== 付款计划管理 =====

// GetPaymentSchedules 获取付款计划列表
func (s *ContractService) GetPaymentSchedules(req *dto.PaymentScheduleListRequest) ([]dto.PaymentScheduleResponse, int64, error) {
	var schedules []models.ContractPaymentSchedule
	var total int64

	query := s.db.Model(&models.ContractPaymentSchedule{}).
		Preload("Contract").
		Preload("Payment")

	// 搜索条件
	if req.ContractID != nil {
		query = query.Where("contract_id = ?", *req.ContractID)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.DueDateFrom != nil {
		query = query.Where("due_date >= ?", *req.DueDateFrom)
	}
	if req.DueDateTo != nil {
		query = query.Where("due_date <= ?", *req.DueDateTo)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count payment schedules:", err)
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("due_date ASC").Find(&schedules).Error; err != nil {
		logger.Error("Failed to get payment schedules:", err)
		return nil, 0, err
	}

	// 转换响应
	responses := make([]dto.PaymentScheduleResponse, len(schedules))
	for i, schedule := range schedules {
		responses[i] = s.convertPaymentScheduleToResponse(schedule)
	}

	return responses, total, nil
}

// CreatePaymentSchedule 创建付款计划
func (s *ContractService) CreatePaymentSchedule(req *dto.CreatePaymentScheduleRequest) (*dto.PaymentScheduleResponse, error) {
	// 验证合同是否存在
	var contract models.Contract
	if err := s.db.First(&contract, req.ContractID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("合同不存在")
		}
		return nil, err
	}

	// 检查合同状态
	if contract.Status != "DRAFT" && contract.Status != "PENDING" && contract.Status != "ACTIVE" {
		return nil, errors.New("只能为草稿、待审批或生效状态的合同创建付款计划")
	}

	// 验证付款金额不超过合同总额
	var totalScheduled decimal.Decimal
	if err := s.db.Model(&models.ContractPaymentSchedule{}).
		Where("contract_id = ?", req.ContractID).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&totalScheduled).Error; err != nil {
		return nil, err
	}

	if totalScheduled.Add(req.Amount).GreaterThan(contract.TotalAmount) {
		return nil, errors.New("付款计划总额不能超过合同总额")
	}

	// 解析到期日期
	var dueDate *time.Time
	if req.DueDate != nil {
		if parsed, err := time.Parse("2006-01-02", *req.DueDate); err != nil {
			return nil, errors.New("到期日期格式错误")
		} else {
			dueDate = &parsed
		}
	}

	schedule := models.ContractPaymentSchedule{
		ID:         uuid.New(),
		ContractID: req.ContractID,
		PhaseName:  req.PhaseName,
		DueDate:    dueDate,
		Amount:     req.Amount,
		Status:     "PENDING",
	}

	if err := s.db.Create(&schedule).Error; err != nil {
		logger.Error("Failed to create payment schedule:", err)
		return nil, err
	}

	// 预加载关联数据
	if err := s.db.Preload("Contract").Preload("Payment").First(&schedule, schedule.ID).Error; err != nil {
		logger.Error("Failed to preload payment schedule data:", err)
		return nil, err
	}

	response := s.convertPaymentScheduleToResponse(schedule)
	return &response, nil
}

// UpdatePaymentSchedule 更新付款计划
func (s *ContractService) UpdatePaymentSchedule(id uuid.UUID, req *dto.UpdatePaymentScheduleRequest) (*dto.PaymentScheduleResponse, error) {
	var schedule models.ContractPaymentSchedule
	if err := s.db.First(&schedule, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("付款计划不存在")
		}
		return nil, err
	}

	// 检查是否可以修改
	if schedule.Status == "PAID" {
		return nil, errors.New("已支付的付款计划不能修改")
	}

	// 更新字段
	if req.PhaseName != nil {
		schedule.PhaseName = req.PhaseName
	}
	if req.DueDate != nil {
		if parsed, err := time.Parse("2006-01-02", *req.DueDate); err != nil {
			return nil, errors.New("到期日期格式错误")
		} else {
			schedule.DueDate = &parsed
		}
	}
	if req.Amount != nil {
		// 验证付款金额不超过合同总额
		var contract models.Contract
		if err := s.db.First(&contract, schedule.ContractID).Error; err != nil {
			return nil, err
		}

		var totalScheduled decimal.Decimal
		if err := s.db.Model(&models.ContractPaymentSchedule{}).
			Where("contract_id = ? AND id != ?", schedule.ContractID, id).
			Select("COALESCE(SUM(amount), 0)").
			Scan(&totalScheduled).Error; err != nil {
			return nil, err
		}

		if totalScheduled.Add(*req.Amount).GreaterThan(contract.TotalAmount) {
			return nil, errors.New("付款计划总额不能超过合同总额")
		}

		schedule.Amount = *req.Amount
	}

	if err := s.db.Save(&schedule).Error; err != nil {
		logger.Error("Failed to update payment schedule:", err)
		return nil, err
	}

	// 预加载关联数据
	if err := s.db.Preload("Contract").Preload("Payment").First(&schedule, schedule.ID).Error; err != nil {
		logger.Error("Failed to preload payment schedule data:", err)
		return nil, err
	}

	response := s.convertPaymentScheduleToResponse(schedule)
	return &response, nil
}

// BatchCreatePaymentSchedules 批量创建付款计划
func (s *ContractService) BatchCreatePaymentSchedules(req *dto.BatchCreatePaymentScheduleRequest) ([]dto.PaymentScheduleResponse, error) {
	// 验证合同是否存在
	var contract models.Contract
	if err := s.db.First(&contract, req.ContractID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("合同不存在")
		}
		return nil, err
	}

	// 检查合同状态
	if contract.Status != "DRAFT" && contract.Status != "PENDING" && contract.Status != "ACTIVE" {
		return nil, errors.New("只能为草稿、待审批或生效状态的合同创建付款计划")
	}

	// 计算总金额
	totalAmount := decimal.Zero
	for _, item := range req.Schedules {
		totalAmount = totalAmount.Add(item.Amount)
	}

	// 验证付款金额不超过合同总额
	var existingTotal decimal.Decimal
	if err := s.db.Model(&models.ContractPaymentSchedule{}).
		Where("contract_id = ?", req.ContractID).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&existingTotal).Error; err != nil {
		return nil, err
	}

	if existingTotal.Add(totalAmount).GreaterThan(contract.TotalAmount) {
		return nil, errors.New("付款计划总额不能超过合同总额")
	}

	// 开启事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	var schedules []models.ContractPaymentSchedule
	for _, item := range req.Schedules {
		// 解析到期日期
		var dueDate *time.Time
		if item.DueDate != nil {
			if parsed, err := time.Parse("2006-01-02", *item.DueDate); err != nil {
				tx.Rollback()
				return nil, errors.New("到期日期格式错误")
			} else {
				dueDate = &parsed
			}
		}

		schedule := models.ContractPaymentSchedule{
			ID:         uuid.New(),
			ContractID: req.ContractID,
			PhaseName:  item.PhaseName,
			DueDate:    dueDate,
			Amount:     item.Amount,
			Status:     "PENDING",
		}

		if err := tx.Create(&schedule).Error; err != nil {
			tx.Rollback()
			logger.Error("Failed to create payment schedule:", err)
			return nil, err
		}

		schedules = append(schedules, schedule)
	}

	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// 转换响应
	responses := make([]dto.PaymentScheduleResponse, len(schedules))
	for i, schedule := range schedules {
		responses[i] = s.convertPaymentScheduleToResponse(schedule)
	}

	return responses, nil
}

// convertPaymentScheduleToResponse 转换付款计划为响应格式
func (s *ContractService) convertPaymentScheduleToResponse(schedule models.ContractPaymentSchedule) dto.PaymentScheduleResponse {
	response := dto.PaymentScheduleResponse{
		ID:         schedule.ID,
		ContractID: schedule.ContractID,
		PhaseName:  schedule.PhaseName,
		Amount:     schedule.Amount,
		Status:     schedule.Status,
		PaymentID:  schedule.PaymentID,
		CreatedAt:  schedule.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:  schedule.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	if schedule.DueDate != nil {
		dueDateStr := schedule.DueDate.Format("2006-01-02")
		response.DueDate = &dueDateStr
	}

	// 关联数据
	if schedule.Contract != nil {
		response.Contract = &dto.ContractSimpleResponse{
			ID:           schedule.Contract.ID,
			ContractCode: schedule.Contract.ContractCode,
			ContractName: schedule.Contract.ContractName,
			ContractType: schedule.Contract.ContractType,
			TotalAmount:  schedule.Contract.TotalAmount,
			Status:       schedule.Contract.Status,
		}
	}

	if schedule.Payment != nil {
		response.Payment = &dto.PaymentSimpleResponse{
			ID:          schedule.Payment.ID,
			PaymentCode: schedule.Payment.PaymentCode,
			Amount:      schedule.Payment.Amount,
			Status:      schedule.Payment.Status,
		}

		if schedule.Payment.PaidAt != nil {
			paidAtStr := schedule.Payment.PaidAt.Format("2006-01-02 15:04:05")
			response.Payment.PaidAt = &paidAtStr
		}
	}

	return response
}
