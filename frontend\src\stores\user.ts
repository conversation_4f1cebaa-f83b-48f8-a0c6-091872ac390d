import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login, logout, getUserProfile, type LoginParams, type UserProfile } from '@/api/auth'
import { message } from 'ant-design-vue'

export const useUserStore = defineStore('user', () => {
  const token = ref<string>(localStorage.getItem('token') || '')
  const userInfo = ref<UserProfile | null>(
    localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')!) : null
  )

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const userName = computed(() => userInfo.value?.user_name || '')
  const userRoles = computed(() => userInfo.value?.roles?.map(role => role.role_name) || [])
  const userPermissions = computed(() => userInfo.value?.permissions || [])

  // 登录
  const loginAction = async (params: LoginParams) => {
    try {
      const response = await login(params)

      // 保存token和用户信息
      token.value = response.access_token
      userInfo.value = {
        ...response.user,
        permissions: [] // 初始化权限数组，后续可以从角色中提取
      } as UserProfile

      localStorage.setItem('token', response.access_token)
      localStorage.setItem('userInfo', JSON.stringify(userInfo.value))

      if (params.remember) {
        localStorage.setItem('remember', 'true')
        localStorage.setItem('username', params.username)
      } else {
        localStorage.removeItem('remember')
        localStorage.removeItem('username')
      }

      message.success('登录成功')
      return response
    } catch (error: any) {
      // 提取错误消息
      const errorMsg = error?.response?.data?.message || error?.message || '登录失败'
      message.error(errorMsg)
      throw error
    }
  }

  // 登出
  const logoutAction = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地存储
      token.value = ''
      userInfo.value = null
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      message.success('已退出登录')
    }
  }

  // 获取用户信息
  const fetchUserProfile = async () => {
    try {
      const profile = await getUserProfile()
      userInfo.value = profile
      localStorage.setItem('userInfo', JSON.stringify(profile))
      return profile
    } catch (error) {
      throw error
    }
  }

  // 检查权限
  const hasPermission = (permission: string) => {
    return userPermissions.value.includes(permission)
  }

  // 检查角色
  const hasRole = (role: string) => {
    return userRoles.value.includes(role)
  }

  return {
    token,
    userInfo,
    isLoggedIn,
    userName,
    userRoles,
    userPermissions,
    loginAction,
    logoutAction,
    fetchUserProfile,
    hasPermission,
    hasRole
  }
})
