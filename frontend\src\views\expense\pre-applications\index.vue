<template>
  <div class="pre-applications-page">
    <a-card :bordered="false">
      <template #title>
        <span>事前申请管理</span>
      </template>

      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <PlusOutlined />
            </template>
            新建申请
          </a-button>
          <a-button @click="handleRefresh">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 搜索区域 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="申请类型">
            <a-select v-model:value="searchForm.type" placeholder="请选择申请类型" allow-clear style="width: 150px">
              <a-select-option value="TRAVEL">差旅申请</a-select-option>
              <a-select-option value="MEETING">会议申请</a-select-option>
              <a-select-option value="TRAINING">培训申请</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="申请状态">
            <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear style="width: 120px">
              <a-select-option value="DRAFT">草稿</a-select-option>
              <a-select-option value="PENDING">待审批</a-select-option>
              <a-select-option value="APPROVED">已批准</a-select-option>
              <a-select-option value="REJECTED">已驳回</a-select-option>
              <a-select-option value="CLOSED">已关闭</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="申请时间">
            <a-range-picker v-model:value="dateRange" style="width: 240px" @change="handleDateChange" />
          </a-form-item>

          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon>
                  <SearchOutlined />
                </template>
                搜索
              </a-button>
              <a-button @click="handleReset">
                <template #icon>
                  <ReloadOutlined />
                </template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 申请列表 -->
      <a-table :columns="columns" :data-source="applicationList" :loading="loading" :pagination="pagination"
        row-key="id" size="middle" @change="handleTableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeText(record.type) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'estimated_amount'">
            <span class="amount-text">{{ formatAmount(record.estimated_amount) }}</span>
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleView(record)">
                查看
              </a-button>
              <a-button type="link" size="small" @click="handleEdit(record)" v-if="record.status === 'DRAFT'">
                编辑
              </a-button>
              <a-button type="link" size="small" @click="handleSubmit(record)" v-if="record.status === 'DRAFT'">
                提交
              </a-button>
              <a-popconfirm title="确定要删除这个申请吗？" @confirm="handleDelete(record)" v-if="record.status === 'DRAFT'">
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑申请弹窗 -->
    <a-modal v-model:open="modalVisible" :title="modalTitle" :confirm-loading="modalLoading" width="800px"
      @ok="handleModalSubmit" @cancel="handleCancel">
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="申请类型" name="type">
          <a-select v-model:value="formData.type" placeholder="请选择申请类型">
            <a-select-option value="TRAVEL">差旅申请</a-select-option>
            <a-select-option value="MEETING">会议申请</a-select-option>
            <a-select-option value="TRAINING">培训申请</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="申请标题" name="title">
          <a-input v-model:value="formData.title" placeholder="请输入申请标题" />
        </a-form-item>

        <a-form-item label="所属部门" name="department_id">
          <a-tree-select v-model:value="formData.department_id" :tree-data="departmentTreeOptions" placeholder="请选择部门"
            tree-default-expand-all />
        </a-form-item>

        <a-form-item label="预计金额" name="estimated_amount">
          <a-input-number v-model:value="formData.estimated_amount" :min="0" :precision="2" placeholder="请输入预计金额"
            style="width: 100%" :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/¥\s?|(,*)/g, '')" />
        </a-form-item>

        <a-form-item label="开始时间" name="start_date">
          <a-date-picker v-model:value="formData.start_date" placeholder="请选择开始时间" style="width: 100%" />
        </a-form-item>

        <a-form-item label="结束时间" name="end_date">
          <a-date-picker v-model:value="formData.end_date" placeholder="请选择结束时间" style="width: 100%" />
        </a-form-item>

        <a-form-item label="目的地" name="destination" v-if="formData.type === 'TRAVEL'">
          <a-input v-model:value="formData.destination" placeholder="请输入目的地" />
        </a-form-item>

        <a-form-item label="参与人员" name="participants">
          <a-textarea v-model:value="formData.participants" placeholder="请输入参与人员" :rows="2" />
        </a-form-item>

        <a-form-item label="申请事由" name="purpose">
          <a-textarea v-model:value="formData.purpose" placeholder="请输入申请事由" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 查看详情弹窗 -->
    <a-modal v-model:open="viewModalVisible" title="申请详情" :footer="null" width="800px">
      <a-descriptions :column="2" bordered v-if="currentRecord">
        <a-descriptions-item label="申请类型">
          <a-tag :color="getTypeColor(currentRecord.type)">
            {{ getTypeText(currentRecord.type) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="申请状态">
          <a-tag :color="getStatusColor(currentRecord.status)">
            {{ getStatusText(currentRecord.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="申请标题" :span="2">
          {{ currentRecord.title }}
        </a-descriptions-item>
        <a-descriptions-item label="申请人">
          {{ currentRecord.applicant_name }}
        </a-descriptions-item>
        <a-descriptions-item label="所属部门">
          {{ currentRecord.department_name }}
        </a-descriptions-item>
        <a-descriptions-item label="预计金额">
          <span class="amount-text">{{ formatAmount(currentRecord.estimated_amount) }}</span>
        </a-descriptions-item>
        <a-descriptions-item label="申请时间">
          {{ currentRecord.created_at }}
        </a-descriptions-item>
        <a-descriptions-item label="开始时间">
          {{ currentRecord.start_date }}
        </a-descriptions-item>
        <a-descriptions-item label="结束时间">
          {{ currentRecord.end_date }}
        </a-descriptions-item>
        <a-descriptions-item label="目的地" v-if="currentRecord.destination">
          {{ currentRecord.destination }}
        </a-descriptions-item>
        <a-descriptions-item label="参与人员" v-if="currentRecord.participants" :span="2">
          {{ currentRecord.participants }}
        </a-descriptions-item>
        <a-descriptions-item label="申请事由" :span="2">
          {{ currentRecord.purpose }}
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'
import dayjs, { type Dayjs } from 'dayjs'
import {
  getPreApplications,
  createPreApplication,
  updatePreApplication,
  submitPreApplication,
  type PreApplication,
  type PreApplicationForm,
  type PreApplicationQuery
} from '@/api/expense'
import { getDepartmentTree, type Department } from '@/api/system'
import type { Rule } from 'ant-design-vue/es/form'
import type { FormInstance } from 'ant-design-vue'

const loading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const viewModalVisible = ref(false)
const formRef = ref<FormInstance>()
const editingId = ref<number | null>(null)

const applicationList = ref<PreApplication[]>([])
const departmentList = ref<Department[]>([])
const currentRecord = ref<PreApplication | null>(null)
const dateRange = ref<[Dayjs, Dayjs] | null>(null)

const searchForm = reactive<PreApplicationQuery>({
  type: undefined,
  status: undefined,
  start_date: undefined,
  end_date: undefined,
  page: 1,
  page_size: 10
})

const formData = reactive<PreApplicationForm & {
  start_date: Dayjs | null
  end_date: Dayjs | null
}>({
  type: 'travel',
  title: '',
  department_id: 0,
  estimated_amount: 0,
  start_date: null,
  end_date: null,
  destination: '',
  purpose: '',
  participants: ''
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '申请类型',
    key: 'type',
    width: 100
  },
  {
    title: '申请标题',
    dataIndex: 'title',
    key: 'title',
    width: 200,
    ellipsis: true
  },
  {
    title: '申请人',
    dataIndex: 'applicant_name',
    key: 'applicant_name',
    width: 100
  },
  {
    title: '所属部门',
    dataIndex: 'department_name',
    key: 'department_name',
    width: 150
  },
  {
    title: '预计金额',
    key: 'estimated_amount',
    width: 120,
    align: 'right'
  },
  {
    title: '开始时间',
    dataIndex: 'start_date',
    key: 'start_date',
    width: 120
  },
  {
    title: '结束时间',
    dataIndex: 'end_date',
    key: 'end_date',
    width: 120
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '申请时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 表单验证规则
const rules: Record<string, Rule[]> = {
  type: [
    { required: true, message: '请选择申请类型', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入申请标题', trigger: 'blur' }
  ],
  department_id: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ],
  estimated_amount: [
    { required: true, message: '请输入预计金额', trigger: 'blur' }
  ],
  start_date: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  end_date: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ],
  purpose: [
    { required: true, message: '请输入申请事由', trigger: 'blur' }
  ]
}

// 弹窗标题
const modalTitle = computed(() => {
  return editingId.value ? '编辑事前申请' : '新建事前申请'
})

// 部门树形选择器选项
const departmentTreeOptions = computed(() => {
  const convertToTreeData = (departments: Department[]): any[] => {
    return departments.map(dept => ({
      title: dept.name,
      value: dept.id,
      key: dept.id,
      children: dept.children ? convertToTreeData(dept.children) : undefined
    }))
  }
  return convertToTreeData(departmentList.value)
})

// 获取申请类型颜色
const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'TRAVEL': 'blue',
    'MEETING': 'green',
    'TRAINING': 'orange'
  }
  return colorMap[type] || 'default'
}

// 获取申请类型文本
const getTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    'TRAVEL': '差旅申请',
    'MEETING': '会议申请',
    'TRAINING': '培训申请'
  }
  return textMap[type] || type
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'DRAFT': 'default',
    'PENDING': 'blue',
    'APPROVED': 'green',
    'REJECTED': 'red',
    'CLOSED': 'orange'
  }
  return colorMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'DRAFT': '草稿',
    'PENDING': '待审批',
    'APPROVED': '已批准',
    'REJECTED': '已驳回',
    'CLOSED': '已关闭'
  }
  return textMap[status] || status
}

// 格式化金额
const formatAmount = (amount: number) => {
  return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`
}

// 加载申请数据
const loadApplications = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.current,
      page_size: pagination.pageSize
    }
    const response = await getPreApplications(params)
    applicationList.value = response.list
    pagination.total = response.total
  } catch (error) {
    message.error('加载申请数据失败')
  } finally {
    loading.value = false
  }
}

// 加载部门数据
const loadDepartments = async () => {
  try {
    departmentList.value = await getDepartmentTree()
  } catch (error) {
    message.error('加载部门数据失败')
  }
}

// 日期范围变化处理
const handleDateChange = (dates: [Dayjs, Dayjs] | null) => {
  if (dates) {
    searchForm.start_date = dates[0].format('YYYY-MM-DD')
    searchForm.end_date = dates[1].format('YYYY-MM-DD')
  } else {
    searchForm.start_date = undefined
    searchForm.end_date = undefined
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadApplications()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    type: undefined,
    status: undefined,
    start_date: undefined,
    end_date: undefined
  })
  dateRange.value = null
  pagination.current = 1
  loadApplications()
}

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadApplications()
}

// 新建申请
const handleAdd = () => {
  editingId.value = null
  resetForm()
  modalVisible.value = true
}

// 编辑申请
const handleEdit = (record: PreApplication) => {
  editingId.value = record.id
  Object.assign(formData, {
    type: record.type,
    title: record.title,
    department_id: record.department_id,
    estimated_amount: record.estimated_amount,
    start_date: dayjs(record.start_date),
    end_date: dayjs(record.end_date),
    destination: record.destination,
    purpose: record.purpose,
    participants: record.participants
  })
  modalVisible.value = true
}

// 查看详情
const handleView = (record: PreApplication) => {
  currentRecord.value = record
  viewModalVisible.value = true
}

// 提交申请
const handleSubmit = (record: PreApplication) => {
  Modal.confirm({
    title: '确认提交',
    content: '提交后将无法修改，确定要提交这个申请吗？',
    onOk: async () => {
      try {
        await submitPreApplication(record.id)
        message.success('提交成功')
        loadApplications()
      } catch (error) {
        message.error('提交失败')
      }
    }
  })
}

// 删除申请
const handleDelete = async (record: PreApplication) => {
  try {
    // TODO: 实现删除API
    message.success('删除成功')
    loadApplications()
  } catch (error) {
    message.error('删除失败')
  }
}

// 刷新数据
const handleRefresh = () => {
  loadApplications()
}

// 提交表单
const handleModalSubmit = async () => {
  try {
    await formRef.value?.validate()
    modalLoading.value = true

    const submitData = {
      type: formData.type,
      title: formData.title,
      department_id: formData.department_id,
      estimated_amount: formData.estimated_amount,
      start_date: formData.start_date?.format('YYYY-MM-DD') || '',
      end_date: formData.end_date?.format('YYYY-MM-DD') || '',
      destination: formData.destination,
      purpose: formData.purpose,
      participants: formData.participants
    }

    if (editingId.value) {
      await updatePreApplication(editingId.value, submitData)
      message.success('更新成功')
    } else {
      await createPreApplication(submitData)
      message.success('创建成功')
    }

    modalVisible.value = false
    loadApplications()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    modalLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    type: 'travel',
    title: '',
    department_id: 0,
    estimated_amount: 0,
    start_date: null,
    end_date: null,
    destination: '',
    purpose: '',
    participants: ''
  })
  formRef.value?.resetFields()
}

onMounted(() => {
  loadDepartments()
  loadApplications()
})
</script>

<style scoped>
.pre-applications-page {
  padding: 24px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.amount-text {
  font-weight: 500;
  color: #1890ff;
}
</style>
