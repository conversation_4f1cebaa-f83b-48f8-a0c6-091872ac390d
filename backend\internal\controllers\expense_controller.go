package controllers

import (
	"hospital-management/internal/dto"
	"hospital-management/internal/services"
	"hospital-management/pkg/logger"
	customValidator "hospital-management/pkg/validator"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
)

type ExpenseController struct {
	expenseService *services.ExpenseService
	paymentService *services.PaymentService
	validator      *validator.Validate
}

func NewExpenseController(expenseService *services.ExpenseService, paymentService *services.PaymentService) *ExpenseController {
	return &ExpenseController{
		expenseService: expenseService,
		paymentService: paymentService,
		validator:      customValidator.NewValidator(),
	}
}

// ===== 事前申请管理 =====

// GetPreApplications 获取事前申请列表
// @Summary 获取事前申请列表
// @Description 获取事前申请列表，支持分页、搜索和筛选
// @Tags 支出控制管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param keyword query string false "搜索关键词"
// @Param type query string false "申请类型" Enums(TRAVEL, MEETING, TRAINING)
// @Param status query string false "状态" Enums(DRAFT, PENDING, APPROVED, REJECTED, CLOSED)
// @Param applicant_id query string false "申请人ID"
// @Param department_id query string false "部门ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /pre-applications [get]
func (c *ExpenseController) GetPreApplications(ctx *gin.Context) {
	var req dto.PreApplicationListRequest

	// 设置默认值
	req.Page = 1
	req.PageSize = 10

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	applications, total, err := c.expenseService.GetPreApplications(&req)
	if err != nil {
		logger.Error("Failed to get pre applications:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取事前申请列表失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      applications,
			"total":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	})
}

// GetPreApplication 获取事前申请详情
// @Summary 获取事前申请详情
// @Description 获取指定事前申请的详细信息
// @Tags 支出控制管理
// @Accept json
// @Produce json
// @Param id path string true "事前申请ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "事前申请不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /pre-applications/{id} [get]
func (c *ExpenseController) GetPreApplication(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的事前申请ID",
		})
		return
	}

	// 调用服务
	application, err := c.expenseService.GetPreApplication(id)
	if err != nil {
		logger.Error("Failed to get pre application:", err)
		if err.Error() == "事前申请不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "获取事前申请详情失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    application,
	})
}

// CreatePreApplication 创建事前申请
// @Summary 创建事前申请
// @Description 创建新的事前申请
// @Tags 支出控制管理
// @Accept json
// @Produce json
// @Param request body dto.CreatePreApplicationRequest true "创建事前申请请求"
// @Success 201 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /pre-applications [post]
func (c *ExpenseController) CreatePreApplication(ctx *gin.Context) {
	var req dto.CreatePreApplicationRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	application, err := c.expenseService.CreatePreApplication(&req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to create pre application:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建事前申请失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "创建成功",
		"data":    application,
	})
}

// UpdatePreApplication 更新事前申请
// @Summary 更新事前申请
// @Description 更新事前申请信息
// @Tags 支出控制管理
// @Accept json
// @Produce json
// @Param id path string true "事前申请ID"
// @Param request body dto.UpdatePreApplicationRequest true "更新事前申请请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "事前申请不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /pre-applications/{id} [put]
func (c *ExpenseController) UpdatePreApplication(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的事前申请ID",
		})
		return
	}

	var req dto.UpdatePreApplicationRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	application, err := c.expenseService.UpdatePreApplication(id, &req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to update pre application:", err)
		if err.Error() == "事前申请不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "更新事前申请失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    application,
	})
}

// SubmitPreApplication 提交事前申请
// @Summary 提交事前申请
// @Description 提交事前申请进入审批流程
// @Tags 支出控制管理
// @Accept json
// @Produce json
// @Param id path string true "事前申请ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "事前申请不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /pre-applications/{id}/submit [post]
func (c *ExpenseController) SubmitPreApplication(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的事前申请ID",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	if err := c.expenseService.SubmitPreApplication(id, userID.(uuid.UUID)); err != nil {
		logger.Error("Failed to submit pre application:", err)
		if err.Error() == "事前申请不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "提交事前申请失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "提交成功",
	})
}

// ===== 费用报销管理 =====

// GetExpenseApplications 获取报销申请列表
// @Summary 获取报销申请列表
// @Description 获取报销申请列表，支持分页、搜索和筛选
// @Tags 支出控制管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param keyword query string false "搜索关键词"
// @Param status query string false "状态" Enums(DRAFT, PENDING, APPROVED, REJECTED, PAID)
// @Param applicant_id query string false "申请人ID"
// @Param department_id query string false "部门ID"
// @Param pre_application_id query string false "事前申请ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /expense/applications [get]
func (c *ExpenseController) GetExpenseApplications(ctx *gin.Context) {
	var req dto.ExpenseApplicationListRequest

	// 设置默认值
	req.Page = 1
	req.PageSize = 10

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	applications, total, err := c.expenseService.GetExpenseApplications(&req)
	if err != nil {
		logger.Error("Failed to get expense applications:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取报销申请列表失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      applications,
			"total":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	})
}

// GetExpenseApplication 获取报销申请详情
// @Summary 获取报销申请详情
// @Description 获取指定报销申请的详细信息
// @Tags 支出控制管理
// @Accept json
// @Produce json
// @Param id path string true "报销申请ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "报销申请不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /expense/applications/{id} [get]
func (c *ExpenseController) GetExpenseApplication(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的报销申请ID",
		})
		return
	}

	// 调用服务
	application, err := c.expenseService.GetExpenseApplication(id)
	if err != nil {
		logger.Error("Failed to get expense application:", err)
		if err.Error() == "报销申请不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "获取报销申请详情失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    application,
	})
}

// CreateExpenseApplication 创建报销申请
// @Summary 创建报销申请
// @Description 创建新的报销申请
// @Tags 支出控制管理
// @Accept json
// @Produce json
// @Param request body dto.CreateExpenseApplicationRequest true "创建报销申请请求"
// @Success 201 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /expense/applications [post]
func (c *ExpenseController) CreateExpenseApplication(ctx *gin.Context) {
	var req dto.CreateExpenseApplicationRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	application, err := c.expenseService.CreateExpenseApplication(&req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to create expense application:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建报销申请失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "创建成功",
		"data":    application,
	})
}

// UpdateExpenseApplication 更新报销申请
// @Summary 更新报销申请
// @Description 更新报销申请信息
// @Tags 支出控制管理
// @Accept json
// @Produce json
// @Param id path string true "报销申请ID"
// @Param request body dto.UpdateExpenseApplicationRequest true "更新报销申请请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "报销申请不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /expense/applications/{id} [put]
func (c *ExpenseController) UpdateExpenseApplication(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的报销申请ID",
		})
		return
	}

	var req dto.UpdateExpenseApplicationRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	application, err := c.expenseService.UpdateExpenseApplication(id, &req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to update expense application:", err)
		if err.Error() == "报销申请不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "更新报销申请失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    application,
	})
}

// SubmitExpenseApplication 提交报销申请
// @Summary 提交报销申请
// @Description 提交报销申请进入审批流程
// @Tags 支出控制管理
// @Accept json
// @Produce json
// @Param id path string true "报销申请ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "报销申请不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /expense/applications/{id}/submit [post]
func (c *ExpenseController) SubmitExpenseApplication(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的报销申请ID",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	if err := c.expenseService.SubmitExpenseApplication(id, userID.(uuid.UUID)); err != nil {
		logger.Error("Failed to submit expense application:", err)
		if err.Error() == "报销申请不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "提交报销申请失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "提交成功",
	})
}

// GetMyExpenseApplications 获取我的报销记录
// @Summary 获取我的报销记录
// @Description 获取当前用户的报销申请记录
// @Tags 支出控制管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param keyword query string false "搜索关键词"
// @Param status query string false "状态" Enums(DRAFT, PENDING, APPROVED, REJECTED, PAID)
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /expense/applications/my [get]
func (c *ExpenseController) GetMyExpenseApplications(ctx *gin.Context) {
	var req dto.ExpenseApplicationListRequest

	// 设置默认值
	req.Page = 1
	req.PageSize = 10

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	applications, total, err := c.expenseService.GetMyExpenseApplications(userID.(uuid.UUID), &req)
	if err != nil {
		logger.Error("Failed to get my expense applications:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取我的报销记录失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      applications,
			"total":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	})
}

// ===== 付款管理 =====

// GetPayments 获取付款单列表
// @Summary 获取付款单列表
// @Description 获取付款单列表，支持分页、搜索和筛选
// @Tags 支出控制管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param keyword query string false "搜索关键词"
// @Param status query string false "状态" Enums(PENDING, PAID, FAILED)
// @Param source_type query string false "来源类型" Enums(EXPENSE, CONTRACT_PAY)
// @Param source_id query string false "来源单据ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /payments [get]
func (c *ExpenseController) GetPayments(ctx *gin.Context) {
	var req dto.PaymentListRequest

	// 设置默认值
	req.Page = 1
	req.PageSize = 10

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	payments, total, err := c.paymentService.GetPayments(&req)
	if err != nil {
		logger.Error("Failed to get payments:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取付款单列表失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      payments,
			"total":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	})
}

// CreatePayment 创建付款单
// @Summary 创建付款单
// @Description 创建新的付款单
// @Tags 支出控制管理
// @Accept json
// @Produce json
// @Param request body dto.CreatePaymentRequest true "创建付款单请求"
// @Success 201 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /payments [post]
func (c *ExpenseController) CreatePayment(ctx *gin.Context) {
	var req dto.CreatePaymentRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	payment, err := c.paymentService.CreatePayment(&req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to create payment:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建付款单失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "创建成功",
		"data":    payment,
	})
}

// UpdatePaymentStatus 更新付款状态
// @Summary 更新付款状态
// @Description 更新付款单的状态
// @Tags 支出控制管理
// @Accept json
// @Produce json
// @Param id path string true "付款单ID"
// @Param request body dto.UpdatePaymentStatusRequest true "更新付款状态请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "付款单不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /payments/{id}/status [put]
func (c *ExpenseController) UpdatePaymentStatus(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的付款单ID",
		})
		return
	}

	var req dto.UpdatePaymentStatusRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	payment, err := c.paymentService.UpdatePaymentStatus(id, &req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to update payment status:", err)
		if err.Error() == "付款单不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "更新付款状态失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    payment,
	})
}

// BatchPayment 批量付款
// @Summary 批量付款
// @Description 批量处理付款单
// @Tags 支出控制管理
// @Accept json
// @Produce json
// @Param request body dto.BatchPaymentRequest true "批量付款请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /payments/batch [post]
func (c *ExpenseController) BatchPayment(ctx *gin.Context) {
	var req dto.BatchPaymentRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	if err := c.paymentService.BatchPayment(&req, userID.(uuid.UUID)); err != nil {
		logger.Error("Failed to batch payment:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "批量付款失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "批量付款成功",
	})
}
