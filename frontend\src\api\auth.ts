import request from './request'

export interface LoginParams {
  username: string
  password: string
  remember?: boolean
}

export interface LoginResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  user: {
    id: string
    user_name: string
    employee_id: string
    email: string
    phone_number: string
    department_id: string
    department_name: string
    job_title: string
    is_active: boolean
    roles: Array<{
      id: string
      role_name: string
    }>
  }
}

export interface UserProfile {
  id: string
  user_name: string
  employee_id: string
  email: string
  phone_number: string
  department_id: string
  department_name: string
  job_title: string
  is_active: boolean
  roles: Array<{
    id: string
    role_name: string
  }>
  permissions: string[]
}

// 用户登录
export const login = (params: LoginParams): Promise<LoginResponse> => {
  return request.post('/api/v1/auth/login', params)
}

// 用户登出
export const logout = (): Promise<void> => {
  return request.post('/api/v1/auth/logout')
}

// 刷新Token
export const refreshToken = (): Promise<{ token: string }> => {
  return request.post('/api/v1/auth/refresh')
}

// 获取当前用户信息
export const getUserProfile = (): Promise<UserProfile> => {
  return request.get('/api/v1/auth/profile')
}
