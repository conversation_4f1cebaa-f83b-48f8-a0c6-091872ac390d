# nginx配置问题排查

## 问题分析
从错误日志看，nginx在 `/usr/share/nginx/html/` 目录下查找文件，说明您的自定义配置没有生效，nginx仍在使用默认配置。

## 排查步骤

### 1. 检查nginx配置文件位置
```bash
# 查看nginx主配置文件
sudo nginx -T | grep "configuration file"

# 或者查看nginx进程使用的配置文件
sudo nginx -t
```

### 2. 检查主配置文件
```bash
# 查看主配置文件内容
sudo cat /etc/nginx/nginx.conf

# 查看是否包含您的server配置
sudo grep -n "server_name localhost" /etc/nginx/nginx.conf
```

### 3. 检查配置文件包含关系
```bash
# 查看是否有include指令
sudo grep -n "include" /etc/nginx/nginx.conf

# 常见的include路径
ls -la /etc/nginx/sites-enabled/
ls -la /etc/nginx/conf.d/
```

## 解决方案

### 方案一：直接修改主配置文件
```bash
# 备份原配置
sudo cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup

# 编辑主配置文件
sudo nano /etc/nginx/nginx.conf
```

在 `http` 块中添加您的server配置：
```nginx
http {
    # ... 其他配置 ...
    
    server {
        listen 80;
        server_name localhost;

        # 前端静态文件
        location /hospital {
            alias /var/www/hospital/dist;
            index index.html;
            try_files $uri $uri/ /hospital/index.html;
        }
        
        # 根路径重定向
        location = / {
            return 301 /hospital/;
        }
        
        # API 代理到后端
        location /api/ {
            proxy_pass http://127.0.0.1:8080;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        client_max_body_size 100M;
    }
}
```

### 方案二：使用独立配置文件
```bash
# 创建配置文件
sudo nano /etc/nginx/conf.d/hospital.conf
```

将server配置写入该文件，然后重启nginx。

### 方案三：禁用默认站点（如果存在）
```bash
# 查看是否有默认站点
ls -la /etc/nginx/sites-enabled/

# 如果有default，可以禁用它
sudo rm /etc/nginx/sites-enabled/default
# 或者
sudo unlink /etc/nginx/sites-enabled/default
```

## 验证和重启

```bash
# 测试配置
sudo nginx -t

# 如果测试通过，重启nginx
sudo systemctl restart nginx

# 查看nginx状态
sudo systemctl status nginx

# 再次查看错误日志
sudo tail -f /var/log/nginx/error.log
```

## 检查文件权限
```bash
# 确保文件存在
ls -la /var/www/hospital/dist/index.html

# 设置正确权限
sudo chown -R nginx:nginx /var/www/hospital/
sudo chmod -R 755 /var/www/hospital/

# 如果是Ubuntu/Debian，可能需要使用www-data
sudo chown -R www-data:www-data /var/www/hospital/
```
