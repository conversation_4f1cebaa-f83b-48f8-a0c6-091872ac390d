<template>
  <div class="departments-page">
    <a-card :bordered="false">
      <template #title>
        <span>部门管理</span>
      </template>

      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <PlusOutlined />
            </template>
            新增部门
          </a-button>
          <a-button @click="handleRefresh">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="departmentList"
        :loading="loading"
        :pagination="false"
        row-key="id"
        :default-expand-all-rows="false"
        :expandable="{
          childrenColumnName: 'children',
          indentSize: 20,
          expandRowByClick: false
        }"
        size="middle"
        :scroll="{ x: 800 }"
        bordered>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'dept_type'">
            <a-tag :color="getDeptTypeColor(record.dept_type)">
              {{ getDeptTypeText(record.dept_type) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'is_active'">
            <a-tag :color="record.is_active ? 'green' : 'red'">
              {{ record.is_active ? '启用' : '禁用' }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)">
                编辑
              </a-button>
              <a-button type="link" size="small" @click="handleAddChild(record)">
                添加子部门
              </a-button>
              <a-popconfirm title="确定要删除这个部门吗？" @confirm="handleDelete(record)">
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑部门弹窗 -->
    <a-modal v-model:open="modalVisible" :title="modalTitle" :confirm-loading="modalLoading" @ok="handleSubmit"
      @cancel="handleCancel">
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="上级部门" name="parent_id">
          <a-tree-select v-model:value="formData.parent_id" :tree-data="departmentTreeOptions"
            placeholder="请选择上级部门（不选则为顶级部门）" allow-clear tree-default-expand-all />
        </a-form-item>

        <a-form-item label="部门名称" name="dept_name">
          <a-input v-model:value="formData.dept_name" placeholder="请输入部门名称" />
        </a-form-item>

        <a-form-item label="部门编码" name="dept_code">
          <a-input v-model:value="formData.dept_code" placeholder="请输入部门编码" />
        </a-form-item>

        <a-form-item label="部门类型" name="dept_type">
          <a-select v-model:value="formData.dept_type" placeholder="请选择部门类型" allow-clear>
            <a-select-option value="clinical">临床科室</a-select-option>
            <a-select-option value="administrative">行政部门</a-select-option>
            <a-select-option value="logistics">后勤部门</a-select-option>
            <a-select-option value="medical_tech">医技科室</a-select-option>
          </a-select>
        </a-form-item>



        <a-form-item label="联系电话" name="contact_phone">
          <a-input v-model:value="formData.contact_phone" placeholder="请输入联系电话" />
        </a-form-item>

        <a-form-item label="联系邮箱" name="contact_email">
          <a-input v-model:value="formData.contact_email" placeholder="请输入联系邮箱" />
        </a-form-item>

        <a-form-item label="部门描述" name="description">
          <a-textarea v-model:value="formData.description" placeholder="请输入部门描述" :rows="3" />
        </a-form-item>

        <a-form-item label="状态" name="is_active">
          <a-radio-group v-model:value="formData.is_active">
            <a-radio :value="true">启用</a-radio>
            <a-radio :value="false">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import {
  getDepartments,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  type Department,
  type DepartmentForm
} from '@/api/system'
import type { Rule } from 'ant-design-vue/es/form'
import type { FormInstance } from 'ant-design-vue'

const loading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const formRef = ref<FormInstance>()
const editingId = ref<string | null>(null)

const departmentList = ref<Department[]>([])

const formData = reactive<DepartmentForm>({
  dept_name: '',
  dept_code: '',
  parent_id: undefined,
  dept_type: 'clinical',
  description: '',
  manager_id: undefined,
  contact_phone: '',
  contact_email: '',
  is_active: true
})

// 表格列配置
const columns = [
  {
    title: '部门名称',
    dataIndex: 'dept_name',
    key: 'dept_name',
    width: 200,
    align: 'left'
  },
  {
    title: '部门编码',
    dataIndex: 'dept_code',
    key: 'dept_code',
    width: 120,
    align: 'center'
  },
  {
    title: '部门类型',
    dataIndex: 'dept_type',
    key: 'dept_type',
    width: 100,
    align: 'center'
  },
  {
    title: '负责人',
    dataIndex: ['manager', 'user_name'],
    key: 'manager_name',
    width: 120,
    align: 'center'
  },
  {
    title: '联系电话',
    dataIndex: 'contact_phone',
    key: 'contact_phone',
    width: 120,
    align: 'center'
  },
  {
    title: '状态',
    key: 'is_active',
    dataIndex: 'is_active',
    width: 100,
    align: 'center'
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true,
    width: 200
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    align: 'center',
    fixed: 'right'
  }
]

// 表单验证规则
const rules: Record<string, Rule[]> = {
  dept_name: [
    { required: true, message: '请输入部门名称', trigger: 'blur' }
  ],
  dept_type: [
    { required: true, message: '请选择部门类型', trigger: 'change' }
  ]
}

// 弹窗标题
const modalTitle = computed(() => {
  return editingId.value ? '编辑部门' : '新增部门'
})

// 部门树形选择器选项
const departmentTreeOptions = computed(() => {
  const convertToTreeData = (departments: Department[]): any[] => {
    return departments.map(dept => ({
      title: dept.dept_name,
      value: dept.id,
      key: dept.id,
      children: dept.children ? convertToTreeData(dept.children) : undefined
    }))
  }

  // 编辑时需要过滤掉当前部门及其子部门
  let filteredDepartments = departmentList.value
  if (editingId.value) {
    const filterDepartments = (depts: Department[], excludeId: string): Department[] => {
      return depts.filter(dept => {
        if (dept.id === excludeId) return false
        if (dept.children) {
          dept.children = filterDepartments(dept.children, excludeId)
        }
        return true
      })
    }
    filteredDepartments = filterDepartments([...departmentList.value], editingId.value)
  }

  return convertToTreeData(filteredDepartments)
})

// 获取部门类型颜色
const getDeptTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'clinical': 'blue',
    'administrative': 'green',
    'logistics': 'orange',
    'medical_tech': 'purple'
  }
  return colorMap[type] || 'default'
}

// 获取部门类型文本
const getDeptTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    'clinical': '临床科室',
    'administrative': '行政部门',
    'logistics': '后勤部门',
    'medical_tech': '医技科室'
  }
  return textMap[type] || type
}

// 将平铺的部门数据转换为树形结构
const buildDepartmentTree = (departments: Department[], parentId?: string): Department[] => {
  const result = departments
    .filter(dept => dept.parent_id === parentId)
    .map(dept => {
      const children = buildDepartmentTree(departments, dept.id)
      return {
        ...dept,
        children: children.length > 0 ? children : undefined
      }
    })

  console.log('buildDepartmentTree:', {
    parentId,
    totalDepartments: departments.length,
    filteredCount: result.length,
    result: result.map(r => ({ name: r.dept_name, id: r.id, hasChildren: !!r.children }))
  })
  return result
}

// 加载部门数据
const loadDepartments = async () => {
  loading.value = true
  try {
    const response = await getDepartments()
    console.log('API响应:', response)

    // 检查响应格式，如果是分页格式则提取list，否则直接使用
    const allDepartments = Array.isArray(response) ? response : response.list || []
    console.log('处理后的部门数据:', allDepartments)

    // 构建树形结构
    departmentList.value = buildDepartmentTree(allDepartments)
    console.log('最终的树形数据:', departmentList.value)
  } catch (error) {
    console.error('加载部门数据失败:', error)
    message.error('加载部门数据失败')
  } finally {
    loading.value = false
  }
}

// 新增部门
const handleAdd = () => {
  editingId.value = null
  resetForm()
  modalVisible.value = true
}

// 添加子部门
const handleAddChild = (parent: Department) => {
  editingId.value = null
  resetForm()
  formData.parent_id = parent.id
  modalVisible.value = true
}

// 编辑部门
const handleEdit = (record: Department) => {
  editingId.value = record.id
  Object.assign(formData, {
    dept_name: record.dept_name,
    dept_code: record.dept_code,
    parent_id: record.parent_id,
    dept_type: record.dept_type,
    description: record.description,
    manager_id: record.manager_id,
    contact_phone: record.contact_phone,
    contact_email: record.contact_email,
    is_active: record.is_active
  })
  modalVisible.value = true
}

// 删除部门
const handleDelete = async (record: Department) => {
  try {
    await deleteDepartment(record.id)
    message.success('删除成功')
    loadDepartments()
  } catch (error) {
    message.error('删除失败')
  }
}

// 刷新数据
const handleRefresh = () => {
  loadDepartments()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    modalLoading.value = true

    // 清理表单数据，移除空值
    const submitData = { ...formData }
    if (!submitData.parent_id) {
      delete submitData.parent_id
    }
    if (!submitData.dept_code) {
      delete submitData.dept_code
    }
    if (!submitData.description) {
      delete submitData.description
    }
    if (!submitData.contact_phone) {
      delete submitData.contact_phone
    }
    if (!submitData.contact_email) {
      delete submitData.contact_email
    }

    if (editingId.value) {
      await updateDepartment(editingId.value, submitData)
      message.success('更新成功')
    } else {
      await createDepartment(submitData)
      message.success('创建成功')
    }

    modalVisible.value = false
    loadDepartments()
  } catch (error) {
    console.error('提交失败:', error)
    message.error('提交失败')
  } finally {
    modalLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    dept_name: '',
    dept_code: '',
    parent_id: undefined,
    dept_type: 'clinical',
    description: '',
    manager_id: undefined,
    contact_phone: '',
    contact_email: '',
    is_active: true
  })
  formRef.value?.resetFields()
}

onMounted(() => {
  loadDepartments()
})
</script>

<style scoped>
.departments-page {
  padding: 24px;
}

/* 表格样式优化 */
:deep(.ant-table) {
  .ant-table-thead > tr > th {
    background-color: #fafafa;
    font-weight: 600;
    text-align: center;
  }

  .ant-table-tbody > tr > td {
    padding: 12px 16px;
  }

  .ant-table-tbody > tr:hover > td {
    background-color: #f5f5f5;
  }
}

/* 状态标签样式 */
:deep(.ant-tag) {
  margin: 0;
}
</style>
