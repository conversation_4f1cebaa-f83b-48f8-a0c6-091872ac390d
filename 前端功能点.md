# 医院内部控制与运营管理系统 - 前端功能点

## 模块一：统一支撑平台

### 1.1 登录认证 ✅
- **登录页面** (`/login`) ✅
  - 用户名/密码登录 ✅
  - 记住密码功能 ✅
  - 登录状态保持 ✅
  - 错误提示处理 ✅

### 1.2 主布局框架 ✅
- **布局组件** (`/layout`) ✅
  - 顶部导航栏（用户信息、消息、退出） ✅
  - 左侧菜单栏（功能模块导航） ✅
  - 面包屑导航 ✅
  - 内容区域 ✅
  - 响应式布局 ✅

### 1.3 工作台首页 ✅
- **仪表板** (`/dashboard`) ✅
  - 待办任务统计 ✅
  - 预算执行概览 ✅
  - 最近申请记录 ✅
  - 系统公告 ✅
  - 快捷操作入口 ✅

### 1.4 组织权限管理 ✅
- **部门管理** (`/system/departments`) ✅
  - 部门树形展示 ✅
  - 新增/编辑/删除部门 ✅
  - 部门层级管理 ✅
  - 部门负责人设置 ✅

- **用户管理** (`/system/users`) ✅
  - 用户列表（表格展示） ✅
  - 用户搜索和筛选 ✅
  - 新增/编辑/删除用户 ✅
  - 用户导入/导出 ✅
  - 用户角色分配 ✅
  - 用户状态管理 ✅

- **角色管理** (`/system/roles`) ✅
  - 角色列表管理 ✅
  - 角色权限配置 ✅
  - 权限树形选择 ✅
  - 角色用户关联 ✅

### 1.5 消息中心 ✅
- **消息列表** (`/messages`) ✅
  - 消息分类（待办、通知、公告） ✅
  - 消息状态管理 ✅
  - 消息详情查看 ✅
  - 批量操作 ✅

### 1.6 文件管理 ✅
- **文件管理** (`/system/files`) ✅
  - 文件上传下载 ✅
  - 文件分类管理 ✅
  - 文件权限控制 ✅
  - 文件分享功能 ✅

## 模块二：全面预算管理

### 2.1 预算方案管理 ✅
- **预算方案** (`/budget/schemes`) ✅
  - 年度预算方案列表 ✅
  - 新增/编辑预算方案 ✅
  - 预算方案状态管理 ✅
  - 预算方案复制 ✅

### 2.2 预算科目管理 ✅
- **预算科目** (`/budget/subjects`) ✅
  - 科目树形结构展示 ✅
  - 科目层级管理 ✅
  - 科目编码规则 ✅
  - 科目类型设置 ✅

### 2.3 预算明细管理 ✅
- **预算明细** (`/budget/items`) ✅
  - 部门预算明细表格 ✅
  - 预算金额录入 ✅
  - 预算执行情况显示 ✅
  - 预算调整申请 ✅
  - 预算分析图表 ✅

### 2.4 预算分析 ✅
- **预算分析** (`/budget/analysis`) ✅
  - 预算执行率图表 ✅
  - 部门预算对比 ✅
  - 科目预算分析 ✅
  - 预算预警提示 ✅

## 模块三：支出控制管理 ✅

### 3.1 事前申请 ✅
- **事前申请** (`/expense/pre-applications`) ✅
  - 申请单列表 ✅
  - 新建申请（差旅/会议/培训） ✅
  - 申请表单填写 ✅
  - 申请进度跟踪 ✅
  - 申请历史查询 ✅

### 3.2 费用报销 ✅
- **报销申请** (`/expense/applications`) ✅
  - 报销单列表 ✅
  - 新建报销单 ✅
  - 报销明细录入 ✅
  - 发票信息管理 ✅
  - 关联事前申请 ✅
  - 报销进度查询 ✅
  - 个人报销历史 ✅

### 3.3 审批工作台 ✅
- **审批中心** (`/expense/approvals`) ✅
  - 待办任务列表 ✅
  - 审批单据详情 ✅
  - 审批操作（同意/驳回/转办） ✅
  - 审批意见填写 ✅
  - 审批历史记录 ✅
  - 批量审批 ✅

### 3.4 付款管理 ✅
- **付款管理** (`/expense/payments`) ✅
  - 付款单列表 ✅
  - 付款单生成 ✅
  - 付款状态跟踪 ✅
  - 银企直连支付 ✅
  - 付款统计分析 ✅

## 模块四：采购管理 ✅

### 4.1 供应商管理 ✅
- **供应商管理** (`/procurement/suppliers`) ✅
  - 供应商信息库 ✅
  - 供应商资质管理 ✅
  - 供应商评估 ✅
  - 供应商状态管理 ✅
  - 供应商档案 ✅

### 4.2 采购申请 ✅
- **采购申请** (`/procurement/requisitions`) ✅
  - 采购需求单 ✅
  - 采购明细录入 ✅
  - 采购预算关联 ✅
  - 采购审批流程 ✅
  - 采购执行跟踪 ✅

## 模块五：合同管理 ✅

### 5.1 合同台账 ✅
- **合同管理** (`/contract/contracts`) ✅
  - 合同列表管理 ✅
  - 合同信息录入 ✅
  - 合同状态跟踪 ✅
  - 合同到期提醒 ✅
  - 合同查询统计 ✅

### 5.2 付款计划 ✅
- **付款计划** (`/contract/payment-schedules`) ✅
  - 合同付款计划 ✅
  - 付款节点管理 ✅
  - 付款申请生成 ✅
  - 付款执行跟踪 ✅

## 模块六：资产管理 ✅

### 6.1 资产分类 ✅
- **资产分类** (`/asset/categories`) ✅
  - 资产分类树 ✅
  - 分类编码管理 ✅
  - 分类属性设置 ✅

### 6.2 资产台账 ✅
- **资产管理** (`/asset/assets`) ✅
  - 资产卡片管理 ✅
  - 资产条码生成 ✅
  - 资产领用/归还 ✅
  - 资产转移/调拨 ✅
  - 资产维保记录 ✅
  - 资产盘点 ✅
  - 资产处置 ✅

### 6.3 资产折旧 ✅
- **资产折旧** (`/asset/depreciation`) ✅
  - 折旧计算 ✅
  - 折旧记录查询 ✅
  - 折旧方法管理 ✅

### 6.4 资产维护 ✅
- **资产维护** (`/asset/maintenance`) ✅
  - 维护记录管理 ✅
  - 维护计划制定 ✅
  - 维护费用统计 ✅

## 通用组件功能

### 表格组件
- 分页功能
- 排序功能
- 筛选功能
- 导出功能
- 批量操作

### 表单组件
- 表单验证
- 动态表单
- 文件上传
- 日期选择
- 下拉选择

### 图表组件
- 柱状图
- 饼图
- 折线图
- 仪表盘

### 工具功能
- 权限控制
- 路由守卫
- 请求拦截
- 错误处理
- 加载状态
- 消息提示

## 页面布局规范

### 列表页面
- 搜索区域
- 操作按钮区
- 数据表格区
- 分页组件

### 表单页面
- 表单标题
- 表单内容区
- 操作按钮区
- 返回按钮

### 详情页面
- 详情标题
- 信息展示区
- 相关操作
- 关联数据

## 交互设计

### 操作反馈
- 成功提示
- 错误提示
- 确认对话框
- 加载动画

### 数据展示
- 空状态提示
- 加载状态
- 错误状态
- 数据刷新
