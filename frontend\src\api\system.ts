import request from './request'

// 部门管理相关接口
export interface Department {
  id: string
  parent_id?: string
  dept_name: string
  dept_code?: string
  dept_type: string
  description?: string
  manager_id?: string
  manager?: {
    id: string
    user_name: string
    job_title?: string
  }
  contact_phone?: string
  contact_email?: string
  is_active: boolean
  created_at: string
  updated_at: string
  children?: Department[]
}

export interface DepartmentForm {
  dept_name: string
  dept_code?: string
  parent_id?: string
  dept_type: string
  description?: string
  manager_id?: string
  contact_phone?: string
  contact_email?: string
  is_active: boolean
}

// 获取部门列表
export const getDepartments = (): Promise<Department[]> => {
  return request.get('/api/v1/departments', {
    params: {
      page: 1,
      page_size: 100
    }
  })
}

// 获取部门树形结构
export const getDepartmentTree = (): Promise<Department[]> => {
  return request.get('/api/v1/departments/tree')
}

// 创建部门
export const createDepartment = (data: DepartmentForm): Promise<Department> => {
  return request.post('/api/v1/departments', data)
}

// 更新部门信息
export const updateDepartment = (id: number, data: DepartmentForm): Promise<Department> => {
  return request.put(`/api/v1/departments/${id}`, data)
}

// 删除部门
export const deleteDepartment = (id: number): Promise<void> => {
  return request.delete(`/api/v1/departments/${id}`)
}

// 用户管理相关接口
export interface User {
  id: string
  department_id: string
  user_name: string
  employee_id?: string
  email?: string
  phone_number?: string
  job_title?: string
  is_active: boolean
  created_at: string
  updated_at: string
  department?: {
    id: string
    dept_name: string
  }
  roles?: Array<{
    id: string
    role_name: string
  }>
}

export interface UserForm {
  department_id: string
  user_name: string
  employee_id?: string
  email?: string
  phone_number?: string
  password?: string
  job_title?: string
  is_active: boolean
  role_ids?: string[]
}

export interface UserQuery {
  page?: number
  page_size?: number
  keyword?: string
  department_id?: string
  is_active?: boolean
  role_id?: string
}

// 获取用户列表
export const getUsers = (params: UserQuery): Promise<{
  list: User[]
  total: number
  page: number
  page_size: number
}> => {
  return request.get('/api/v1/users', { params })
}

// 获取用户详情
export const getUserDetail = (id: number): Promise<User> => {
  return request.get(`/api/v1/users/${id}`)
}

// 创建用户
export const createUser = (data: UserForm): Promise<User> => {
  return request.post('/api/v1/users', data)
}

// 更新用户信息
export const updateUser = (id: number, data: UserForm): Promise<User> => {
  return request.put(`/api/v1/users/${id}`, data)
}

// 删除用户
export const deleteUser = (id: number): Promise<void> => {
  return request.delete(`/api/v1/users/${id}`)
}

// 批量导入用户
export const importUsers = (file: File): Promise<{
  success_count: number
  error_count: number
  errors: string[]
}> => {
  const formData = new FormData()
  formData.append('file', file)
  return request.post('/api/v1/users/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 导出用户数据
export const exportUsers = (params: UserQuery): Promise<Blob> => {
  return request.get('/api/v1/users/export', { 
    params,
    responseType: 'blob'
  })
}

// 角色管理相关接口
export interface Role {
  id: string
  role_name: string
  role_code?: string
  description?: string
  permissions?: string[]
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface RoleForm {
  role_name: string
  role_code?: string
  description?: string
  is_active: boolean
  permissions?: string[]
}

// 获取角色列表
export const getRoles = (): Promise<Role[]> => {
  return request.get('/api/v1/roles')
}

// 创建角色
export const createRole = (data: RoleForm): Promise<Role> => {
  return request.post('/api/v1/roles', data)
}

// 更新角色
export const updateRole = (id: number, data: RoleForm): Promise<Role> => {
  return request.put(`/api/v1/roles/${id}`, data)
}

// 删除角色
export const deleteRole = (id: number): Promise<void> => {
  return request.delete(`/api/v1/roles/${id}`)
}

// 分配权限
export const assignPermissions = (roleId: number, permissionIds: number[]): Promise<void> => {
  return request.post(`/api/v1/roles/${roleId}/permissions`, { permission_ids: permissionIds })
}
