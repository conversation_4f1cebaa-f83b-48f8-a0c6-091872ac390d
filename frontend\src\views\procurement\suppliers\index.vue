<template>
  <div class="suppliers-page">
    <a-card :bordered="false">
      <template #title>
        <span>供应商管理</span>
      </template>

      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <PlusOutlined />
            </template>
            新增供应商
          </a-button>
          <a-button @click="handleRefresh">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 搜索区域 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="关键词">
            <a-input v-model:value="searchForm.keyword" placeholder="供应商名称/编码/联系人" allow-clear style="width: 200px" />
          </a-form-item>

          <a-form-item label="供应商类型">
            <a-select v-model:value="searchForm.type" placeholder="请选择类型" allow-clear style="width: 120px">
              <a-select-option value="goods">商品供应商</a-select-option>
              <a-select-option value="service">服务供应商</a-select-option>
              <a-select-option value="both">综合供应商</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="状态">
            <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear style="width: 120px">
              <a-select-option value="active">启用</a-select-option>
              <a-select-option value="inactive">禁用</a-select-option>
              <a-select-option value="blacklist">黑名单</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon>
                  <SearchOutlined />
                </template>
                搜索
              </a-button>
              <a-button @click="handleReset">
                <template #icon>
                  <ReloadOutlined />
                </template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 供应商列表 -->
      <a-table :columns="columns" :data-source="supplierList" :loading="loading" :pagination="pagination" row-key="id"
        size="middle" @change="handleTableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleView(record)">
                查看
              </a-button>
              <a-button type="link" size="small" @click="handleEdit(record)">
                编辑
              </a-button>
              <a-dropdown>
                <a-button type="link" size="small">
                  更多
                  <DownOutlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="enable" @click="handleUpdateStatus(record, 'active')"
                      v-if="record.status !== 'active'">
                      启用
                    </a-menu-item>
                    <a-menu-item key="disable" @click="handleUpdateStatus(record, 'inactive')"
                      v-if="record.status !== 'inactive'">
                      禁用
                    </a-menu-item>
                    <a-menu-item key="blacklist" @click="handleUpdateStatus(record, 'blacklist')"
                      v-if="record.status !== 'blacklist'">
                      加入黑名单
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" @click="handleDelete(record)" danger>
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑供应商弹窗 -->
    <a-modal v-model:open="modalVisible" :title="modalTitle" :confirm-loading="modalLoading" width="800px"
      @ok="handleSubmit" @cancel="handleCancel">
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="供应商名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入供应商名称" />
        </a-form-item>

        <a-form-item label="统一社会信用代码" name="credit_code">
          <a-input v-model:value="formData.credit_code" placeholder="请输入统一社会信用代码" />
        </a-form-item>

        <a-form-item label="供应商类型" name="['contact_info', 'type']">
          <a-radio-group v-model:value="formData.contact_info!.type">
            <a-radio value="goods">商品供应商</a-radio>
            <a-radio value="service">服务供应商</a-radio>
            <a-radio value="both">综合供应商</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="联系人" name="['contact_info', 'contact_person']">
          <a-input v-model:value="formData.contact_info!.contact_person" placeholder="请输入联系人姓名" />
        </a-form-item>

        <a-form-item label="联系电话" name="['contact_info', 'contact_phone']">
          <a-input v-model:value="formData.contact_info!.contact_phone" placeholder="请输入联系电话" />
        </a-form-item>

        <a-form-item label="联系邮箱" name="['contact_info', 'contact_email']">
          <a-input v-model:value="formData.contact_info!.contact_email" placeholder="请输入联系邮箱" />
        </a-form-item>

        <a-form-item label="联系地址" name="['contact_info', 'address']">
          <a-textarea v-model:value="formData.contact_info!.address" placeholder="请输入联系地址" :rows="2" />
        </a-form-item>

        <a-form-item label="银行账号" name="['contact_info', 'bank_account']">
          <a-input v-model:value="formData.contact_info!.bank_account" placeholder="请输入银行账号" />
        </a-form-item>

        <a-form-item label="开户银行" name="['contact_info', 'bank_name']">
          <a-input v-model:value="formData.contact_info!.bank_name" placeholder="请输入开户银行" />
        </a-form-item>

        <a-form-item label="税号" name="['contact_info', 'tax_number']">
          <a-input v-model:value="formData.contact_info!.tax_number" placeholder="请输入税号" />
        </a-form-item>

        <a-form-item label="营业执照号" name="['contact_info', 'business_license']">
          <a-input v-model:value="formData.contact_info!.business_license" placeholder="请输入营业执照号" />
        </a-form-item>

        <a-form-item label="资质等级" name="['contact_info', 'qualification_level']">
          <a-select v-model:value="formData.contact_info!.qualification_level" placeholder="请选择资质等级">
            <a-select-option value="A">A级</a-select-option>
            <a-select-option value="B">B级</a-select-option>
            <a-select-option value="C">C级</a-select-option>
            <a-select-option value="D">D级</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="formData.status">
            <a-radio :value="1">合作中</a-radio>
            <a-radio :value="0">黑名单</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 查看详情弹窗 -->
    <a-modal v-model:open="viewModalVisible" title="供应商详情" :footer="null" width="800px">
      <a-descriptions :column="2" bordered v-if="currentRecord">
        <a-descriptions-item label="供应商名称" :span="2">
          {{ currentRecord.name }}
        </a-descriptions-item>
        <a-descriptions-item label="供应商编码">
          {{ currentRecord.code }}
        </a-descriptions-item>
        <a-descriptions-item label="供应商类型">
          <a-tag :color="getTypeColor(currentRecord.type)">
            {{ getTypeText(currentRecord.type) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="联系人">
          {{ currentRecord.contact_person }}
        </a-descriptions-item>
        <a-descriptions-item label="联系电话">
          {{ currentRecord.contact_phone }}
        </a-descriptions-item>
        <a-descriptions-item label="联系邮箱" :span="2">
          {{ currentRecord.contact_email }}
        </a-descriptions-item>
        <a-descriptions-item label="联系地址" :span="2">
          {{ currentRecord.address }}
        </a-descriptions-item>
        <a-descriptions-item label="银行账号" v-if="currentRecord.bank_account">
          {{ currentRecord.bank_account }}
        </a-descriptions-item>
        <a-descriptions-item label="开户银行" v-if="currentRecord.bank_name">
          {{ currentRecord.bank_name }}
        </a-descriptions-item>
        <a-descriptions-item label="税号" v-if="currentRecord.tax_number">
          {{ currentRecord.tax_number }}
        </a-descriptions-item>
        <a-descriptions-item label="营业执照号" v-if="currentRecord.business_license">
          {{ currentRecord.business_license }}
        </a-descriptions-item>
        <a-descriptions-item label="资质等级" v-if="currentRecord.qualification_level">
          {{ currentRecord.qualification_level }}级
        </a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="getStatusColor(currentRecord.status)">
            {{ getStatusText(currentRecord.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">
          {{ currentRecord.created_at }}
        </a-descriptions-item>
        <a-descriptions-item label="更新时间">
          {{ currentRecord.updated_at }}
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import {
  getSuppliers,
  createSupplier,
  updateSupplier,
  deleteSupplier,
  type Supplier,
  type SupplierForm,
  type SupplierQuery
} from '@/api/procurement'
import type { Rule } from 'ant-design-vue/es/form'
import type { FormInstance } from 'ant-design-vue'

const loading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const viewModalVisible = ref(false)
const formRef = ref<FormInstance>()
const editingId = ref<number | null>(null)

const supplierList = ref<Supplier[]>([])
const currentRecord = ref<Supplier | null>(null)

const searchForm = reactive<SupplierQuery>({
  keyword: '',
  type: undefined,
  status: undefined,
  page: 1,
  page_size: 10
})

const formData = reactive<SupplierForm>({
  name: '',
  credit_code: '',
  status: 1,  // 默认为合作中
  contact_info: {
    contact_person: '',
    contact_phone: '',
    contact_email: '',
    address: '',
    bank_account: '',
    bank_name: '',
    tax_number: '',
    business_license: '',
    qualification_level: '',
    type: 'goods'
  }
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '供应商名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '统一社会信用代码',
    dataIndex: 'credit_code',
    key: 'credit_code',
    width: 180,
    ellipsis: true,
    customRender: ({ text }: any) => text || '-'
  },
  {
    title: '联系人',
    key: 'contact_person',
    width: 100,
    customRender: ({ record }: any) => record.contact_info?.contact_person || '-'
  },
  {
    title: '联系电话',
    key: 'contact_phone',
    width: 120,
    customRender: ({ record }: any) => record.contact_info?.contact_phone || '-'
  },
  {
    title: '联系邮箱',
    key: 'contact_email',
    width: 180,
    ellipsis: true,
    customRender: ({ record }: any) => record.contact_info?.contact_email || '-'
  },
  {
    title: '地址',
    key: 'address',
    width: 200,
    ellipsis: true,
    customRender: ({ record }: any) => record.contact_info?.address || '-'
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right'
  }
]

// 表单验证规则
const rules: Record<string, Rule[]> = {
  name: [
    { required: true, message: '请输入供应商名称', trigger: 'blur' }
  ],
  credit_code: [
    { pattern: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/, message: '请输入正确的统一社会信用代码', trigger: 'blur' }
  ]
}

// 弹窗标题
const modalTitle = computed(() => {
  return editingId.value ? '编辑供应商' : '新增供应商'
})

// 获取类型颜色
const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'goods': 'blue',
    'service': 'green',
    'both': 'purple'
  }
  return colorMap[type] || 'default'
}

// 获取类型文本
const getTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    'goods': '商品供应商',
    'service': '服务供应商',
    'both': '综合供应商'
  }
  return textMap[type] || type
}

// 获取状态颜色
const getStatusColor = (status: number) => {
  const colorMap: Record<number, string> = {
    1: 'green',   // 合作中
    0: 'red'      // 黑名单
  }
  return colorMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: number) => {
  const textMap: Record<number, string> = {
    1: '合作中',
    0: '黑名单'
  }
  return textMap[status] || '未知'
}

// 加载供应商数据
const loadSuppliers = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.current,
      page_size: pagination.pageSize
    }
    const response = await getSuppliers(params)
    console.log('供应商数据:', response) // 调试信息
    supplierList.value = response.list || []
    pagination.total = response.total || 0
  } catch (error) {
    console.error('加载供应商数据失败:', error)
    message.error('加载供应商数据失败')
    supplierList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadSuppliers()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    type: undefined,
    status: undefined
  })
  pagination.current = 1
  loadSuppliers()
}

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadSuppliers()
}

// 新增供应商
const handleAdd = () => {
  editingId.value = null
  resetForm()
  modalVisible.value = true
}

// 编辑供应商
const handleEdit = (record: Supplier) => {
  editingId.value = record.id
  Object.assign(formData, {
    name: record.name,
    credit_code: record.credit_code,
    status: record.status,
    contact_info: {
      contact_person: record.contact_info?.contact_person || '',
      contact_phone: record.contact_info?.contact_phone || '',
      contact_email: record.contact_info?.contact_email || '',
      address: record.contact_info?.address || '',
      bank_account: record.contact_info?.bank_account || '',
      bank_name: record.contact_info?.bank_name || '',
      tax_number: record.contact_info?.tax_number || '',
      business_license: record.contact_info?.business_license || '',
      qualification_level: record.contact_info?.qualification_level || '',
      type: record.contact_info?.type || 'goods'
    }
  })
  modalVisible.value = true
}

// 查看详情
const handleView = (record: Supplier) => {
  currentRecord.value = record
  viewModalVisible.value = true
}

// 更新状态
const handleUpdateStatus = (record: Supplier, status: string) => {
  Modal.confirm({
    title: '确认操作',
    content: `确定要将供应商状态更新为"${getStatusText(status)}"吗？`,
    onOk: async () => {
      try {
        await updateSupplier(record.id, { ...record, status })
        message.success('状态更新成功')
        loadSuppliers()
      } catch (error) {
        message.error('状态更新失败')
      }
    }
  })
}

// 删除供应商
const handleDelete = (record: Supplier) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这个供应商吗？删除后无法恢复。',
    onOk: async () => {
      try {
        await deleteSupplier(record.id)
        message.success('删除成功')
        loadSuppliers()
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

// 刷新数据
const handleRefresh = () => {
  loadSuppliers()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    modalLoading.value = true

    if (editingId.value) {
      await updateSupplier(editingId.value, formData)
      message.success('更新成功')
    } else {
      await createSupplier(formData)
      message.success('创建成功')
    }

    modalVisible.value = false
    loadSuppliers()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    modalLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    code: '',
    type: 'goods',
    contact_person: '',
    contact_phone: '',
    contact_email: '',
    address: '',
    bank_account: '',
    bank_name: '',
    tax_number: '',
    business_license: '',
    qualification_level: undefined,
    status: 'active'
  })
  formRef.value?.resetFields()
}

onMounted(() => {
  loadSuppliers()
})
</script>

<style scoped>
.suppliers-page {
  padding: 24px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}
</style>
