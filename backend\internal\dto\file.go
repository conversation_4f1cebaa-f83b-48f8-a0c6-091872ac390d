package dto

import (
	"github.com/google/uuid"
)

// FileUploadResponse 文件上传响应
type FileUploadResponse struct {
	ID           uuid.UUID `json:"id"`
	FileName     string    `json:"file_name"`
	OriginalName string    `json:"original_name"`
	FileSize     int64     `json:"file_size"`
	FileType     string    `json:"file_type"`
	FilePath     string    `json:"file_path"`
	FileURL      string    `json:"file_url"`
	CreatedAt    string    `json:"created_at"`
	CreatedBy    uuid.UUID `json:"created_by"`
}

// FileResponse 文件响应
type FileResponse struct {
	ID           uuid.UUID            `json:"id"`
	FileName     string               `json:"file_name"`
	OriginalName string               `json:"original_name"`
	FileSize     int64                `json:"file_size"`
	FileType     string               `json:"file_type"`
	FilePath     string               `json:"file_path"`
	FileURL      string               `json:"file_url"`
	BusinessType *string              `json:"business_type,omitempty"`
	BusinessID   *uuid.UUID           `json:"business_id,omitempty"`
	Description  *string              `json:"description,omitempty"`
	IsPublic     bool                 `json:"is_public"`
	DownloadCount int                 `json:"download_count"`
	CreatedAt    string               `json:"created_at"`
	UpdatedAt    string               `json:"updated_at"`
	CreatedBy    uuid.UUID            `json:"created_by"`
	Uploader     *UserSimpleResponse  `json:"uploader,omitempty"`
}

// FileListRequest 文件列表请求
type FileListRequest struct {
	Page         int        `form:"page" validate:"min=1"`
	PageSize     int        `form:"page_size" validate:"min=1,max=100"`
	BusinessType string     `form:"business_type"`
	BusinessID   *uuid.UUID `form:"business_id" validate:"omitempty,uuid"`
	FileType     string     `form:"file_type"`
	IsPublic     *bool      `form:"is_public"`
	UploaderID   *uuid.UUID `form:"uploader_id" validate:"omitempty,uuid"`
	Keyword      string     `form:"keyword"`
	StartDate    string     `form:"start_date" validate:"omitempty,datetime=2006-01-02"`
	EndDate      string     `form:"end_date" validate:"omitempty,datetime=2006-01-02"`
}

// BusinessFileRequest 业务关联文件请求
type BusinessFileRequest struct {
	BusinessType string    `form:"business_type" validate:"required,max=50"`
	BusinessID   uuid.UUID `form:"business_id" validate:"required,uuid"`
}

// UpdateFileRequest 更新文件信息请求
type UpdateFileRequest struct {
	FileName     *string    `json:"file_name,omitempty" validate:"omitempty,max=255"`
	Description  *string    `json:"description,omitempty" validate:"omitempty,max=1000"`
	IsPublic     *bool      `json:"is_public,omitempty"`
	BusinessType *string    `json:"business_type,omitempty" validate:"omitempty,max=50"`
	BusinessID   *uuid.UUID `json:"business_id,omitempty" validate:"omitempty,uuid"`
}

// FileStatisticsResponse 文件统计响应
type FileStatisticsResponse struct {
	TotalFiles     int64                    `json:"total_files"`
	TotalSize      int64                    `json:"total_size"`
	TotalDownloads int64                    `json:"total_downloads"`
	ByFileType     map[string]int64         `json:"by_file_type"`
	ByBusinessType map[string]int64         `json:"by_business_type"`
	RecentUploads  []FileResponse           `json:"recent_uploads"`
	PopularFiles   []FileResponse           `json:"popular_files"`
}

// BatchDeleteRequest 批量删除请求
type BatchDeleteRequest struct {
	FileIDs []uuid.UUID `json:"file_ids" validate:"required,dive,uuid"`
}

// FileDownloadRequest 文件下载请求
type FileDownloadRequest struct {
	FileID uuid.UUID `json:"file_id" validate:"required,uuid"`
}

// FileShareRequest 文件分享请求
type FileShareRequest struct {
	FileID      uuid.UUID   `json:"file_id" validate:"required,uuid"`
	ShareType   string      `json:"share_type" validate:"required,oneof=public private"`
	ShareWith   []uuid.UUID `json:"share_with,omitempty" validate:"omitempty,dive,uuid"`
	ExpiresAt   *string     `json:"expires_at,omitempty" validate:"omitempty,datetime=2006-01-02T15:04:05Z07:00"`
	Description *string     `json:"description,omitempty" validate:"omitempty,max=500"`
}

// FileShareResponse 文件分享响应
type FileShareResponse struct {
	ID          uuid.UUID `json:"id"`
	FileID      uuid.UUID `json:"file_id"`
	ShareType   string    `json:"share_type"`
	ShareToken  string    `json:"share_token"`
	ShareURL    string    `json:"share_url"`
	ExpiresAt   *string   `json:"expires_at,omitempty"`
	Description *string   `json:"description,omitempty"`
	CreatedAt   string    `json:"created_at"`
	CreatedBy   uuid.UUID `json:"created_by"`
}
