package services

import (
	"hospital-management/internal/dto"
	"hospital-management/internal/models"
	"hospital-management/pkg/logger"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type CommonService struct {
	db *gorm.DB
}

func NewCommonService(db *gorm.DB) *CommonService {
	return &CommonService{db: db}
}

// ===== 数据字典相关 =====

// GetDictionaries 获取数据字典
func (s *CommonService) GetDictionaries() (map[string]interface{}, error) {
	dictionaries := make(map[string]interface{})

	// 预算控制规则
	dictionaries["budget_control_rules"] = []map[string]string{
		{"value": "STRICT", "label": "严格控制"},
		{"value": "FLEXIBLE", "label": "灵活控制"},
		{"value": "WARNING", "label": "预警提醒"},
	}

	// 预算方案状态
	dictionaries["budget_scheme_status"] = []map[string]string{
		{"value": "DRAFT", "label": "草稿"},
		{"value": "ACTIVE", "label": "生效"},
		{"value": "EXPIRED", "label": "过期"},
	}

	// 预算科目类型
	dictionaries["budget_subject_types"] = []map[string]string{
		{"value": "INCOME", "label": "收入"},
		{"value": "EXPENSE", "label": "支出"},
	}

	// 事前申请类型
	dictionaries["pre_application_types"] = []map[string]string{
		{"value": "TRAVEL", "label": "差旅申请"},
		{"value": "MEETING", "label": "会议申请"},
		{"value": "TRAINING", "label": "培训申请"},
		{"value": "OTHER", "label": "其他申请"},
	}

	// 申请状态
	dictionaries["application_status"] = []map[string]string{
		{"value": "DRAFT", "label": "草稿"},
		{"value": "PENDING", "label": "待审批"},
		{"value": "APPROVED", "label": "已通过"},
		{"value": "REJECTED", "label": "已拒绝"},
		{"value": "CANCELLED", "label": "已取消"},
		{"value": "CLOSED", "label": "已关闭"},
	}

	// 付款状态
	dictionaries["payment_status"] = []map[string]string{
		{"value": "PENDING", "label": "待付款"},
		{"value": "PAID", "label": "已付款"},
		{"value": "FAILED", "label": "付款失败"},
		{"value": "CANCELLED", "label": "已取消"},
	}

	// 供应商状态
	dictionaries["supplier_status"] = []map[string]string{
		{"value": "1", "label": "合作中"},
		{"value": "0", "label": "黑名单"},
	}

	// 合同类型
	dictionaries["contract_types"] = []map[string]string{
		{"value": "采购合同", "label": "采购合同"},
		{"value": "服务合同", "label": "服务合同"},
		{"value": "基建合同", "label": "基建合同"},
		{"value": "租赁合同", "label": "租赁合同"},
		{"value": "其他合同", "label": "其他合同"},
	}

	// 合同状态
	dictionaries["contract_status"] = []map[string]string{
		{"value": "DRAFT", "label": "草稿"},
		{"value": "PENDING", "label": "待审批"},
		{"value": "ACTIVE", "label": "生效中"},
		{"value": "COMPLETED", "label": "已完成"},
		{"value": "TERMINATED", "label": "已终止"},
	}

	// 资产状态
	dictionaries["asset_status"] = []map[string]string{
		{"value": "IN_USE", "label": "在用"},
		{"value": "IDLE", "label": "闲置"},
		{"value": "MAINTENANCE", "label": "维修中"},
		{"value": "SCRAPPED", "label": "已报废"},
	}

	// 资产来源类型
	dictionaries["asset_source_types"] = []map[string]string{
		{"value": "PURCHASE", "label": "采购"},
		{"value": "DONATION", "label": "捐赠"},
		{"value": "TRANSFER", "label": "调拨"},
		{"value": "SELF_BUILD", "label": "自建"},
		{"value": "OTHER", "label": "其他"},
	}

	// 审批状态
	dictionaries["approval_status"] = []map[string]string{
		{"value": "PENDING", "label": "待审批"},
		{"value": "APPROVED", "label": "已通过"},
		{"value": "REJECTED", "label": "已拒绝"},
		{"value": "TRANSFERRED", "label": "已转交"},
	}

	return dictionaries, nil
}

// ===== 统计报表相关 =====

// GetDashboardStatistics 获取仪表盘统计数据
func (s *CommonService) GetDashboardStatistics() (*dto.DashboardStatisticsResponse, error) {
	stats := &dto.DashboardStatisticsResponse{}

	// 预算统计
	budgetStats, err := s.getBudgetStatistics()
	if err != nil {
		logger.Error("Failed to get budget statistics:", err)
		return nil, err
	}
	stats.BudgetStats = budgetStats

	// 支出统计
	expenseStats, err := s.getExpenseStatistics()
	if err != nil {
		logger.Error("Failed to get expense statistics:", err)
		return nil, err
	}
	stats.ExpenseStats = expenseStats

	// 采购统计
	procurementStats, err := s.getProcurementStatistics()
	if err != nil {
		logger.Error("Failed to get procurement statistics:", err)
		return nil, err
	}
	stats.ProcurementStats = procurementStats

	// 合同统计
	contractStats, err := s.getContractStatistics()
	if err != nil {
		logger.Error("Failed to get contract statistics:", err)
		return nil, err
	}
	stats.ContractStats = contractStats

	// 资产统计
	assetStats, err := s.getAssetStatistics()
	if err != nil {
		logger.Error("Failed to get asset statistics:", err)
		return nil, err
	}
	stats.AssetStats = assetStats

	return stats, nil
}

// getBudgetStatistics 获取预算统计
func (s *CommonService) getBudgetStatistics() (*dto.BudgetStatsResponse, error) {
	stats := &dto.BudgetStatsResponse{}

	// 获取当前年度的预算方案
	var activeScheme models.BudgetScheme
	if err := s.db.Where("status = ? AND year = ?", "ACTIVE", 2025).First(&activeScheme).Error; err != nil {
		// 如果没有找到活跃的预算方案，返回空统计
		return stats, nil
	}

	// 统计预算总额、已用金额、冻结金额
	var result struct {
		TotalAmount  decimal.Decimal `json:"total_amount"`
		UsedAmount   decimal.Decimal `json:"used_amount"`
		FrozenAmount decimal.Decimal `json:"frozen_amount"`
	}

	if err := s.db.Model(&models.BudgetItem{}).
		Where("scheme_id = ?", activeScheme.ID).
		Select("COALESCE(SUM(total_amount), 0) as total_amount, COALESCE(SUM(used_amount), 0) as used_amount, COALESCE(SUM(frozen_amount), 0) as frozen_amount").
		Scan(&result).Error; err != nil {
		return nil, err
	}

	stats.TotalBudget = result.TotalAmount
	stats.UsedBudget = result.UsedAmount
	stats.FrozenBudget = result.FrozenAmount
	stats.AvailableBudget = result.TotalAmount.Sub(result.UsedAmount).Sub(result.FrozenAmount)

	// 计算使用率
	if result.TotalAmount.GreaterThan(decimal.Zero) {
		stats.UsageRate = result.UsedAmount.Div(result.TotalAmount).Mul(decimal.NewFromInt(100))
	}

	return stats, nil
}

// getExpenseStatistics 获取支出统计
func (s *CommonService) getExpenseStatistics() (*dto.ExpenseStatsResponse, error) {
	stats := &dto.ExpenseStatsResponse{}

	// 统计报销申请数量和金额
	var expenseResult struct {
		TotalCount  int64           `json:"total_count"`
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	if err := s.db.Model(&models.ExpenseApplication{}).
		Select("COUNT(*) as total_count, COALESCE(SUM(total_amount), 0) as total_amount").
		Scan(&expenseResult).Error; err != nil {
		return nil, err
	}

	stats.TotalExpenseApplications = expenseResult.TotalCount
	stats.TotalExpenseAmount = expenseResult.TotalAmount

	// 统计待审批的报销申请
	var pendingCount int64
	if err := s.db.Model(&models.ExpenseApplication{}).
		Where("status = ?", "PENDING").
		Count(&pendingCount).Error; err != nil {
		return nil, err
	}
	stats.PendingExpenseApplications = pendingCount

	// 统计付款单数量和金额
	var paymentResult struct {
		TotalCount  int64           `json:"total_count"`
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	if err := s.db.Model(&models.Payment{}).
		Select("COUNT(*) as total_count, COALESCE(SUM(amount), 0) as total_amount").
		Scan(&paymentResult).Error; err != nil {
		return nil, err
	}

	stats.TotalPayments = paymentResult.TotalCount
	stats.TotalPaymentAmount = paymentResult.TotalAmount

	return stats, nil
}

// getProcurementStatistics 获取采购统计
func (s *CommonService) getProcurementStatistics() (*dto.ProcurementStatsResponse, error) {
	stats := &dto.ProcurementStatsResponse{}

	// 统计供应商数量
	var supplierCount int64
	if err := s.db.Model(&models.Supplier{}).Count(&supplierCount).Error; err != nil {
		return nil, err
	}
	stats.TotalSuppliers = supplierCount

	// 统计采购申请数量和金额
	var requisitionResult struct {
		TotalCount  int64           `json:"total_count"`
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	if err := s.db.Model(&models.PurchaseRequisition{}).
		Select("COUNT(*) as total_count, COALESCE(SUM(total_amount), 0) as total_amount").
		Scan(&requisitionResult).Error; err != nil {
		return nil, err
	}

	stats.TotalPurchaseRequisitions = requisitionResult.TotalCount
	stats.TotalPurchaseAmount = requisitionResult.TotalAmount

	// 统计待审批的采购申请
	var pendingCount int64
	if err := s.db.Model(&models.PurchaseRequisition{}).
		Where("status = ?", "PENDING").
		Count(&pendingCount).Error; err != nil {
		return nil, err
	}
	stats.PendingPurchaseRequisitions = pendingCount

	return stats, nil
}

// getContractStatistics 获取合同统计
func (s *CommonService) getContractStatistics() (*dto.ContractStatsResponse, error) {
	stats := &dto.ContractStatsResponse{}

	// 统计合同数量和金额
	var contractResult struct {
		TotalCount  int64           `json:"total_count"`
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	if err := s.db.Model(&models.Contract{}).
		Select("COUNT(*) as total_count, COALESCE(SUM(total_amount), 0) as total_amount").
		Scan(&contractResult).Error; err != nil {
		return nil, err
	}

	stats.TotalContracts = contractResult.TotalCount
	stats.TotalContractAmount = contractResult.TotalAmount

	// 统计生效中的合同
	var activeCount int64
	if err := s.db.Model(&models.Contract{}).
		Where("status = ?", "ACTIVE").
		Count(&activeCount).Error; err != nil {
		return nil, err
	}
	stats.ActiveContracts = activeCount

	// 统计付款计划数量和金额
	var scheduleResult struct {
		TotalCount  int64           `json:"total_count"`
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	if err := s.db.Model(&models.ContractPaymentSchedule{}).
		Select("COUNT(*) as total_count, COALESCE(SUM(amount), 0) as total_amount").
		Scan(&scheduleResult).Error; err != nil {
		return nil, err
	}

	stats.TotalPaymentSchedules = scheduleResult.TotalCount
	stats.TotalScheduleAmount = scheduleResult.TotalAmount

	return stats, nil
}

// getAssetStatistics 获取资产统计
func (s *CommonService) getAssetStatistics() (*dto.AssetStatsResponse, error) {
	stats := &dto.AssetStatsResponse{}

	// 统计资产数量和价值
	var assetResult struct {
		TotalCount int64           `json:"total_count"`
		TotalValue decimal.Decimal `json:"total_value"`
	}

	if err := s.db.Model(&models.Asset{}).
		Select("COUNT(*) as total_count, COALESCE(SUM(purchase_price), 0) as total_value").
		Scan(&assetResult).Error; err != nil {
		return nil, err
	}

	stats.TotalAssets = assetResult.TotalCount
	stats.TotalAssetValue = assetResult.TotalValue

	// 统计各状态的资产数量
	var statusCounts []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}

	if err := s.db.Model(&models.Asset{}).
		Select("status, COUNT(*) as count").
		Group("status").
		Scan(&statusCounts).Error; err != nil {
		return nil, err
	}

	for _, sc := range statusCounts {
		switch sc.Status {
		case "IN_USE":
			stats.InUseAssets = sc.Count
		case "IDLE":
			stats.IdleAssets = sc.Count
		case "MAINTENANCE":
			stats.MaintenanceAssets = sc.Count
		case "SCRAPPED":
			stats.ScrappedAssets = sc.Count
		}
	}

	// 统计资产分类数量
	var categoryCount int64
	if err := s.db.Model(&models.AssetCategory{}).Count(&categoryCount).Error; err != nil {
		return nil, err
	}
	stats.TotalCategories = categoryCount

	return stats, nil
}
