package dto

import (
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// ===== 合同管理相关 =====

// CreateContractRequest 创建合同请求
type CreateContractRequest struct {
	ContractName     string          `json:"contract_name" validate:"required,max=255"`
	ContractType     string          `json:"contract_type" validate:"required,max=50"`
	CounterpartyID   *uuid.UUID      `json:"counterparty_id,omitempty" validate:"omitempty,uuid"`
	CounterpartyName string          `json:"counterparty_name" validate:"required,max=255"`
	TotalAmount      decimal.Decimal `json:"total_amount" validate:"required,gte=0"`
	StartDate        *string         `json:"start_date,omitempty" validate:"omitempty,datetime=2006-01-02"`
	EndDate          *string         `json:"end_date,omitempty" validate:"omitempty,datetime=2006-01-02"`
}

// UpdateContractRequest 更新合同请求
type UpdateContractRequest struct {
	ContractName     *string         `json:"contract_name,omitempty" validate:"omitempty,max=255"`
	ContractType     *string         `json:"contract_type,omitempty" validate:"omitempty,max=50"`
	CounterpartyID   *uuid.UUID      `json:"counterparty_id,omitempty" validate:"omitempty,uuid"`
	CounterpartyName *string         `json:"counterparty_name,omitempty" validate:"omitempty,max=255"`
	TotalAmount      *decimal.Decimal `json:"total_amount,omitempty" validate:"omitempty,gte=0"`
	StartDate        *string         `json:"start_date,omitempty" validate:"omitempty,datetime=2006-01-02"`
	EndDate          *string         `json:"end_date,omitempty" validate:"omitempty,datetime=2006-01-02"`
}

// UpdateContractStatusRequest 更新合同状态请求
type UpdateContractStatusRequest struct {
	Status string `json:"status" validate:"required,oneof=DRAFT PENDING ACTIVE COMPLETED TERMINATED"`
}

// ContractResponse 合同响应
type ContractResponse struct {
	ID               uuid.UUID                      `json:"id"`
	ContractCode     string                         `json:"contract_code"`
	ContractName     string                         `json:"contract_name"`
	ContractType     string                         `json:"contract_type"`
	CounterpartyID   *uuid.UUID                     `json:"counterparty_id,omitempty"`
	CounterpartyName string                         `json:"counterparty_name"`
	TotalAmount      decimal.Decimal                `json:"total_amount"`
	StartDate        *string                        `json:"start_date,omitempty"`
	EndDate          *string                        `json:"end_date,omitempty"`
	Status           string                         `json:"status"`
	CreatedAt        string                         `json:"created_at"`
	UpdatedAt        string                         `json:"updated_at"`
	Counterparty     *SupplierSimpleResponse        `json:"counterparty,omitempty"`
	PaymentSchedules []PaymentScheduleResponse      `json:"payment_schedules,omitempty"`
}

// ContractListRequest 合同列表请求
type ContractListRequest struct {
	Page             int        `form:"page" validate:"min=1"`
	PageSize         int        `form:"page_size" validate:"min=1,max=100"`
	Keyword          string     `form:"keyword"`
	ContractType     string     `form:"contract_type"`
	Status           string     `form:"status" validate:"omitempty,oneof=DRAFT PENDING ACTIVE COMPLETED TERMINATED"`
	CounterpartyID   *uuid.UUID `form:"counterparty_id" validate:"omitempty,uuid"`
	StartDateFrom    *string    `form:"start_date_from" validate:"omitempty,datetime=2006-01-02"`
	StartDateTo      *string    `form:"start_date_to" validate:"omitempty,datetime=2006-01-02"`
}

// ===== 付款计划相关 =====

// CreatePaymentScheduleRequest 创建付款计划请求
type CreatePaymentScheduleRequest struct {
	ContractID uuid.UUID       `json:"contract_id" validate:"required,uuid"`
	PhaseName  *string         `json:"phase_name,omitempty" validate:"omitempty,max=100"`
	DueDate    *string         `json:"due_date,omitempty" validate:"omitempty,datetime=2006-01-02"`
	Amount     decimal.Decimal `json:"amount" validate:"required,gt=0"`
}

// UpdatePaymentScheduleRequest 更新付款计划请求
type UpdatePaymentScheduleRequest struct {
	PhaseName *string         `json:"phase_name,omitempty" validate:"omitempty,max=100"`
	DueDate   *string         `json:"due_date,omitempty" validate:"omitempty,datetime=2006-01-02"`
	Amount    *decimal.Decimal `json:"amount,omitempty" validate:"omitempty,gt=0"`
}

// PaymentScheduleResponse 付款计划响应
type PaymentScheduleResponse struct {
	ID         uuid.UUID       `json:"id"`
	ContractID uuid.UUID       `json:"contract_id"`
	PhaseName  *string         `json:"phase_name,omitempty"`
	DueDate    *string         `json:"due_date,omitempty"`
	Amount     decimal.Decimal `json:"amount"`
	Status     string          `json:"status"`
	PaymentID  *uuid.UUID      `json:"payment_id,omitempty"`
	CreatedAt  string          `json:"created_at"`
	UpdatedAt  string          `json:"updated_at"`
	Contract   *ContractSimpleResponse `json:"contract,omitempty"`
	Payment    *PaymentSimpleResponse  `json:"payment,omitempty"`
}

// PaymentScheduleListRequest 付款计划列表请求
type PaymentScheduleListRequest struct {
	Page       int        `form:"page" validate:"min=1"`
	PageSize   int        `form:"page_size" validate:"min=1,max=100"`
	ContractID *uuid.UUID `form:"contract_id" validate:"omitempty,uuid"`
	Status     string     `form:"status" validate:"omitempty,oneof=PENDING PAID"`
	DueDateFrom *string   `form:"due_date_from" validate:"omitempty,datetime=2006-01-02"`
	DueDateTo   *string   `form:"due_date_to" validate:"omitempty,datetime=2006-01-02"`
}

// BatchCreatePaymentScheduleRequest 批量创建付款计划请求
type BatchCreatePaymentScheduleRequest struct {
	ContractID uuid.UUID                       `json:"contract_id" validate:"required,uuid"`
	Schedules  []PaymentScheduleItemRequest    `json:"schedules" validate:"required,dive"`
}

// PaymentScheduleItemRequest 付款计划项请求
type PaymentScheduleItemRequest struct {
	PhaseName *string         `json:"phase_name,omitempty" validate:"omitempty,max=100"`
	DueDate   *string         `json:"due_date,omitempty" validate:"omitempty,datetime=2006-01-02"`
	Amount    decimal.Decimal `json:"amount" validate:"required,gt=0"`
}

// ===== 合同统计分析相关 =====

// ContractStatisticsRequest 合同统计请求
type ContractStatisticsRequest struct {
	StartDate      *string `form:"start_date" validate:"omitempty,datetime=2006-01-02"`
	EndDate        *string `form:"end_date" validate:"omitempty,datetime=2006-01-02"`
	ContractType   string  `form:"contract_type"`
	CounterpartyID *uuid.UUID `form:"counterparty_id" validate:"omitempty,uuid"`
}

// ContractStatisticsResponse 合同统计响应
type ContractStatisticsResponse struct {
	TotalContracts       int64                           `json:"total_contracts"`
	TotalAmount          decimal.Decimal                 `json:"total_amount"`
	ActiveContracts      int64                           `json:"active_contracts"`
	ActiveAmount         decimal.Decimal                 `json:"active_amount"`
	CompletedContracts   int64                           `json:"completed_contracts"`
	CompletedAmount      decimal.Decimal                 `json:"completed_amount"`
	TypeStats            []ContractTypeStatsResponse     `json:"type_stats"`
	CounterpartyStats    []ContractCounterpartyStatsResponse `json:"counterparty_stats"`
	MonthlyStats         []ContractMonthlyStatsResponse  `json:"monthly_stats"`
}

// ContractTypeStatsResponse 合同类型统计响应
type ContractTypeStatsResponse struct {
	ContractType   string          `json:"contract_type"`
	ContractCount  int64           `json:"contract_count"`
	TotalAmount    decimal.Decimal `json:"total_amount"`
}

// ContractCounterpartyStatsResponse 合同对方统计响应
type ContractCounterpartyStatsResponse struct {
	CounterpartyID   *uuid.UUID      `json:"counterparty_id,omitempty"`
	CounterpartyName string          `json:"counterparty_name"`
	ContractCount    int64           `json:"contract_count"`
	TotalAmount      decimal.Decimal `json:"total_amount"`
}

// ContractMonthlyStatsResponse 月度合同统计响应
type ContractMonthlyStatsResponse struct {
	Month         string          `json:"month"`
	ContractCount int64           `json:"contract_count"`
	TotalAmount   decimal.Decimal `json:"total_amount"`
}

// ===== 辅助响应结构 =====

// ContractSimpleResponse 合同简单响应（用于关联显示）
type ContractSimpleResponse struct {
	ID           uuid.UUID       `json:"id"`
	ContractCode string          `json:"contract_code"`
	ContractName string          `json:"contract_name"`
	ContractType string          `json:"contract_type"`
	TotalAmount  decimal.Decimal `json:"total_amount"`
	Status       string          `json:"status"`
}

// PaymentSimpleResponse 付款单简单响应（用于关联显示）
type PaymentSimpleResponse struct {
	ID          uuid.UUID       `json:"id"`
	PaymentCode string          `json:"payment_code"`
	Amount      decimal.Decimal `json:"amount"`
	Status      string          `json:"status"`
	PaidAt      *string         `json:"paid_at,omitempty"`
}

// ===== 合同执行监控相关 =====

// ContractMonitoringRequest 合同监控请求
type ContractMonitoringRequest struct {
	Status         string     `form:"status" validate:"omitempty,oneof=ACTIVE COMPLETED"`
	DueDateFrom    *string    `form:"due_date_from" validate:"omitempty,datetime=2006-01-02"`
	DueDateTo      *string    `form:"due_date_to" validate:"omitempty,datetime=2006-01-02"`
	CounterpartyID *uuid.UUID `form:"counterparty_id" validate:"omitempty,uuid"`
}

// ContractMonitoringResponse 合同监控响应
type ContractMonitoringResponse struct {
	ExpiringContracts    []ContractResponse        `json:"expiring_contracts"`    // 即将到期的合同
	OverduePayments      []PaymentScheduleResponse `json:"overdue_payments"`      // 逾期付款计划
	UpcomingPayments     []PaymentScheduleResponse `json:"upcoming_payments"`     // 即将到期的付款计划
	ContractAlerts       []ContractAlertResponse   `json:"contract_alerts"`       // 合同预警信息
}

// ContractAlertResponse 合同预警响应
type ContractAlertResponse struct {
	ContractID   uuid.UUID `json:"contract_id"`
	ContractCode string    `json:"contract_code"`
	ContractName string    `json:"contract_name"`
	AlertType    string    `json:"alert_type"`    // CONTRACT_EXPIRING, PAYMENT_OVERDUE, PAYMENT_UPCOMING
	AlertMessage string    `json:"alert_message"`
	AlertDate    string    `json:"alert_date"`
	Severity     string    `json:"severity"`      // HIGH, MEDIUM, LOW
}
