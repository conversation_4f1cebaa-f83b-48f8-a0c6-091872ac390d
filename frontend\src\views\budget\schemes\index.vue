<template>
  <div class="budget-schemes-page">
    <a-card :bordered="false">
      <template #title>
        <span>预算方案管理</span>
      </template>
      
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon><PlusOutlined /></template>
            新增方案
          </a-button>
          <a-button @click="handleRefresh">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="schemeList"
        :loading="loading"
        :pagination="false"
        row-key="id"
        size="middle"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'control_rule'">
            <a-tag :color="record.control_rule === 'FLEXIBLE' ? 'blue' : 'orange'">
              {{ record.control_rule === 'FLEXIBLE' ? '灵活控制' : '严格控制' }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)">
                编辑
              </a-button>
              <a-button type="link" size="small" @click="handleCopy(record)">
                复制
              </a-button>
              <a-button 
                type="link" 
                size="small" 
                @click="handleActivate(record)"
                v-if="record.status === 'DRAFT'"
              >
                启用
              </a-button>
              <a-button 
                type="link" 
                size="small" 
                @click="handleClose(record)"
                v-if="record.status === 'ACTIVE'"
              >
                关闭
              </a-button>
              <a-popconfirm
                title="确定要删除这个预算方案吗？"
                @confirm="handleDelete(record)"
                v-if="record.status === 'DRAFT'"
              >
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑预算方案弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :confirm-loading="modalLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="方案名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入方案名称" />
        </a-form-item>
        
        <a-form-item label="预算年度" name="year">
          <a-date-picker
            v-model:value="formData.year"
            picker="year"
            placeholder="请选择预算年度"
            style="width: 100%"
            :disabled-date="disabledDate"
          />
        </a-form-item>

        <a-form-item label="控制规则" name="control_rule">
          <a-radio-group v-model:value="formData.control_rule">
            <a-radio value="FLEXIBLE">灵活控制</a-radio>
            <a-radio value="RIGID">严格控制</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="formData.status">
            <a-radio value="DRAFT">草稿</a-radio>
            <a-radio value="ACTIVE">启用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import dayjs, { type Dayjs } from 'dayjs'
import {
  getBudgetSchemes,
  createBudgetScheme,
  updateBudgetScheme,
  deleteBudgetScheme,
  type BudgetScheme,
  type BudgetSchemeForm
} from '@/api/budget'
import type { Rule } from 'ant-design-vue/es/form'
import type { FormInstance } from 'ant-design-vue'

const loading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const formRef = ref<FormInstance>()
const editingId = ref<string | null>(null)

const schemeList = ref<BudgetScheme[]>([])

const formData = reactive<BudgetSchemeForm & { year: Dayjs | null }>({
  name: '',
  year: null,
  control_rule: 'FLEXIBLE',
  status: 'DRAFT'
})

// 表格列配置
const columns = [
  {
    title: '方案名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '预算年度',
    dataIndex: 'year',
    key: 'year',
    width: 120
  },
  {
    title: '控制规则',
    dataIndex: 'control_rule',
    key: 'control_rule',
    width: 120
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 250,
    fixed: 'right'
  }
]

// 表单验证规则
const rules: Record<string, Rule[]> = {
  name: [
    { required: true, message: '请输入方案名称', trigger: 'blur' }
  ],
  year: [
    { required: true, message: '请选择预算年度', trigger: 'change' }
  ]
}

// 弹窗标题
const modalTitle = computed(() => {
  return editingId.value ? '编辑预算方案' : '新增预算方案'
})

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'DRAFT': 'orange',
    'ACTIVE': 'green',
    'CLOSED': 'red'
  }
  return colorMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'DRAFT': '草稿',
    'ACTIVE': '启用',
    'CLOSED': '关闭'
  }
  return textMap[status] || status
}

// 禁用日期（只能选择当前年度及以后）
const disabledDate = (current: Dayjs) => {
  return current && current.year() < dayjs().year()
}

// 加载预算方案数据
const loadSchemes = async () => {
  loading.value = true
  try {
    const result = await getBudgetSchemes({ page: 1, page_size: 100 })
    schemeList.value = result.list || []
  } catch (error) {
    message.error('加载预算方案数据失败')
  } finally {
    loading.value = false
  }
}

// 新增方案
const handleAdd = () => {
  editingId.value = null
  resetForm()
  modalVisible.value = true
}

// 编辑方案
const handleEdit = (record: BudgetScheme) => {
  editingId.value = record.id
  Object.assign(formData, {
    name: record.name,
    year: dayjs().year(record.year),
    control_rule: record.control_rule,
    status: record.status
  })
  modalVisible.value = true
}

// 复制方案
const handleCopy = (record: BudgetScheme) => {
  editingId.value = null
  Object.assign(formData, {
    name: `${record.name}_副本`,
    year: dayjs().year(record.year + 1),
    control_rule: record.control_rule,
    status: 'DRAFT'
  })
  modalVisible.value = true
}

// 启用方案
const handleActivate = (record: BudgetScheme) => {
  Modal.confirm({
    title: '确认启用',
    content: '启用后该方案将成为当前有效的预算方案，确定要启用吗？',
    onOk: async () => {
      try {
        await updateBudgetScheme(record.id, { ...record, status: 'active' })
        message.success('启用成功')
        loadSchemes()
      } catch (error) {
        message.error('启用失败')
      }
    }
  })
}

// 关闭方案
const handleClose = (record: BudgetScheme) => {
  Modal.confirm({
    title: '确认关闭',
    content: '关闭后该方案将不再可用，确定要关闭吗？',
    onOk: async () => {
      try {
        await updateBudgetScheme(record.id, { ...record, status: 'closed' })
        message.success('关闭成功')
        loadSchemes()
      } catch (error) {
        message.error('关闭失败')
      }
    }
  })
}

// 删除方案
const handleDelete = async (record: BudgetScheme) => {
  try {
    await deleteBudgetScheme(record.id)
    message.success('删除成功')
    loadSchemes()
  } catch (error) {
    message.error('删除失败')
  }
}

// 刷新数据
const handleRefresh = () => {
  loadSchemes()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    modalLoading.value = true
    
    const submitData = {
      name: formData.name,
      year: formData.year?.year() || dayjs().year(),
      description: formData.description,
      status: formData.status
    }
    
    if (editingId.value) {
      await updateBudgetScheme(editingId.value, submitData)
      message.success('更新成功')
    } else {
      await createBudgetScheme(submitData)
      message.success('创建成功')
    }
    
    modalVisible.value = false
    loadSchemes()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    modalLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    year: null,
    control_rule: 'FLEXIBLE',
    status: 'DRAFT'
  })
  formRef.value?.resetFields()
}

onMounted(() => {
  loadSchemes()
})
</script>

<style scoped>
.budget-schemes-page {
  padding: 24px;
}
</style>
