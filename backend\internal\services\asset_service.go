package services

import (
	"errors"
	"fmt"
	"hospital-management/internal/dto"
	"hospital-management/internal/models"
	"hospital-management/pkg/logger"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type AssetService struct {
	db *gorm.DB
}

func NewAssetService(db *gorm.DB) *AssetService {
	return &AssetService{db: db}
}

// ===== 资产分类管理 =====

// GetAssetCategories 获取资产分类列表
func (s *AssetService) GetAssetCategories() ([]dto.AssetCategoryResponse, error) {
	var categories []models.AssetCategory
	if err := s.db.Preload("Parent").Find(&categories).Error; err != nil {
		logger.Error("Failed to get asset categories:", err)
		return nil, err
	}

	// 转换响应
	responses := make([]dto.AssetCategoryResponse, len(categories))
	for i, category := range categories {
		responses[i] = s.convertAssetCategoryToResponse(category)
	}

	return responses, nil
}

// GetAssetCategoryTree 获取资产分类树形结构
func (s *AssetService) GetAssetCategoryTree() ([]dto.AssetCategoryTreeResponse, error) {
	var categories []models.AssetCategory
	if err := s.db.Order("code ASC").Find(&categories).Error; err != nil {
		logger.Error("Failed to get asset categories:", err)
		return nil, err
	}

	// 构建树形结构
	categoryMap := make(map[uuid.UUID]*dto.AssetCategoryTreeResponse)
	var roots []dto.AssetCategoryTreeResponse

	// 第一遍：创建所有节点
	for _, category := range categories {
		node := dto.AssetCategoryTreeResponse{
			ID:       category.ID,
			ParentID: category.ParentID,
			Name:     category.Name,
			Code:     category.Code,
			Children: make([]dto.AssetCategoryTreeResponse, 0),
		}
		categoryMap[category.ID] = &node
	}

	// 第二遍：构建父子关系
	for _, category := range categories {
		node := categoryMap[category.ID]
		if category.ParentID == nil {
			// 根节点
			roots = append(roots, *node)
		} else {
			// 子节点
			if parent, exists := categoryMap[*category.ParentID]; exists {
				parent.Children = append(parent.Children, *node)
			}
		}
	}

	return roots, nil
}

// CreateAssetCategory 创建资产分类
func (s *AssetService) CreateAssetCategory(req *dto.CreateAssetCategoryRequest) (*dto.AssetCategoryResponse, error) {
	// 检查分类编码是否重复
	var count int64
	if err := s.db.Model(&models.AssetCategory{}).Where("code = ?", req.Code).Count(&count).Error; err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.New("分类编码已存在")
	}

	// 验证父分类是否存在
	if req.ParentID != nil {
		var parent models.AssetCategory
		if err := s.db.First(&parent, *req.ParentID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errors.New("父分类不存在")
			}
			return nil, err
		}
	}

	category := models.AssetCategory{
		ParentID: req.ParentID,
		Name:     req.Name,
		Code:     req.Code,
	}

	if err := s.db.Create(&category).Error; err != nil {
		logger.Error("Failed to create asset category:", err)
		return nil, err
	}

	// 预加载关联数据
	if err := s.db.Preload("Parent").First(&category, category.ID).Error; err != nil {
		logger.Error("Failed to preload asset category data:", err)
		return nil, err
	}

	response := s.convertAssetCategoryToResponse(category)
	return &response, nil
}

// UpdateAssetCategory 更新资产分类
func (s *AssetService) UpdateAssetCategory(id uuid.UUID, req *dto.UpdateAssetCategoryRequest) (*dto.AssetCategoryResponse, error) {
	var category models.AssetCategory
	if err := s.db.First(&category, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("资产分类不存在")
		}
		return nil, err
	}

	// 检查分类编码是否重复
	if req.Code != nil {
		var count int64
		if err := s.db.Model(&models.AssetCategory{}).Where("code = ? AND id != ?", *req.Code, id).Count(&count).Error; err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, errors.New("分类编码已存在")
		}
		category.Code = *req.Code
	}

	// 验证父分类是否存在
	if req.ParentID != nil {
		if *req.ParentID == id {
			return nil, errors.New("不能将自己设为父分类")
		}
		var parent models.AssetCategory
		if err := s.db.First(&parent, *req.ParentID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errors.New("父分类不存在")
			}
			return nil, err
		}
		category.ParentID = req.ParentID
	}

	// 更新其他字段
	if req.Name != nil {
		category.Name = *req.Name
	}

	if err := s.db.Save(&category).Error; err != nil {
		logger.Error("Failed to update asset category:", err)
		return nil, err
	}

	// 预加载关联数据
	if err := s.db.Preload("Parent").First(&category, category.ID).Error; err != nil {
		logger.Error("Failed to preload asset category data:", err)
		return nil, err
	}

	response := s.convertAssetCategoryToResponse(category)
	return &response, nil
}

// DeleteAssetCategory 删除资产分类
func (s *AssetService) DeleteAssetCategory(id uuid.UUID) error {
	var category models.AssetCategory
	if err := s.db.First(&category, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("资产分类不存在")
		}
		return err
	}

	// 检查是否有子分类
	var childCount int64
	if err := s.db.Model(&models.AssetCategory{}).Where("parent_id = ?", id).Count(&childCount).Error; err != nil {
		return err
	}
	if childCount > 0 {
		return errors.New("存在子分类，不能删除")
	}

	// 检查是否有关联的资产
	var assetCount int64
	if err := s.db.Model(&models.Asset{}).Where("category_id = ?", id).Count(&assetCount).Error; err != nil {
		return err
	}
	if assetCount > 0 {
		return errors.New("存在关联的资产，不能删除")
	}

	if err := s.db.Delete(&category).Error; err != nil {
		logger.Error("Failed to delete asset category:", err)
		return err
	}

	return nil
}

// ===== 资产管理 =====

// GetAssets 获取资产列表
func (s *AssetService) GetAssets(req *dto.AssetListRequest) ([]dto.AssetResponse, int64, error) {
	var assets []models.Asset
	var total int64

	query := s.db.Model(&models.Asset{}).
		Preload("Category").
		Preload("PurchaseContract").
		Preload("OwnerDept").
		Preload("Custodian")

	// 搜索条件
	if req.Keyword != "" {
		query = query.Where("asset_name ILIKE ? OR asset_code ILIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}
	if req.CategoryID != nil {
		query = query.Where("category_id = ?", *req.CategoryID)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.OwnerDeptID != nil {
		query = query.Where("owner_dept_id = ?", *req.OwnerDeptID)
	}
	if req.CustodianID != nil {
		query = query.Where("custodian_id = ?", *req.CustodianID)
	}
	if req.PurchaseDateFrom != nil {
		query = query.Where("purchase_date >= ?", *req.PurchaseDateFrom)
	}
	if req.PurchaseDateTo != nil {
		query = query.Where("purchase_date <= ?", *req.PurchaseDateTo)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count assets:", err)
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&assets).Error; err != nil {
		logger.Error("Failed to get assets:", err)
		return nil, 0, err
	}

	// 转换响应
	responses := make([]dto.AssetResponse, len(assets))
	for i, asset := range assets {
		responses[i] = s.convertAssetToResponse(asset)
	}

	return responses, total, nil
}

// GetAsset 获取资产详情
func (s *AssetService) GetAsset(id uuid.UUID) (*dto.AssetResponse, error) {
	var asset models.Asset
	if err := s.db.Preload("Category").
		Preload("PurchaseContract").
		Preload("OwnerDept").
		Preload("Custodian").
		First(&asset, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("资产不存在")
		}
		return nil, err
	}

	response := s.convertAssetToResponse(asset)
	return &response, nil
}

// CreateAsset 创建资产
func (s *AssetService) CreateAsset(req *dto.CreateAssetRequest, userID uuid.UUID) (*dto.AssetResponse, error) {
	// 验证资产分类是否存在
	var category models.AssetCategory
	if err := s.db.First(&category, req.CategoryID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("资产分类不存在")
		}
		return nil, err
	}

	// 验证使用部门是否存在
	var dept models.Department
	if err := s.db.First(&dept, req.OwnerDeptID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("使用部门不存在")
		}
		return nil, err
	}

	// 验证保管人是否存在
	if req.CustodianID != nil {
		var custodian models.User
		if err := s.db.First(&custodian, *req.CustodianID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errors.New("保管人不存在")
			}
			return nil, err
		}
	}

	// 验证采购合同是否存在
	if req.PurchaseContractID != nil {
		var contract models.Contract
		if err := s.db.First(&contract, *req.PurchaseContractID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errors.New("采购合同不存在")
			}
			return nil, err
		}
	}

	// 生成资产编码
	assetCode, err := s.generateAssetCode()
	if err != nil {
		return nil, err
	}

	// 解析采购日期
	var purchaseDate *time.Time
	if req.PurchaseDate != nil {
		if parsed, err := time.Parse("2006-01-02", *req.PurchaseDate); err != nil {
			return nil, errors.New("采购日期格式错误")
		} else {
			purchaseDate = &parsed
		}
	}

	asset := models.Asset{
		AssetCode:          assetCode,
		AssetName:          req.AssetName,
		CategoryID:         req.CategoryID,
		SourceType:         req.SourceType,
		PurchaseContractID: req.PurchaseContractID,
		PurchaseDate:       purchaseDate,
		PurchasePrice:      req.PurchasePrice,
		Status:             req.Status,
		OwnerDeptID:        req.OwnerDeptID,
		CustodianID:        req.CustodianID,
		Location:           req.Location,
	}
	asset.CreatedBy = &userID

	if err := s.db.Create(&asset).Error; err != nil {
		logger.Error("Failed to create asset:", err)
		return nil, err
	}

	// 预加载关联数据
	if err := s.db.Preload("Category").
		Preload("PurchaseContract").
		Preload("OwnerDept").
		Preload("Custodian").
		First(&asset, asset.ID).Error; err != nil {
		logger.Error("Failed to preload asset data:", err)
		return nil, err
	}

	response := s.convertAssetToResponse(asset)
	return &response, nil
}

// ===== 辅助方法 =====

// generateAssetCode 生成资产编码
func (s *AssetService) generateAssetCode() (string, error) {
	now := time.Now()
	prefix := fmt.Sprintf("ZC%04d%02d%02d", now.Year(), now.Month(), now.Day())
	
	var count int64
	if err := s.db.Model(&models.Asset{}).
		Where("asset_code LIKE ?", prefix+"%").
		Count(&count).Error; err != nil {
		return "", err
	}
	
	return fmt.Sprintf("%s%04d", prefix, count+1), nil
}

// convertAssetCategoryToResponse 转换资产分类为响应格式
func (s *AssetService) convertAssetCategoryToResponse(category models.AssetCategory) dto.AssetCategoryResponse {
	response := dto.AssetCategoryResponse{
		ID:        category.ID,
		ParentID:  category.ParentID,
		Name:      category.Name,
		Code:      category.Code,
		CreatedAt: category.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt: category.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	if category.Parent != nil {
		parent := s.convertAssetCategoryToResponse(*category.Parent)
		response.Parent = &parent
	}

	return response
}

// convertAssetToResponse 转换资产为响应格式
func (s *AssetService) convertAssetToResponse(asset models.Asset) dto.AssetResponse {
	response := dto.AssetResponse{
		ID:                 asset.ID,
		AssetCode:          asset.AssetCode,
		AssetName:          asset.AssetName,
		CategoryID:         asset.CategoryID,
		SourceType:         asset.SourceType,
		PurchaseContractID: asset.PurchaseContractID,
		PurchasePrice:      asset.PurchasePrice,
		Status:             asset.Status,
		OwnerDeptID:        asset.OwnerDeptID,
		CustodianID:        asset.CustodianID,
		Location:           asset.Location,
		CreatedAt:          asset.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:          asset.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	// 处理采购日期
	if asset.PurchaseDate != nil {
		purchaseDateStr := asset.PurchaseDate.Format("2006-01-02")
		response.PurchaseDate = &purchaseDateStr
	}

	// 关联数据
	if asset.Category != nil {
		response.Category = &dto.AssetCategoryResponse{
			ID:   asset.Category.ID,
			Name: asset.Category.Name,
			Code: asset.Category.Code,
		}
	}

	if asset.PurchaseContract != nil {
		response.PurchaseContract = &dto.ContractSimpleResponse{
			ID:           asset.PurchaseContract.ID,
			ContractCode: asset.PurchaseContract.ContractCode,
			ContractName: asset.PurchaseContract.ContractName,
			ContractType: asset.PurchaseContract.ContractType,
			TotalAmount:  asset.PurchaseContract.TotalAmount,
			Status:       asset.PurchaseContract.Status,
		}
	}

	if asset.OwnerDept != nil {
		response.OwnerDept = &dto.DepartmentSimpleResponse{
			ID:       asset.OwnerDept.ID,
			DeptName: asset.OwnerDept.Name,
			DeptCode: asset.OwnerDept.Code,
		}
	}

	if asset.Custodian != nil {
		response.Custodian = &dto.UserSimpleResponse{
			ID:       asset.Custodian.ID,
			UserName: asset.Custodian.UserName,
			JobTitle: asset.Custodian.JobTitle,
		}
	}

	return response
}

// UpdateAsset 更新资产
func (s *AssetService) UpdateAsset(id uuid.UUID, req *dto.UpdateAssetRequest, userID uuid.UUID) (*dto.AssetResponse, error) {
	var asset models.Asset
	if err := s.db.First(&asset, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("资产不存在")
		}
		return nil, err
	}

	// 验证资产分类是否存在
	if req.CategoryID != nil {
		var category models.AssetCategory
		if err := s.db.First(&category, *req.CategoryID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errors.New("资产分类不存在")
			}
			return nil, err
		}
		asset.CategoryID = *req.CategoryID
	}

	// 验证使用部门是否存在
	if req.OwnerDeptID != nil {
		var dept models.Department
		if err := s.db.First(&dept, *req.OwnerDeptID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errors.New("使用部门不存在")
			}
			return nil, err
		}
		asset.OwnerDeptID = *req.OwnerDeptID
	}

	// 验证保管人是否存在
	if req.CustodianID != nil {
		var custodian models.User
		if err := s.db.First(&custodian, *req.CustodianID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errors.New("保管人不存在")
			}
			return nil, err
		}
		asset.CustodianID = req.CustodianID
	}

	// 验证采购合同是否存在
	if req.PurchaseContractID != nil {
		var contract models.Contract
		if err := s.db.First(&contract, *req.PurchaseContractID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errors.New("采购合同不存在")
			}
			return nil, err
		}
		asset.PurchaseContractID = req.PurchaseContractID
	}

	// 更新其他字段
	if req.AssetName != nil {
		asset.AssetName = *req.AssetName
	}
	if req.SourceType != nil {
		asset.SourceType = req.SourceType
	}
	if req.PurchaseDate != nil {
		if parsed, err := time.Parse("2006-01-02", *req.PurchaseDate); err != nil {
			return nil, errors.New("采购日期格式错误")
		} else {
			asset.PurchaseDate = &parsed
		}
	}
	if req.PurchasePrice != nil {
		asset.PurchasePrice = req.PurchasePrice
	}
	if req.Status != nil {
		asset.Status = *req.Status
	}
	if req.Location != nil {
		asset.Location = req.Location
	}

	asset.UpdatedBy = &userID

	if err := s.db.Save(&asset).Error; err != nil {
		logger.Error("Failed to update asset:", err)
		return nil, err
	}

	// 预加载关联数据
	if err := s.db.Preload("Category").
		Preload("PurchaseContract").
		Preload("OwnerDept").
		Preload("Custodian").
		First(&asset, asset.ID).Error; err != nil {
		logger.Error("Failed to preload asset data:", err)
		return nil, err
	}

	response := s.convertAssetToResponse(asset)
	return &response, nil
}

// UpdateAssetStatus 更新资产状态
func (s *AssetService) UpdateAssetStatus(id uuid.UUID, req *dto.UpdateAssetStatusRequest, userID uuid.UUID) (*dto.AssetResponse, error) {
	var asset models.Asset
	if err := s.db.First(&asset, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("资产不存在")
		}
		return nil, err
	}

	asset.Status = req.Status
	asset.UpdatedBy = &userID

	if err := s.db.Save(&asset).Error; err != nil {
		logger.Error("Failed to update asset status:", err)
		return nil, err
	}

	// 预加载关联数据
	if err := s.db.Preload("Category").
		Preload("PurchaseContract").
		Preload("OwnerDept").
		Preload("Custodian").
		First(&asset, asset.ID).Error; err != nil {
		logger.Error("Failed to preload asset data:", err)
		return nil, err
	}

	response := s.convertAssetToResponse(asset)
	return &response, nil
}

// BatchUpdateAssetStatus 批量更新资产状态
func (s *AssetService) BatchUpdateAssetStatus(req *dto.BatchUpdateAssetStatusRequest, userID uuid.UUID) error {
	// 开启事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, assetID := range req.AssetIDs {
		var asset models.Asset
		if err := tx.First(&asset, assetID).Error; err != nil {
			tx.Rollback()
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return errors.New("资产不存在")
			}
			return err
		}

		asset.Status = req.Status
		asset.UpdatedBy = &userID

		if err := tx.Save(&asset).Error; err != nil {
			tx.Rollback()
			logger.Error("Failed to update asset status:", err)
			return err
		}
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

// DeleteAsset 删除资产
func (s *AssetService) DeleteAsset(id uuid.UUID) error {
	var asset models.Asset
	if err := s.db.First(&asset, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("资产不存在")
		}
		return err
	}

	// 检查资产状态，只有报废状态的资产才能删除
	if asset.Status != "SCRAPPED" {
		return errors.New("只能删除已报废的资产")
	}

	if err := s.db.Delete(&asset).Error; err != nil {
		logger.Error("Failed to delete asset:", err)
		return err
	}

	return nil
}
