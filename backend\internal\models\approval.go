package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ApprovalFlow 审批流实例表 - 对应 tbl_approval_flows
type ApprovalFlow struct {
	BaseModel
	BusinessID    uuid.UUID  `json:"business_id" gorm:"type:uuid;not null" validate:"required"`
	BusinessType  string     `json:"business_type" gorm:"type:varchar(50);not null" validate:"required,max=50"`
	Title         *string    `json:"title,omitempty" gorm:"type:varchar(200)"`
	Content       *string    `json:"content,omitempty" gorm:"type:text"`
	Status        string     `json:"status" gorm:"type:varchar(30);not null" validate:"required,max=30"`
	CurrentStep   *int       `json:"current_step,omitempty" gorm:"default:0"`
	CurrentNodeID *uuid.UUID `json:"current_node_id,omitempty" gorm:"type:uuid"`

	// 关联关系
	Nodes   []ApprovalNode `json:"nodes,omitempty" gorm:"foreignKey:FlowID"`
	Creator *User          `json:"creator,omitempty" gorm:"foreignKey:CreatedBy"`
}

// TableName 指定表名
func (ApprovalFlow) TableName() string {
	return "tbl_approval_flows"
}

// ApprovalNode 审批节点记录表 - 对应 tbl_approval_nodes
type ApprovalNode struct {
	BaseModel
	FlowID       uuid.UUID  `json:"flow_id" gorm:"type:uuid;not null" validate:"required"`
	ApproverID   uuid.UUID  `json:"approver_id" gorm:"type:uuid;not null" validate:"required"`
	NodeOrder    *int       `json:"node_order,omitempty"`
	NodeName     *string    `json:"node_name,omitempty" gorm:"type:varchar(100)"`
	ApproverType *string    `json:"approver_type,omitempty" gorm:"type:varchar(20)"`
	Status       string     `json:"status" gorm:"type:varchar(30);not null" validate:"required,max=30"`
	IsRequired   *bool      `json:"is_required,omitempty" gorm:"default:true"`
	Comment      *string    `json:"comment,omitempty" gorm:"type:text"`
	Comments     *string    `json:"comments,omitempty" gorm:"type:text"` // 别名字段
	Description  *string    `json:"description,omitempty" gorm:"type:text"`
	Step         *int       `json:"step,omitempty"`
	ProcessedAt  *time.Time `json:"processed_at,omitempty" gorm:"type:timestamptz"`
	ApprovedAt   *time.Time `json:"approved_at,omitempty" gorm:"type:timestamptz"` // 别名字段
	ApprovedBy   *uuid.UUID `json:"approved_by,omitempty" gorm:"type:uuid"`        // 审批人ID

	// 关联关系
	Flow     *ApprovalFlow `json:"flow,omitempty" gorm:"foreignKey:FlowID"`
	Approver *User         `json:"approver,omitempty" gorm:"foreignKey:ApproverID"`
}

// TableName 指定表名
func (ApprovalNode) TableName() string {
	return "tbl_approval_nodes"
}

// AfterFind GORM钩子，用于填充虚拟字段
func (a *ApprovalNode) AfterFind(tx *gorm.DB) error {
	a.Comments = a.Comment
	a.ApprovedAt = a.ProcessedAt
	return nil
}

// BeforeSave GORM钩子，用于同步虚拟字段
func (a *ApprovalNode) BeforeSave(tx *gorm.DB) error {
	if a.Comments != nil {
		a.Comment = a.Comments
	}
	if a.ApprovedAt != nil {
		a.ProcessedAt = a.ApprovedAt
	}
	return nil
}
