package dto

import (
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// ===== 事前申请相关 =====

// CreatePreApplicationRequest 创建事前申请请求
type CreatePreApplicationRequest struct {
	Type            string                 `json:"type" validate:"required,oneof=TRAVEL MEETING TRAINING"`
	Title           string                 `json:"title" validate:"required,max=255"`
	EstimatedAmount decimal.Decimal        `json:"estimated_amount" validate:"required,gte=0"`
	Details         map[string]interface{} `json:"details,omitempty"`
}

// UpdatePreApplicationRequest 更新事前申请请求
type UpdatePreApplicationRequest struct {
	Type            *string                `json:"type,omitempty" validate:"omitempty,oneof=TRAVEL MEETING TRAINING"`
	Title           *string                `json:"title,omitempty" validate:"omitempty,max=255"`
	EstimatedAmount *decimal.Decimal       `json:"estimated_amount,omitempty" validate:"omitempty,gte=0"`
	Details         map[string]interface{} `json:"details,omitempty"`
}

// PreApplicationResponse 事前申请响应
type PreApplicationResponse struct {
	ID              uuid.UUID                 `json:"id"`
	ApplicationCode string                    `json:"application_code"`
	ApplicantID     uuid.UUID                 `json:"applicant_id"`
	DepartmentID    uuid.UUID                 `json:"department_id"`
	Type            string                    `json:"type"`
	Title           string                    `json:"title"`
	EstimatedAmount decimal.Decimal           `json:"estimated_amount"`
	Status          string                    `json:"status"`
	Details         map[string]interface{}    `json:"details,omitempty"`
	CreatedAt       string                    `json:"created_at"`
	UpdatedAt       string                    `json:"updated_at"`
	Applicant       *UserSimpleResponse       `json:"applicant,omitempty"`
	Department      *DepartmentSimpleResponse `json:"department,omitempty"`
}

// PreApplicationListRequest 事前申请列表请求
type PreApplicationListRequest struct {
	Page         int        `form:"page" validate:"min=1"`
	PageSize     int        `form:"page_size" validate:"min=1,max=100"`
	Keyword      string     `form:"keyword"`
	Type         string     `form:"type" validate:"omitempty,oneof=TRAVEL MEETING TRAINING"`
	Status       string     `form:"status" validate:"omitempty,oneof=DRAFT PENDING APPROVED REJECTED CLOSED"`
	ApplicantID  *uuid.UUID `form:"applicant_id" validate:"omitempty,uuid"`
	DepartmentID *uuid.UUID `form:"department_id" validate:"omitempty,uuid"`
}

// ===== 费用报销相关 =====

// ExpenseDetailRequest 报销明细请求
type ExpenseDetailRequest struct {
	BudgetItemID uuid.UUID              `json:"budget_item_id" validate:"required,uuid"`
	Description  string                 `json:"description" validate:"required"`
	Amount       decimal.Decimal        `json:"amount" validate:"required,gt=0"`
	ExpenseDate  string                 `json:"expense_date" validate:"required,datetime=2006-01-02"`
	InvoiceInfo  map[string]interface{} `json:"invoice_info,omitempty"`
}

// CreateExpenseApplicationRequest 创建报销申请请求
type CreateExpenseApplicationRequest struct {
	PreApplicationID *uuid.UUID             `json:"pre_application_id,omitempty" validate:"omitempty,uuid"`
	Title            string                 `json:"title" validate:"required,max=255"`
	Details          []ExpenseDetailRequest `json:"details" validate:"required,dive"`
}

// UpdateExpenseApplicationRequest 更新报销申请请求
type UpdateExpenseApplicationRequest struct {
	PreApplicationID *uuid.UUID             `json:"pre_application_id,omitempty" validate:"omitempty,uuid"`
	Title            *string                `json:"title,omitempty" validate:"omitempty,max=255"`
	Details          []ExpenseDetailRequest `json:"details,omitempty" validate:"omitempty,dive"`
}

// ExpenseDetailResponse 报销明细响应
type ExpenseDetailResponse struct {
	ID           uuid.UUID                 `json:"id"`
	BudgetItemID uuid.UUID                 `json:"budget_item_id"`
	Description  string                    `json:"description"`
	Amount       decimal.Decimal           `json:"amount"`
	ExpenseDate  string                    `json:"expense_date"`
	InvoiceInfo  map[string]interface{}    `json:"invoice_info,omitempty"`
	BudgetItem   *BudgetItemSimpleResponse `json:"budget_item,omitempty"`
}

// ExpenseApplicationResponse 报销申请响应
type ExpenseApplicationResponse struct {
	ID               uuid.UUID                 `json:"id"`
	ApplicationCode  string                    `json:"application_code"`
	ApplicantID      uuid.UUID                 `json:"applicant_id"`
	DepartmentID     uuid.UUID                 `json:"department_id"`
	PreApplicationID *uuid.UUID                `json:"pre_application_id,omitempty"`
	Title            string                    `json:"title"`
	TotalAmount      decimal.Decimal           `json:"total_amount"`
	Status           string                    `json:"status"`
	CreatedAt        string                    `json:"created_at"`
	UpdatedAt        string                    `json:"updated_at"`
	Applicant        *UserSimpleResponse       `json:"applicant,omitempty"`
	Department       *DepartmentSimpleResponse `json:"department,omitempty"`
	PreApplication   *PreApplicationResponse   `json:"pre_application,omitempty"`
	Details          []ExpenseDetailResponse   `json:"details,omitempty"`
}

// ExpenseApplicationListRequest 报销申请列表请求
type ExpenseApplicationListRequest struct {
	Page             int        `form:"page" validate:"min=1"`
	PageSize         int        `form:"page_size" validate:"min=1,max=100"`
	Keyword          string     `form:"keyword"`
	Status           string     `form:"status" validate:"omitempty,oneof=DRAFT PENDING APPROVED REJECTED PAID"`
	ApplicantID      *uuid.UUID `form:"applicant_id" validate:"omitempty,uuid"`
	DepartmentID     *uuid.UUID `form:"department_id" validate:"omitempty,uuid"`
	PreApplicationID *uuid.UUID `form:"pre_application_id" validate:"omitempty,uuid"`
}

// ===== 付款管理相关 =====

// CreatePaymentRequest 创建付款单请求
type CreatePaymentRequest struct {
	SourceID     uuid.UUID       `json:"source_id" validate:"required,uuid"`
	SourceType   string          `json:"source_type" validate:"required,oneof=EXPENSE CONTRACT_PAY"`
	PayeeName    string          `json:"payee_name" validate:"required,max=255"`
	PayeeAccount string          `json:"payee_account" validate:"required,max=100"`
	PayeeBank    string          `json:"payee_bank" validate:"required,max=255"`
	Amount       decimal.Decimal `json:"amount" validate:"required,gt=0"`
}

// UpdatePaymentStatusRequest 更新付款状态请求
type UpdatePaymentStatusRequest struct {
	Status        string  `json:"status" validate:"required,oneof=PENDING PAID FAILED"`
	TransactionID *string `json:"transaction_id,omitempty" validate:"omitempty,max=255"`
}

// PaymentResponse 付款单响应
type PaymentResponse struct {
	ID            uuid.UUID       `json:"id"`
	PaymentCode   string          `json:"payment_code"`
	SourceID      uuid.UUID       `json:"source_id"`
	SourceType    string          `json:"source_type"`
	PayeeName     string          `json:"payee_name"`
	PayeeAccount  string          `json:"payee_account"`
	PayeeBank     string          `json:"payee_bank"`
	Amount        decimal.Decimal `json:"amount"`
	Status        string          `json:"status"`
	PaidAt        *string         `json:"paid_at,omitempty"`
	TransactionID *string         `json:"transaction_id,omitempty"`
	CreatedAt     string          `json:"created_at"`
	UpdatedAt     string          `json:"updated_at"`
}

// PaymentListRequest 付款单列表请求
type PaymentListRequest struct {
	Page       int        `form:"page" validate:"min=1"`
	PageSize   int        `form:"page_size" validate:"min=1,max=100"`
	Keyword    string     `form:"keyword"`
	Status     string     `form:"status" validate:"omitempty,oneof=PENDING PAID FAILED"`
	SourceType string     `form:"source_type" validate:"omitempty,oneof=EXPENSE CONTRACT_PAY"`
	SourceID   *uuid.UUID `form:"source_id" validate:"omitempty,uuid"`
}

// BatchPaymentRequest 批量付款请求
type BatchPaymentRequest struct {
	PaymentIDs []uuid.UUID `json:"payment_ids" validate:"required,dive,uuid"`
}

// ===== 辅助响应结构 =====

// BudgetItemSimpleResponse 预算明细简单响应（用于关联显示）
type BudgetItemSimpleResponse struct {
	ID           uuid.UUID                    `json:"id"`
	TotalAmount  decimal.Decimal              `json:"total_amount"`
	UsedAmount   decimal.Decimal              `json:"used_amount"`
	FrozenAmount decimal.Decimal              `json:"frozen_amount"`
	Subject      *BudgetSubjectSimpleResponse `json:"subject,omitempty"`
}

// BudgetSubjectSimpleResponse 预算科目简单响应（用于关联显示）
type BudgetSubjectSimpleResponse struct {
	ID   uuid.UUID `json:"id"`
	Name string    `json:"name"`
	Code string    `json:"code"`
	Type string    `json:"type"`
}
