package services

import (
	"errors"
	"fmt"
	"hospital-management/internal/dto"
	"hospital-management/internal/models"
	"hospital-management/pkg/logger"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type FileService struct {
	db          *gorm.DB
	uploadPath  string
	baseURL     string
	maxFileSize int64
}

func NewFileService(db *gorm.DB, uploadPath, baseURL string, maxFileSize int64) *FileService {
	return &FileService{
		db:          db,
		uploadPath:  uploadPath,
		baseURL:     baseURL,
		maxFileSize: maxFileSize,
	}
}

// UploadFile 上传文件
func (s *FileService) UploadFile(fileHeader *multipart.FileHeader, userID uuid.UUID, businessType *string, businessID *uuid.UUID) (*dto.FileUploadResponse, error) {
	// 检查文件大小
	if fileHeader.Size > s.maxFileSize {
		return nil, errors.New("文件大小超过限制")
	}

	// 检查文件类型
	if !s.isAllowedFileType(fileHeader.Filename) {
		return nil, errors.New("不支持的文件类型")
	}

	// 生成文件名
	fileID := uuid.New()
	ext := filepath.Ext(fileHeader.Filename)
	fileName := fmt.Sprintf("%s%s", fileID.String(), ext)

	// 创建上传目录
	uploadDir := filepath.Join(s.uploadPath, time.Now().Format("2006/01/02"))
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		logger.Error("Failed to create upload directory:", err)
		return nil, errors.New("创建上传目录失败")
	}

	// 保存文件
	filePath := filepath.Join(uploadDir, fileName)
	src, err := fileHeader.Open()
	if err != nil {
		logger.Error("Failed to open uploaded file:", err)
		return nil, errors.New("打开上传文件失败")
	}
	defer src.Close()

	dst, err := os.Create(filePath)
	if err != nil {
		logger.Error("Failed to create file:", err)
		return nil, errors.New("创建文件失败")
	}
	defer dst.Close()

	if _, err := io.Copy(dst, src); err != nil {
		logger.Error("Failed to save file:", err)
		return nil, errors.New("保存文件失败")
	}

	// 保存文件记录到数据库
	file := models.File{
		FileName:     fileName,
		OriginalName: fileHeader.Filename,
		FileSize:     &fileHeader.Size,
		FileType:     StringPtr(s.getFileType(fileHeader.Filename)),
		FilePath:     filePath,
		BusinessType: businessType,
		BusinessID:   businessID,
		IsPublic:     BoolPtr(false),
	}
	file.ID = fileID
	file.CreatedBy = &userID

	if err := s.db.Create(&file).Error; err != nil {
		// 删除已上传的文件
		os.Remove(filePath)
		logger.Error("Failed to save file record:", err)
		return nil, errors.New("保存文件记录失败")
	}

	// 构建文件URL
	relativePath := strings.Replace(filePath, s.uploadPath, "", 1)
	relativePath = strings.Replace(relativePath, "\\", "/", -1)
	fileURL := fmt.Sprintf("%s/files%s", s.baseURL, relativePath)

	return &dto.FileUploadResponse{
		ID:           file.ID,
		FileName:     file.FileName,
		OriginalName: file.OriginalName,
		FileSize:     *file.FileSize,
		FileType:     StringValue(file.FileType),
		FilePath:     file.FilePath,
		FileURL:      fileURL,
		CreatedAt:    file.CreatedAt.Format(time.RFC3339),
		CreatedBy:    *file.CreatedBy,
	}, nil
}

// GetFiles 获取文件列表
func (s *FileService) GetFiles(req *dto.FileListRequest) ([]dto.FileResponse, int64, error) {
	var files []models.File
	var total int64

	query := s.db.Model(&models.File{}).Preload("Uploader")

	// 搜索条件
	if req.BusinessType != "" {
		query = query.Where("business_type = ?", req.BusinessType)
	}
	if req.BusinessID != nil {
		query = query.Where("business_id = ?", *req.BusinessID)
	}
	if req.FileType != "" {
		query = query.Where("file_type = ?", req.FileType)
	}
	if req.IsPublic != nil {
		query = query.Where("is_public = ?", *req.IsPublic)
	}
	if req.UploaderID != nil {
		query = query.Where("created_by = ?", *req.UploaderID)
	}
	if req.Keyword != "" {
		query = query.Where("file_name ILIKE ? OR original_name ILIKE ?",
			"%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}
	if req.StartDate != "" {
		query = query.Where("created_at >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		query = query.Where("created_at <= ?", req.EndDate+" 23:59:59")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count files:", err)
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).
		Order("created_at DESC").Find(&files).Error; err != nil {
		logger.Error("Failed to get files:", err)
		return nil, 0, err
	}

	// 转换为响应格式
	responses := make([]dto.FileResponse, len(files))
	for i, file := range files {
		responses[i] = s.convertToResponse(file)
	}

	return responses, total, nil
}

// GetFileByID 根据ID获取文件
func (s *FileService) GetFileByID(id uuid.UUID) (*dto.FileResponse, error) {
	var file models.File
	if err := s.db.Preload("Uploader").First(&file, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("文件不存在")
		}
		logger.Error("Failed to get file:", err)
		return nil, err
	}

	response := s.convertToResponse(file)
	return &response, nil
}

// GetBusinessFiles 获取业务关联文件
func (s *FileService) GetBusinessFiles(businessType string, businessID uuid.UUID) ([]dto.FileResponse, error) {
	var files []models.File
	if err := s.db.Preload("Uploader").
		Where("business_type = ? AND business_id = ?", businessType, businessID).
		Order("created_at DESC").Find(&files).Error; err != nil {
		logger.Error("Failed to get business files:", err)
		return nil, err
	}

	// 转换为响应格式
	responses := make([]dto.FileResponse, len(files))
	for i, file := range files {
		responses[i] = s.convertToResponse(file)
	}

	return responses, nil
}

// DownloadFile 下载文件
func (s *FileService) DownloadFile(id uuid.UUID, userID uuid.UUID) (string, string, error) {
	var file models.File
	if err := s.db.First(&file, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", "", errors.New("文件不存在")
		}
		return "", "", err
	}

	// 检查文件是否存在
	if _, err := os.Stat(file.FilePath); os.IsNotExist(err) {
		return "", "", errors.New("文件不存在")
	}

	// 更新下载次数
	s.db.Model(&file).UpdateColumn("download_count", gorm.Expr("download_count + 1"))

	return file.FilePath, file.OriginalName, nil
}

// UpdateFile 更新文件信息
func (s *FileService) UpdateFile(id uuid.UUID, req *dto.UpdateFileRequest, userID uuid.UUID) (*dto.FileResponse, error) {
	var file models.File
	if err := s.db.First(&file, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("文件不存在")
		}
		return nil, err
	}

	// 检查权限（只有上传者可以修改）
	if file.CreatedBy == nil || *file.CreatedBy != userID {
		return nil, errors.New("无权限修改该文件")
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.FileName != nil {
		updates["file_name"] = *req.FileName
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.IsPublic != nil {
		updates["is_public"] = *req.IsPublic
	}
	if req.BusinessType != nil {
		updates["business_type"] = *req.BusinessType
	}
	if req.BusinessID != nil {
		updates["business_id"] = *req.BusinessID
	}
	updates["updated_by"] = userID

	if err := s.db.Model(&file).Updates(updates).Error; err != nil {
		logger.Error("Failed to update file:", err)
		return nil, err
	}

	// 重新获取文件信息
	if err := s.db.Preload("Uploader").First(&file, id).Error; err != nil {
		return nil, err
	}

	response := s.convertToResponse(file)
	return &response, nil
}

// DeleteFile 删除文件
func (s *FileService) DeleteFile(id uuid.UUID, userID uuid.UUID) error {
	var file models.File
	if err := s.db.First(&file, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("文件不存在")
		}
		return err
	}

	// 检查权限（只有上传者可以删除）
	if file.CreatedBy == nil || *file.CreatedBy != userID {
		return errors.New("无权限删除该文件")
	}

	// 删除物理文件
	if err := os.Remove(file.FilePath); err != nil {
		logger.Error("Failed to delete physical file:", err)
		// 继续删除数据库记录，即使物理文件删除失败
	}

	// 删除数据库记录
	if err := s.db.Delete(&file).Error; err != nil {
		logger.Error("Failed to delete file record:", err)
		return err
	}

	return nil
}

// isAllowedFileType 检查文件类型是否允许
func (s *FileService) isAllowedFileType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	allowedTypes := []string{
		".jpg", ".jpeg", ".png", ".gif", ".bmp",
		".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
		".txt", ".csv", ".zip", ".rar", ".7z",
	}

	for _, allowedType := range allowedTypes {
		if ext == allowedType {
			return true
		}
	}
	return false
}

// getFileType 获取文件类型
func (s *FileService) getFileType(filename string) string {
	ext := strings.ToLower(filepath.Ext(filename))

	imageTypes := []string{".jpg", ".jpeg", ".png", ".gif", ".bmp"}
	documentTypes := []string{".pdf", ".doc", ".docx", ".txt"}
	spreadsheetTypes := []string{".xls", ".xlsx", ".csv"}
	presentationTypes := []string{".ppt", ".pptx"}
	archiveTypes := []string{".zip", ".rar", ".7z"}

	for _, t := range imageTypes {
		if ext == t {
			return "image"
		}
	}
	for _, t := range documentTypes {
		if ext == t {
			return "document"
		}
	}
	for _, t := range spreadsheetTypes {
		if ext == t {
			return "spreadsheet"
		}
	}
	for _, t := range presentationTypes {
		if ext == t {
			return "presentation"
		}
	}
	for _, t := range archiveTypes {
		if ext == t {
			return "archive"
		}
	}

	return "other"
}

// convertToResponse 转换为响应格式
func (s *FileService) convertToResponse(file models.File) dto.FileResponse {
	// 构建文件URL
	relativePath := strings.Replace(file.FilePath, s.uploadPath, "", 1)
	relativePath = strings.Replace(relativePath, "\\", "/", -1)
	fileURL := fmt.Sprintf("%s/files%s", s.baseURL, relativePath)

	response := dto.FileResponse{
		ID:            file.ID,
		FileName:      file.FileName,
		OriginalName:  file.OriginalName,
		FileSize:      Int64Value(file.FileSize),
		FileType:      StringValue(file.FileType),
		FilePath:      file.FilePath,
		FileURL:       fileURL,
		BusinessType:  file.BusinessType,
		BusinessID:    file.BusinessID,
		Description:   file.Description,
		IsPublic:      BoolValue(file.IsPublic),
		DownloadCount: int(Int64Value(file.DownloadCount)),
		CreatedAt:     file.CreatedAt.Format(time.RFC3339),
		UpdatedAt:     file.UpdatedAt.Format(time.RFC3339),
		CreatedBy:     *file.CreatedBy,
	}

	if file.Uploader != nil {
		response.Uploader = &dto.UserSimpleResponse{
			ID:       file.Uploader.ID,
			UserName: file.Uploader.UserName,
			JobTitle: file.Uploader.JobTitle,
		}
	}

	return response
}
