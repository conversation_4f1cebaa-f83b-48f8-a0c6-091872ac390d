package services

import (
	"encoding/json"
	"errors"
	"hospital-management/internal/dto"
	"hospital-management/internal/models"
	"hospital-management/pkg/logger"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type AssetMaintenanceService struct {
	db *gorm.DB
}

func NewAssetMaintenanceService(db *gorm.DB) *AssetMaintenanceService {
	return &AssetMaintenanceService{db: db}
}

// CreateMaintenanceRecord 创建维护记录
func (s *AssetMaintenanceService) CreateMaintenanceRecord(req *dto.CreateAssetMaintenanceRequest, userID uuid.UUID) (*dto.AssetMaintenanceResponse, error) {
	// 检查资产是否存在
	var asset models.Asset
	if err := s.db.First(&asset, req.AssetID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("资产不存在")
		}
		return nil, err
	}

	// 解析维护日期
	maintenanceDate, err := time.Parse("2006-01-02", req.MaintenanceDate)
	if err != nil {
		return nil, errors.New("维护日期格式错误")
	}

	// 解析下次维护日期
	var nextMaintenanceDate *time.Time
	if req.NextMaintenanceDate != nil {
		nextDate, err := time.Parse("2006-01-02", *req.NextMaintenanceDate)
		if err != nil {
			return nil, errors.New("下次维护日期格式错误")
		}
		nextMaintenanceDate = &nextDate
	}

	// 处理附件URLs
	var attachmentUrlsJSON *string
	if len(req.AttachmentUrls) > 0 {
		urlsBytes, err := json.Marshal(req.AttachmentUrls)
		if err != nil {
			return nil, errors.New("附件URLs格式错误")
		}
		urlsStr := string(urlsBytes)
		attachmentUrlsJSON = &urlsStr
	}

	// 创建维护记录
	record := models.AssetMaintenanceRecord{
		AssetID:             req.AssetID,
		MaintenanceType:     req.MaintenanceType,
		MaintenanceDate:     maintenanceDate,
		MaintenanceCost:     req.MaintenanceCost,
		MaintenanceCompany:  req.MaintenanceCompany,
		MaintenancePerson:   req.MaintenancePerson,
		Description:         req.Description,
		NextMaintenanceDate: nextMaintenanceDate,
		Status:              req.Status,
		AttachmentUrls:      attachmentUrlsJSON,
		Remark:              req.Remark,
	}
	record.CreatedBy = &userID

	if err := s.db.Create(&record).Error; err != nil {
		logger.Error("Failed to create maintenance record:", err)
		return nil, err
	}

	// 如果维护完成，更新资产的最后维护日期和下次维护日期
	if req.Status == "completed" {
		updates := map[string]interface{}{
			"last_maintenance_date": maintenanceDate,
		}
		if nextMaintenanceDate != nil {
			updates["next_maintenance_date"] = *nextMaintenanceDate
		}
		
		if err := s.db.Model(&asset).Updates(updates).Error; err != nil {
			logger.Error("Failed to update asset maintenance dates:", err)
		}
	}

	return s.GetMaintenanceRecordByID(record.ID, userID)
}

// UpdateMaintenanceRecord 更新维护记录
func (s *AssetMaintenanceService) UpdateMaintenanceRecord(id uuid.UUID, req *dto.UpdateAssetMaintenanceRequest, userID uuid.UUID) (*dto.AssetMaintenanceResponse, error) {
	var record models.AssetMaintenanceRecord
	if err := s.db.First(&record, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("维护记录不存在")
		}
		return nil, err
	}

	// 更新字段
	updates := make(map[string]interface{})
	
	if req.MaintenanceType != nil {
		updates["maintenance_type"] = *req.MaintenanceType
	}
	if req.MaintenanceDate != nil {
		maintenanceDate, err := time.Parse("2006-01-02", *req.MaintenanceDate)
		if err != nil {
			return nil, errors.New("维护日期格式错误")
		}
		updates["maintenance_date"] = maintenanceDate
	}
	if req.MaintenanceCost != nil {
		updates["maintenance_cost"] = *req.MaintenanceCost
	}
	if req.MaintenanceCompany != nil {
		updates["maintenance_company"] = *req.MaintenanceCompany
	}
	if req.MaintenancePerson != nil {
		updates["maintenance_person"] = *req.MaintenancePerson
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.NextMaintenanceDate != nil {
		nextDate, err := time.Parse("2006-01-02", *req.NextMaintenanceDate)
		if err != nil {
			return nil, errors.New("下次维护日期格式错误")
		}
		updates["next_maintenance_date"] = nextDate
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}
	if len(req.AttachmentUrls) > 0 {
		urlsBytes, err := json.Marshal(req.AttachmentUrls)
		if err != nil {
			return nil, errors.New("附件URLs格式错误")
		}
		updates["attachment_urls"] = string(urlsBytes)
	}
	if req.Remark != nil {
		updates["remark"] = *req.Remark
	}

	updates["updated_by"] = userID

	if err := s.db.Model(&record).Updates(updates).Error; err != nil {
		logger.Error("Failed to update maintenance record:", err)
		return nil, err
	}

	// 如果状态更新为完成，更新资产的维护日期
	if req.Status != nil && *req.Status == "completed" {
		var asset models.Asset
		if err := s.db.First(&asset, record.AssetID).Error; err == nil {
			assetUpdates := map[string]interface{}{
				"last_maintenance_date": record.MaintenanceDate,
			}
			if record.NextMaintenanceDate != nil {
				assetUpdates["next_maintenance_date"] = *record.NextMaintenanceDate
			}
			
			if err := s.db.Model(&asset).Updates(assetUpdates).Error; err != nil {
				logger.Error("Failed to update asset maintenance dates:", err)
			}
		}
	}

	return s.GetMaintenanceRecordByID(id, userID)
}

// GetMaintenanceRecords 获取维护记录列表
func (s *AssetMaintenanceService) GetMaintenanceRecords(req *dto.AssetMaintenanceListRequest) ([]dto.AssetMaintenanceResponse, int64, error) {
	var records []models.AssetMaintenanceRecord
	var total int64

	query := s.db.Model(&models.AssetMaintenanceRecord{}).
		Preload("Asset").
		Preload("Creator")

	// 搜索条件
	if req.AssetID != nil {
		query = query.Where("asset_id = ?", *req.AssetID)
	}
	if req.MaintenanceType != "" {
		query = query.Where("maintenance_type = ?", req.MaintenanceType)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.StartDate != "" {
		query = query.Where("maintenance_date >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		query = query.Where("maintenance_date <= ?", req.EndDate)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).
		Order("maintenance_date DESC").Find(&records).Error; err != nil {
		return nil, 0, err
	}

	// 转换为响应格式
	responses := make([]dto.AssetMaintenanceResponse, len(records))
	for i, record := range records {
		responses[i] = s.convertToMaintenanceResponse(record)
	}

	return responses, total, nil
}

// GetMaintenanceRecordByID 根据ID获取维护记录
func (s *AssetMaintenanceService) GetMaintenanceRecordByID(id uuid.UUID, userID uuid.UUID) (*dto.AssetMaintenanceResponse, error) {
	var record models.AssetMaintenanceRecord
	if err := s.db.Preload("Asset").Preload("Creator").First(&record, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("维护记录不存在")
		}
		return nil, err
	}

	response := s.convertToMaintenanceResponse(record)
	return &response, nil
}

// DeleteMaintenanceRecord 删除维护记录
func (s *AssetMaintenanceService) DeleteMaintenanceRecord(id uuid.UUID, userID uuid.UUID) error {
	var record models.AssetMaintenanceRecord
	if err := s.db.First(&record, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("维护记录不存在")
		}
		return err
	}

	// 软删除
	if err := s.db.Model(&record).Update("deleted_at", time.Now()).Error; err != nil {
		logger.Error("Failed to delete maintenance record:", err)
		return err
	}

	return nil
}

// GetUpcomingMaintenances 获取即将到期的维护
func (s *AssetMaintenanceService) GetUpcomingMaintenances(days int) ([]dto.AssetMaintenanceResponse, error) {
	var records []models.AssetMaintenanceRecord
	
	// 查询未来指定天数内需要维护的记录
	endDate := time.Now().AddDate(0, 0, days)
	
	if err := s.db.Preload("Asset").Preload("Creator").
		Where("next_maintenance_date <= ? AND next_maintenance_date >= ? AND status != ?", 
			endDate, time.Now(), "completed").
		Order("next_maintenance_date ASC").
		Find(&records).Error; err != nil {
		return nil, err
	}

	// 转换为响应格式
	responses := make([]dto.AssetMaintenanceResponse, len(records))
	for i, record := range records {
		responses[i] = s.convertToMaintenanceResponse(record)
	}

	return responses, nil
}

// convertToMaintenanceResponse 转换为维护响应格式
func (s *AssetMaintenanceService) convertToMaintenanceResponse(record models.AssetMaintenanceRecord) dto.AssetMaintenanceResponse {
	response := dto.AssetMaintenanceResponse{
		ID:                  record.ID,
		AssetID:             record.AssetID,
		MaintenanceType:     record.MaintenanceType,
		MaintenanceDate:     record.MaintenanceDate.Format("2006-01-02"),
		MaintenanceCost:     record.MaintenanceCost,
		MaintenanceCompany:  record.MaintenanceCompany,
		MaintenancePerson:   record.MaintenancePerson,
		Description:         record.Description,
		Status:              record.Status,
		Remark:              record.Remark,
		CreatedAt:           record.CreatedAt.Format(time.RFC3339),
		UpdatedAt:           record.UpdatedAt.Format(time.RFC3339),
		CreatedBy:           *record.CreatedBy,
	}

	if record.NextMaintenanceDate != nil {
		nextDate := record.NextMaintenanceDate.Format("2006-01-02")
		response.NextMaintenanceDate = &nextDate
	}

	// 解析附件URLs
	if record.AttachmentUrls != nil {
		var urls []string
		if err := json.Unmarshal([]byte(*record.AttachmentUrls), &urls); err == nil {
			response.AttachmentUrls = urls
		}
	}

	if record.Asset != nil {
		// 这里可以添加资产信息的转换
		// response.Asset = convertToAssetResponse(*record.Asset)
	}

	if record.Creator != nil {
		response.Creator = &dto.UserSimpleResponse{
			ID:       record.Creator.ID,
			UserName: record.Creator.UserName,
			JobTitle: record.Creator.JobTitle,
		}
	}

	return response
}
