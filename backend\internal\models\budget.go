package models

import (
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// BudgetScheme 预算方案表 - 对应 tbl_budget_schemes
type BudgetScheme struct {
	BaseModel
	Name        string `json:"name" gorm:"type:varchar(255);not null;index" validate:"required,max=255"`
	Year        int    `json:"year" gorm:"not null;index" validate:"required,min=2000,max=3000"`
	ControlRule string `json:"control_rule" gorm:"type:varchar(30);not null;default:'FLEXIBLE'" validate:"required,max=30"`
	Status      string `json:"status" gorm:"type:varchar(30);not null;index" validate:"required,max=30"`

	// 关联关系
	BudgetItems []BudgetItem `json:"budget_items,omitempty" gorm:"foreignKey:SchemeID"`
}

// TableName 指定表名
func (BudgetScheme) TableName() string {
	return "tbl_budget_schemes"
}

// BudgetSubject 预算科目体系 - 对应 tbl_budget_subjects
type BudgetSubject struct {
	BaseModel
	ParentID              *uuid.UUID `json:"parent_id,omitempty" gorm:"type:uuid;index"`
	Name                  string     `json:"name" gorm:"type:varchar(255);not null;index" validate:"required,max=255"`
	Code                  string     `json:"code" gorm:"type:varchar(50);unique;not null;index" validate:"required,max=50"`
	Type                  string     `json:"type" gorm:"type:varchar(20);not null;index" validate:"required,max=20"`
	AccountingSubjectCode *string    `json:"accounting_subject_code,omitempty" gorm:"type:varchar(50)"`

	// 关联关系
	Parent      *BudgetSubject  `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	Children    []BudgetSubject `json:"children,omitempty" gorm:"foreignKey:ParentID"`
	BudgetItems []BudgetItem    `json:"budget_items,omitempty" gorm:"foreignKey:SubjectID"`
}

// TableName 指定表名
func (BudgetSubject) TableName() string {
	return "tbl_budget_subjects"
}

// BudgetItem 预算明细项 - 对应 tbl_budget_items
type BudgetItem struct {
	BaseModel
	SchemeID     uuid.UUID       `json:"scheme_id" gorm:"type:uuid;not null" validate:"required"`
	DepartmentID uuid.UUID       `json:"department_id" gorm:"type:uuid;not null" validate:"required"`
	SubjectID    uuid.UUID       `json:"subject_id" gorm:"type:uuid;not null" validate:"required"`
	TotalAmount  decimal.Decimal `json:"total_amount" gorm:"type:decimal(18,2);not null;default:0.00" validate:"required,gte=0"`
	UsedAmount   decimal.Decimal `json:"used_amount" gorm:"type:decimal(18,2);not null;default:0.00" validate:"gte=0"`
	FrozenAmount decimal.Decimal `json:"frozen_amount" gorm:"type:decimal(18,2);not null;default:0.00" validate:"gte=0"`

	// 关联关系
	Scheme     *BudgetScheme  `json:"scheme,omitempty" gorm:"foreignKey:SchemeID"`
	Department *Department    `json:"department,omitempty" gorm:"foreignKey:DepartmentID"`
	Subject    *BudgetSubject `json:"subject,omitempty" gorm:"foreignKey:SubjectID"`
}

// TableName 指定表名
func (BudgetItem) TableName() string {
	return "tbl_budget_items"
}
