package controllers

import (
	"hospital-management/internal/dto"
	"hospital-management/internal/services"
	"hospital-management/pkg/logger"
	customValidator "hospital-management/pkg/validator"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
)

type AssetController struct {
	assetService        *services.AssetService
	depreciationService *services.AssetDepreciationService
	maintenanceService  *services.AssetMaintenanceService
	validator           *validator.Validate
}

func NewAssetController(assetService *services.AssetService, depreciationService *services.AssetDepreciationService, maintenanceService *services.AssetMaintenanceService) *AssetController {
	return &AssetController{
		assetService:        assetService,
		depreciationService: depreciationService,
		maintenanceService:  maintenanceService,
		validator:           customValidator.NewValidator(),
	}
}

// ===== 资产分类管理 =====

// GetAssetCategories 获取资产分类列表
// @Summary 获取资产分类列表
// @Description 获取所有资产分类
// @Tags 资产管理
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "success"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /asset/categories [get]
func (c *AssetController) GetAssetCategories(ctx *gin.Context) {
	// 调用服务
	categories, err := c.assetService.GetAssetCategories()
	if err != nil {
		logger.Error("Failed to get asset categories:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取资产分类列表失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    categories,
	})
}

// GetAssetCategoryTree 获取资产分类树形结构
// @Summary 获取资产分类树形结构
// @Description 获取资产分类的树形结构
// @Tags 资产管理
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "success"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /asset/categories/tree [get]
func (c *AssetController) GetAssetCategoryTree(ctx *gin.Context) {
	// 调用服务
	tree, err := c.assetService.GetAssetCategoryTree()
	if err != nil {
		logger.Error("Failed to get asset category tree:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取资产分类树失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    tree,
	})
}

// CreateAssetCategory 创建资产分类
// @Summary 创建资产分类
// @Description 创建新的资产分类
// @Tags 资产管理
// @Accept json
// @Produce json
// @Param request body dto.CreateAssetCategoryRequest true "创建资产分类请求"
// @Success 201 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /asset/categories [post]
func (c *AssetController) CreateAssetCategory(ctx *gin.Context) {
	var req dto.CreateAssetCategoryRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	category, err := c.assetService.CreateAssetCategory(&req)
	if err != nil {
		logger.Error("Failed to create asset category:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建资产分类失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "创建成功",
		"data":    category,
	})
}

// UpdateAssetCategory 更新资产分类
// @Summary 更新资产分类
// @Description 更新资产分类信息
// @Tags 资产管理
// @Accept json
// @Produce json
// @Param id path string true "资产分类ID"
// @Param request body dto.UpdateAssetCategoryRequest true "更新资产分类请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "资产分类不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /asset/categories/{id} [put]
func (c *AssetController) UpdateAssetCategory(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的资产分类ID",
		})
		return
	}

	var req dto.UpdateAssetCategoryRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	category, err := c.assetService.UpdateAssetCategory(id, &req)
	if err != nil {
		logger.Error("Failed to update asset category:", err)
		if err.Error() == "资产分类不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "更新资产分类失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    category,
	})
}

// DeleteAssetCategory 删除资产分类
// @Summary 删除资产分类
// @Description 删除指定的资产分类
// @Tags 资产管理
// @Accept json
// @Produce json
// @Param id path string true "资产分类ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "资产分类不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /asset/categories/{id} [delete]
func (c *AssetController) DeleteAssetCategory(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的资产分类ID",
		})
		return
	}

	// 调用服务
	if err := c.assetService.DeleteAssetCategory(id); err != nil {
		logger.Error("Failed to delete asset category:", err)
		if err.Error() == "资产分类不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "删除资产分类失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// ===== 资产管理 =====

// GetAssets 获取资产列表
// @Summary 获取资产列表
// @Description 获取资产列表，支持分页、搜索和筛选
// @Tags 资产管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param keyword query string false "搜索关键词"
// @Param category_id query string false "资产分类ID"
// @Param status query string false "状态" Enums(IN_USE, IDLE, MAINTENANCE, SCRAPPED)
// @Param owner_dept_id query string false "使用部门ID"
// @Param custodian_id query string false "保管人ID"
// @Param purchase_date_from query string false "采购日期起"
// @Param purchase_date_to query string false "采购日期止"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /assets [get]
func (c *AssetController) GetAssets(ctx *gin.Context) {
	var req dto.AssetListRequest

	// 设置默认值
	req.Page = 1
	req.PageSize = 10

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 调用服务
	assets, total, err := c.assetService.GetAssets(&req)
	if err != nil {
		logger.Error("Failed to get assets:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取资产列表失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      assets,
			"total":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	})
}

// GetAsset 获取资产详情
// @Summary 获取资产详情
// @Description 获取指定资产的详细信息
// @Tags 资产管理
// @Accept json
// @Produce json
// @Param id path string true "资产ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "资产不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /assets/{id} [get]
func (c *AssetController) GetAsset(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的资产ID",
		})
		return
	}

	// 调用服务
	asset, err := c.assetService.GetAsset(id)
	if err != nil {
		logger.Error("Failed to get asset:", err)
		if err.Error() == "资产不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "获取资产详情失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    asset,
	})
}

// CreateAsset 创建资产
// @Summary 创建资产
// @Description 创建新的资产
// @Tags 资产管理
// @Accept json
// @Produce json
// @Param request body dto.CreateAssetRequest true "创建资产请求"
// @Success 201 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /assets [post]
func (c *AssetController) CreateAsset(ctx *gin.Context) {
	var req dto.CreateAssetRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	asset, err := c.assetService.CreateAsset(&req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to create asset:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建资产失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "创建成功",
		"data":    asset,
	})
}

// UpdateAsset 更新资产
// @Summary 更新资产
// @Description 更新资产信息
// @Tags 资产管理
// @Accept json
// @Produce json
// @Param id path string true "资产ID"
// @Param request body dto.UpdateAssetRequest true "更新资产请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "资产不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /assets/{id} [put]
func (c *AssetController) UpdateAsset(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的资产ID",
		})
		return
	}

	var req dto.UpdateAssetRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	asset, err := c.assetService.UpdateAsset(id, &req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to update asset:", err)
		if err.Error() == "资产不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "更新资产失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    asset,
	})
}

// UpdateAssetStatus 更新资产状态
// @Summary 更新资产状态
// @Description 更新资产的状态
// @Tags 资产管理
// @Accept json
// @Produce json
// @Param id path string true "资产ID"
// @Param request body dto.UpdateAssetStatusRequest true "更新资产状态请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "资产不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /assets/{id}/status [put]
func (c *AssetController) UpdateAssetStatus(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的资产ID",
		})
		return
	}

	var req dto.UpdateAssetStatusRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	asset, err := c.assetService.UpdateAssetStatus(id, &req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to update asset status:", err)
		if err.Error() == "资产不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "更新资产状态失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    asset,
	})
}

// BatchUpdateAssetStatus 批量更新资产状态
// @Summary 批量更新资产状态
// @Description 批量更新多个资产的状态
// @Tags 资产管理
// @Accept json
// @Produce json
// @Param request body dto.BatchUpdateAssetStatusRequest true "批量更新资产状态请求"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /assets/batch/status [put]
func (c *AssetController) BatchUpdateAssetStatus(ctx *gin.Context) {
	var req dto.BatchUpdateAssetStatusRequest

	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权",
		})
		return
	}

	// 调用服务
	if err := c.assetService.BatchUpdateAssetStatus(&req, userID.(uuid.UUID)); err != nil {
		logger.Error("Failed to batch update asset status:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "批量更新资产状态失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "批量更新成功",
	})
}

// DeleteAsset 删除资产
// @Summary 删除资产
// @Description 删除指定的资产
// @Tags 资产管理
// @Accept json
// @Produce json
// @Param id path string true "资产ID"
// @Success 200 {object} map[string]interface{} "success"
// @Failure 400 {object} map[string]interface{} "参数错误"
// @Failure 404 {object} map[string]interface{} "资产不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Security BearerAuth
// @Router /assets/{id} [delete]
func (c *AssetController) DeleteAsset(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的资产ID",
		})
		return
	}

	// 调用服务
	if err := c.assetService.DeleteAsset(id); err != nil {
		logger.Error("Failed to delete asset:", err)
		if err.Error() == "资产不存在" {
			ctx.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": err.Error(),
			})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "删除资产失败",
				"error":   err.Error(),
			})
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// ===== 资产折旧管理相关接口 =====

// CreateDepreciationRecord 创建折旧记录
func (c *AssetController) CreateDepreciationRecord(ctx *gin.Context) {
	var req dto.CreateAssetDepreciationRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证请求参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权访问",
		})
		return
	}

	response, err := c.depreciationService.CreateDepreciationRecord(&req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to create depreciation record:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建折旧记录失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data":    response,
	})
}

// GetDepreciationRecords 获取折旧记录列表
func (c *AssetController) GetDepreciationRecords(ctx *gin.Context) {
	var req dto.AssetDepreciationListRequest

	// 设置默认值
	req.Page = 1
	req.PageSize = 10

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证请求参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	records, total, err := c.depreciationService.GetDepreciationRecords(&req)
	if err != nil {
		logger.Error("Failed to get depreciation records:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取折旧记录失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":       records,
			"total":      total,
			"page":       req.Page,
			"page_size":  req.PageSize,
			"total_page": (total + int64(req.PageSize) - 1) / int64(req.PageSize),
		},
	})
}

// GetDepreciationRecord 获取折旧记录详情
func (c *AssetController) GetDepreciationRecord(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的ID格式",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权访问",
		})
		return
	}

	record, err := c.depreciationService.GetDepreciationRecordByID(id, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to get depreciation record:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取折旧记录失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    record,
	})
}

// BatchCalculateDepreciation 批量计算折旧
func (c *AssetController) BatchCalculateDepreciation(ctx *gin.Context) {
	var req dto.BatchDepreciationRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证请求参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权访问",
		})
		return
	}

	err := c.depreciationService.BatchCalculateDepreciation(&req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to batch calculate depreciation:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "批量计算折旧失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "批量计算折旧成功",
	})
}

// ===== 资产维护管理相关接口 =====

// CreateMaintenanceRecord 创建维护记录
func (c *AssetController) CreateMaintenanceRecord(ctx *gin.Context) {
	var req dto.CreateAssetMaintenanceRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证请求参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权访问",
		})
		return
	}

	response, err := c.maintenanceService.CreateMaintenanceRecord(&req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to create maintenance record:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建维护记录失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data":    response,
	})
}

// UpdateMaintenanceRecord 更新维护记录
func (c *AssetController) UpdateMaintenanceRecord(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的ID格式",
		})
		return
	}

	var req dto.UpdateAssetMaintenanceRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Error("Failed to bind JSON:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证请求参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权访问",
		})
		return
	}

	response, err := c.maintenanceService.UpdateMaintenanceRecord(id, &req, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to update maintenance record:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新维护记录失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    response,
	})
}

// GetMaintenanceRecords 获取维护记录列表
func (c *AssetController) GetMaintenanceRecords(ctx *gin.Context) {
	var req dto.AssetMaintenanceListRequest

	// 设置默认值
	req.Page = 1
	req.PageSize = 10

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		logger.Error("Failed to bind query parameters:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证请求参数
	if err := c.validator.Struct(&req); err != nil {
		logger.Error("Validation failed:", err)
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数验证失败",
			"error":   err.Error(),
		})
		return
	}

	records, total, err := c.maintenanceService.GetMaintenanceRecords(&req)
	if err != nil {
		logger.Error("Failed to get maintenance records:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取维护记录失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":       records,
			"total":      total,
			"page":       req.Page,
			"page_size":  req.PageSize,
			"total_page": (total + int64(req.PageSize) - 1) / int64(req.PageSize),
		},
	})
}

// GetMaintenanceRecord 获取维护记录详情
func (c *AssetController) GetMaintenanceRecord(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的ID格式",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权访问",
		})
		return
	}

	record, err := c.maintenanceService.GetMaintenanceRecordByID(id, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to get maintenance record:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取维护记录失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    record,
	})
}

// DeleteMaintenanceRecord 删除维护记录
func (c *AssetController) DeleteMaintenanceRecord(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的ID格式",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权访问",
		})
		return
	}

	err = c.maintenanceService.DeleteMaintenanceRecord(id, userID.(uuid.UUID))
	if err != nil {
		logger.Error("Failed to delete maintenance record:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除维护记录失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// GetUpcomingMaintenances 获取即将到期的维护
func (c *AssetController) GetUpcomingMaintenances(ctx *gin.Context) {
	// 默认查询未来30天内的维护
	days := 30
	if daysStr := ctx.Query("days"); daysStr != "" {
		if d, err := strconv.Atoi(daysStr); err == nil && d > 0 {
			days = d
		}
	}

	records, err := c.maintenanceService.GetUpcomingMaintenances(days)
	if err != nil {
		logger.Error("Failed to get upcoming maintenances:", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取即将到期的维护失败",
			"error":   err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    records,
	})
}
