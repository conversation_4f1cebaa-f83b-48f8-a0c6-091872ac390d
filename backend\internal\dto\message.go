package dto

import (
	"github.com/google/uuid"
)

// CreateMessageRequest 创建消息请求
type CreateMessageRequest struct {
	MessageType string      `json:"message_type" validate:"required,oneof=notification announcement todo reminder"`
	Title       string      `json:"title" validate:"required,max=200"`
	Content     string      `json:"content" validate:"required,max=2000"`
	Priority    string      `json:"priority" validate:"required,oneof=low normal high urgent"`
	ReceiverIDs []uuid.UUID `json:"receiver_ids,omitempty" validate:"omitempty,dive,uuid"`
	BusinessID  *uuid.UUID  `json:"business_id,omitempty" validate:"omitempty,uuid"`
	BusinessType *string    `json:"business_type,omitempty" validate:"omitempty,max=50"`
	ScheduledAt *string     `json:"scheduled_at,omitempty" validate:"omitempty,datetime=2006-01-02T15:04:05Z07:00"`
}

// MessageResponse 消息响应
type MessageResponse struct {
	ID           uuid.UUID            `json:"id"`
	MessageType  string               `json:"message_type"`
	Title        string               `json:"title"`
	Content      string               `json:"content"`
	Priority     string               `json:"priority"`
	Status       string               `json:"status"`
	BusinessID   *uuid.UUID           `json:"business_id,omitempty"`
	BusinessType *string              `json:"business_type,omitempty"`
	ScheduledAt  *string              `json:"scheduled_at,omitempty"`
	SentAt       *string              `json:"sent_at,omitempty"`
	CreatedAt    string               `json:"created_at"`
	UpdatedAt    string               `json:"updated_at"`
	CreatedBy    uuid.UUID            `json:"created_by"`
	Sender       *UserSimpleResponse  `json:"sender,omitempty"`
	ReadStatus   *string              `json:"read_status,omitempty"` // 当前用户的阅读状态
	ReadAt       *string              `json:"read_at,omitempty"`     // 当前用户的阅读时间
}

// MessageListRequest 消息列表请求
type MessageListRequest struct {
	Page         int        `form:"page" validate:"min=1"`
	PageSize     int        `form:"page_size" validate:"min=1,max=100"`
	MessageType  string     `form:"message_type" validate:"omitempty,oneof=notification announcement todo reminder"`
	Priority     string     `form:"priority" validate:"omitempty,oneof=low normal high urgent"`
	Status       string     `form:"status" validate:"omitempty,oneof=draft sent"`
	IsRead       *bool      `form:"is_read"`
	BusinessType string     `form:"business_type"`
	SenderID     *uuid.UUID `form:"sender_id" validate:"omitempty,uuid"`
	Keyword      string     `form:"keyword"`
	StartDate    string     `form:"start_date" validate:"omitempty,datetime=2006-01-02"`
	EndDate      string     `form:"end_date" validate:"omitempty,datetime=2006-01-02"`
}

// MarkReadRequest 标记已读请求
type MarkReadRequest struct {
	MessageIDs []uuid.UUID `json:"message_ids" validate:"required,dive,uuid"`
}

// UnreadCountResponse 未读消息数量响应
type UnreadCountResponse struct {
	TotalCount        int64                    `json:"total_count"`
	NotificationCount int64                    `json:"notification_count"`
	AnnouncementCount int64                    `json:"announcement_count"`
	TodoCount         int64                    `json:"todo_count"`
	ReminderCount     int64                    `json:"reminder_count"`
	ByPriority        map[string]int64         `json:"by_priority"`
	ByBusinessType    map[string]int64         `json:"by_business_type"`
}

// SendMessageRequest 发送消息请求
type SendMessageRequest struct {
	MessageID uuid.UUID `json:"message_id" validate:"required,uuid"`
}

// BroadcastMessageRequest 广播消息请求
type BroadcastMessageRequest struct {
	MessageType  string     `json:"message_type" validate:"required,oneof=notification announcement"`
	Title        string     `json:"title" validate:"required,max=200"`
	Content      string     `json:"content" validate:"required,max=2000"`
	Priority     string     `json:"priority" validate:"required,oneof=low normal high urgent"`
	TargetType   string     `json:"target_type" validate:"required,oneof=all department role"`
	TargetIDs    []uuid.UUID `json:"target_ids,omitempty" validate:"omitempty,dive,uuid"`
	ScheduledAt  *string    `json:"scheduled_at,omitempty" validate:"omitempty,datetime=2006-01-02T15:04:05Z07:00"`
}

// MessageReceiverResponse 消息接收者响应
type MessageReceiverResponse struct {
	ID         uuid.UUID            `json:"id"`
	MessageID  uuid.UUID            `json:"message_id"`
	ReceiverID uuid.UUID            `json:"receiver_id"`
	Status     string               `json:"status"`
	ReadAt     *string              `json:"read_at,omitempty"`
	CreatedAt  string               `json:"created_at"`
	Receiver   *UserSimpleResponse  `json:"receiver,omitempty"`
}

// MessageStatisticsResponse 消息统计响应
type MessageStatisticsResponse struct {
	TotalSent      int64                    `json:"total_sent"`
	TotalRead      int64                    `json:"total_read"`
	ReadRate       float64                  `json:"read_rate"`
	ByMessageType  map[string]int64         `json:"by_message_type"`
	ByPriority     map[string]int64         `json:"by_priority"`
	RecentActivity []MessageActivityItem    `json:"recent_activity"`
}

// MessageActivityItem 消息活动项
type MessageActivityItem struct {
	Date  string `json:"date"`
	Sent  int64  `json:"sent"`
	Read  int64  `json:"read"`
}
