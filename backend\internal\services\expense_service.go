package services

import (
	"errors"
	"fmt"
	"hospital-management/internal/dto"
	"hospital-management/internal/models"
	"hospital-management/pkg/logger"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type ExpenseService struct {
	db *gorm.DB
}

func NewExpenseService(db *gorm.DB) *ExpenseService {
	return &ExpenseService{db: db}
}

// ===== 事前申请管理 =====

// GetPreApplications 获取事前申请列表
func (s *ExpenseService) GetPreApplications(req *dto.PreApplicationListRequest) ([]dto.PreApplicationResponse, int64, error) {
	var applications []models.PreApplication
	var total int64

	query := s.db.Model(&models.PreApplication{}).
		Preload("Applicant").
		Preload("Department")

	// 搜索条件
	if req.Keyword != "" {
		query = query.Where("title ILIKE ? OR application_code ILIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}
	if req.Type != "" {
		query = query.Where("type = ?", req.Type)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.ApplicantID != nil {
		query = query.Where("applicant_id = ?", *req.ApplicantID)
	}
	if req.DepartmentID != nil {
		query = query.Where("department_id = ?", *req.DepartmentID)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count pre applications:", err)
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&applications).Error; err != nil {
		logger.Error("Failed to get pre applications:", err)
		return nil, 0, err
	}

	// 转换响应
	responses := make([]dto.PreApplicationResponse, len(applications))
	for i, app := range applications {
		responses[i] = s.convertPreApplicationToResponse(app)
	}

	return responses, total, nil
}

// GetPreApplication 获取事前申请详情
func (s *ExpenseService) GetPreApplication(id uuid.UUID) (*dto.PreApplicationResponse, error) {
	var application models.PreApplication
	if err := s.db.Preload("Applicant").Preload("Department").First(&application, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("事前申请不存在")
		}
		return nil, err
	}

	response := s.convertPreApplicationToResponse(application)
	return &response, nil
}

// CreatePreApplication 创建事前申请
func (s *ExpenseService) CreatePreApplication(req *dto.CreatePreApplicationRequest, userID uuid.UUID) (*dto.PreApplicationResponse, error) {
	// 获取用户信息以获取部门ID
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return nil, errors.New("用户不存在")
	}

	// 生成申请单号
	applicationCode, err := s.generatePreApplicationCode()
	if err != nil {
		return nil, err
	}

	application := models.PreApplication{
		ApplicationCode: applicationCode,
		ApplicantID:     userID,
		DepartmentID:    user.DepartmentID,
		Type:            req.Type,
		Title:           req.Title,
		EstimatedAmount: req.EstimatedAmount,
		Status:          "DRAFT",
		Details:         (*models.JSONMap)(&req.Details),
	}
	application.CreatedBy = &userID

	if err := s.db.Create(&application).Error; err != nil {
		logger.Error("Failed to create pre application:", err)
		return nil, err
	}

	// 预加载关联数据
	if err := s.db.Preload("Applicant").Preload("Department").First(&application, application.ID).Error; err != nil {
		logger.Error("Failed to preload pre application data:", err)
		return nil, err
	}

	response := s.convertPreApplicationToResponse(application)
	return &response, nil
}

// UpdatePreApplication 更新事前申请
func (s *ExpenseService) UpdatePreApplication(id uuid.UUID, req *dto.UpdatePreApplicationRequest, userID uuid.UUID) (*dto.PreApplicationResponse, error) {
	var application models.PreApplication
	if err := s.db.First(&application, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("事前申请不存在")
		}
		return nil, err
	}

	// 检查是否可以修改
	if application.Status != "DRAFT" {
		return nil, errors.New("只能修改草稿状态的申请")
	}

	// 检查权限
	if application.ApplicantID != userID {
		return nil, errors.New("只能修改自己的申请")
	}

	// 更新字段
	if req.Type != nil {
		application.Type = *req.Type
	}
	if req.Title != nil {
		application.Title = *req.Title
	}
	if req.EstimatedAmount != nil {
		application.EstimatedAmount = *req.EstimatedAmount
	}
	if req.Details != nil {
		application.Details = (*models.JSONMap)(&req.Details)
	}
	application.UpdatedBy = &userID

	if err := s.db.Save(&application).Error; err != nil {
		logger.Error("Failed to update pre application:", err)
		return nil, err
	}

	// 预加载关联数据
	if err := s.db.Preload("Applicant").Preload("Department").First(&application, application.ID).Error; err != nil {
		logger.Error("Failed to preload pre application data:", err)
		return nil, err
	}

	response := s.convertPreApplicationToResponse(application)
	return &response, nil
}

// SubmitPreApplication 提交事前申请
func (s *ExpenseService) SubmitPreApplication(id uuid.UUID, userID uuid.UUID) error {
	var application models.PreApplication
	if err := s.db.First(&application, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("事前申请不存在")
		}
		return err
	}

	// 检查状态
	if application.Status != "DRAFT" {
		return errors.New("只能提交草稿状态的申请")
	}

	// 检查权限
	if application.ApplicantID != userID {
		return errors.New("只能提交自己的申请")
	}

	// 更新状态
	application.Status = "PENDING"
	application.UpdatedBy = &userID

	if err := s.db.Save(&application).Error; err != nil {
		logger.Error("Failed to submit pre application:", err)
		return err
	}

	// TODO: 这里应该启动审批流程
	logger.Info("Pre application submitted:", application.ID)

	return nil
}

// ===== 费用报销管理 =====

// GetExpenseApplications 获取报销申请列表
func (s *ExpenseService) GetExpenseApplications(req *dto.ExpenseApplicationListRequest) ([]dto.ExpenseApplicationResponse, int64, error) {
	var applications []models.ExpenseApplication
	var total int64

	query := s.db.Model(&models.ExpenseApplication{}).
		Preload("Applicant").
		Preload("Department").
		Preload("PreApplication").
		Preload("Details").
		Preload("Details.BudgetItem").
		Preload("Details.BudgetItem.Subject")

	// 搜索条件
	if req.Keyword != "" {
		query = query.Where("title ILIKE ? OR application_code ILIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.ApplicantID != nil {
		query = query.Where("applicant_id = ?", *req.ApplicantID)
	}
	if req.DepartmentID != nil {
		query = query.Where("department_id = ?", *req.DepartmentID)
	}
	if req.PreApplicationID != nil {
		query = query.Where("pre_application_id = ?", *req.PreApplicationID)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count expense applications:", err)
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&applications).Error; err != nil {
		logger.Error("Failed to get expense applications:", err)
		return nil, 0, err
	}

	// 转换响应
	responses := make([]dto.ExpenseApplicationResponse, len(applications))
	for i, app := range applications {
		responses[i] = s.convertExpenseApplicationToResponse(app)
	}

	return responses, total, nil
}

// GetExpenseApplication 获取报销申请详情
func (s *ExpenseService) GetExpenseApplication(id uuid.UUID) (*dto.ExpenseApplicationResponse, error) {
	var application models.ExpenseApplication
	if err := s.db.Preload("Applicant").
		Preload("Department").
		Preload("PreApplication").
		Preload("Details").
		Preload("Details.BudgetItem").
		Preload("Details.BudgetItem.Subject").
		First(&application, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("报销申请不存在")
		}
		return nil, err
	}

	response := s.convertExpenseApplicationToResponse(application)
	return &response, nil
}

// GetMyExpenseApplications 获取我的报销记录
func (s *ExpenseService) GetMyExpenseApplications(userID uuid.UUID, req *dto.ExpenseApplicationListRequest) ([]dto.ExpenseApplicationResponse, int64, error) {
	// 设置申请人ID为当前用户
	req.ApplicantID = &userID
	return s.GetExpenseApplications(req)
}

// ===== 辅助方法 =====

// generatePreApplicationCode 生成事前申请单号
func (s *ExpenseService) generatePreApplicationCode() (string, error) {
	now := time.Now()
	prefix := fmt.Sprintf("SQ%04d%02d%02d", now.Year(), now.Month(), now.Day())

	var count int64
	if err := s.db.Model(&models.PreApplication{}).
		Where("application_code LIKE ?", prefix+"%").
		Count(&count).Error; err != nil {
		return "", err
	}

	return fmt.Sprintf("%s%04d", prefix, count+1), nil
}

// generateExpenseApplicationCode 生成报销申请单号
func (s *ExpenseService) generateExpenseApplicationCode() (string, error) {
	now := time.Now()
	prefix := fmt.Sprintf("BX%04d%02d%02d", now.Year(), now.Month(), now.Day())

	var count int64
	if err := s.db.Model(&models.ExpenseApplication{}).
		Where("application_code LIKE ?", prefix+"%").
		Count(&count).Error; err != nil {
		return "", err
	}

	return fmt.Sprintf("%s%04d", prefix, count+1), nil
}

// convertPreApplicationToResponse 转换事前申请为响应格式
func (s *ExpenseService) convertPreApplicationToResponse(app models.PreApplication) dto.PreApplicationResponse {
	response := dto.PreApplicationResponse{
		ID:              app.ID,
		ApplicationCode: app.ApplicationCode,
		ApplicantID:     app.ApplicantID,
		DepartmentID:    app.DepartmentID,
		Type:            app.Type,
		Title:           app.Title,
		EstimatedAmount: app.EstimatedAmount,
		Status:          app.Status,
		CreatedAt:       app.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:       app.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	if app.Details != nil {
		response.Details = map[string]interface{}(*app.Details)
	}

	if app.Applicant != nil {
		response.Applicant = &dto.UserSimpleResponse{
			ID:       app.Applicant.ID,
			UserName: app.Applicant.UserName,
			JobTitle: app.Applicant.JobTitle,
		}
	}

	if app.Department != nil {
		response.Department = &dto.DepartmentSimpleResponse{
			ID:       app.Department.ID,
			DeptName: app.Department.DeptName,
			DeptCode: app.Department.DeptCode,
		}
	}

	return response
}

// convertExpenseApplicationToResponse 转换报销申请为响应格式
func (s *ExpenseService) convertExpenseApplicationToResponse(app models.ExpenseApplication) dto.ExpenseApplicationResponse {
	response := dto.ExpenseApplicationResponse{
		ID:               app.ID,
		ApplicationCode:  app.ApplicationCode,
		ApplicantID:      app.ApplicantID,
		DepartmentID:     app.DepartmentID,
		PreApplicationID: app.PreApplicationID,
		Title:            app.Title,
		TotalAmount:      app.TotalAmount,
		Status:           app.Status,
		CreatedAt:        app.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:        app.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	// 关联数据
	if app.Applicant != nil {
		response.Applicant = &dto.UserSimpleResponse{
			ID:       app.Applicant.ID,
			UserName: app.Applicant.UserName,
			JobTitle: app.Applicant.JobTitle,
		}
	}

	if app.Department != nil {
		response.Department = &dto.DepartmentSimpleResponse{
			ID:       app.Department.ID,
			DeptName: app.Department.DeptName,
			DeptCode: app.Department.DeptCode,
		}
	}

	if app.PreApplication != nil {
		preAppResponse := s.convertPreApplicationToResponse(*app.PreApplication)
		response.PreApplication = &preAppResponse
	}

	// 报销明细
	if len(app.Details) > 0 {
		details := make([]dto.ExpenseDetailResponse, len(app.Details))
		for i, detail := range app.Details {
			details[i] = dto.ExpenseDetailResponse{
				ID:           detail.ID,
				BudgetItemID: detail.BudgetItemID,
				Description:  detail.Description,
				Amount:       detail.Amount,
				ExpenseDate:  detail.ExpenseDate.Format("2006-01-02"),
			}

			if detail.InvoiceInfo != nil {
				details[i].InvoiceInfo = map[string]interface{}(*detail.InvoiceInfo)
			}

			if detail.BudgetItem != nil {
				details[i].BudgetItem = &dto.BudgetItemSimpleResponse{
					ID:           detail.BudgetItem.ID,
					TotalAmount:  detail.BudgetItem.TotalAmount,
					UsedAmount:   detail.BudgetItem.UsedAmount,
					FrozenAmount: detail.BudgetItem.FrozenAmount,
				}

				if detail.BudgetItem.Subject != nil {
					details[i].BudgetItem.Subject = &dto.BudgetSubjectSimpleResponse{
						ID:   detail.BudgetItem.Subject.ID,
						Name: detail.BudgetItem.Subject.Name,
						Code: detail.BudgetItem.Subject.Code,
						Type: detail.BudgetItem.Subject.Type,
					}
				}
			}
		}
		response.Details = details
	}

	return response
}

// CreateExpenseApplication 创建报销申请
func (s *ExpenseService) CreateExpenseApplication(req *dto.CreateExpenseApplicationRequest, userID uuid.UUID) (*dto.ExpenseApplicationResponse, error) {
	// 获取用户信息以获取部门ID
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return nil, errors.New("用户不存在")
	}

	// 验证事前申请（如果有）
	if req.PreApplicationID != nil {
		var preApp models.PreApplication
		if err := s.db.First(&preApp, *req.PreApplicationID).Error; err != nil {
			return nil, errors.New("事前申请不存在")
		}
		if preApp.Status != "APPROVED" {
			return nil, errors.New("事前申请未审批通过")
		}
	}

	// 计算总金额
	totalAmount := decimal.Zero
	for _, detail := range req.Details {
		totalAmount = totalAmount.Add(detail.Amount)
	}

	// 生成申请单号
	applicationCode, err := s.generateExpenseApplicationCode()
	if err != nil {
		return nil, err
	}

	// 开启事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建报销申请主表
	application := models.ExpenseApplication{
		ApplicationCode:  applicationCode,
		ApplicantID:      userID,
		DepartmentID:     user.DepartmentID,
		PreApplicationID: req.PreApplicationID,
		Title:            req.Title,
		TotalAmount:      totalAmount,
		Status:           "DRAFT",
	}
	application.CreatedBy = &userID

	if err := tx.Create(&application).Error; err != nil {
		tx.Rollback()
		logger.Error("Failed to create expense application:", err)
		return nil, err
	}

	// 创建报销明细并冻结预算
	for _, detailReq := range req.Details {
		// 验证预算明细是否存在
		var budgetItem models.BudgetItem
		if err := tx.First(&budgetItem, detailReq.BudgetItemID).Error; err != nil {
			tx.Rollback()
			return nil, errors.New("预算明细不存在")
		}

		// 检查预算余额
		availableAmount := budgetItem.TotalAmount.Sub(budgetItem.UsedAmount).Sub(budgetItem.FrozenAmount)
		if detailReq.Amount.GreaterThan(availableAmount) {
			tx.Rollback()
			return nil, errors.New("预算余额不足")
		}

		// 冻结预算额度
		budgetItem.FrozenAmount = budgetItem.FrozenAmount.Add(detailReq.Amount)
		if err := tx.Save(&budgetItem).Error; err != nil {
			tx.Rollback()
			return nil, err
		}

		// 解析费用日期
		expenseDate, err := time.Parse("2006-01-02", detailReq.ExpenseDate)
		if err != nil {
			tx.Rollback()
			return nil, errors.New("费用日期格式错误")
		}

		// 创建报销明细
		detail := models.ExpenseDetail{
			ApplicationID: application.ID,
			BudgetItemID:  detailReq.BudgetItemID,
			Description:   detailReq.Description,
			Amount:        detailReq.Amount,
			ExpenseDate:   expenseDate,
			InvoiceInfo:   (*models.JSONMap)(&detailReq.InvoiceInfo),
		}

		if err := tx.Create(&detail).Error; err != nil {
			tx.Rollback()
			logger.Error("Failed to create expense detail:", err)
			return nil, err
		}
	}

	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// 获取完整数据
	response, err := s.GetExpenseApplication(application.ID)
	if err != nil {
		return nil, err
	}

	return response, nil
}

// UpdateExpenseApplication 更新报销申请
func (s *ExpenseService) UpdateExpenseApplication(id uuid.UUID, req *dto.UpdateExpenseApplicationRequest, userID uuid.UUID) (*dto.ExpenseApplicationResponse, error) {
	var application models.ExpenseApplication
	if err := s.db.Preload("Details").First(&application, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("报销申请不存在")
		}
		return nil, err
	}

	// 检查是否可以修改
	if application.Status != "DRAFT" {
		return nil, errors.New("只能修改草稿状态的申请")
	}

	// 检查权限
	if application.ApplicantID != userID {
		return nil, errors.New("只能修改自己的申请")
	}

	// 开启事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 如果更新明细，需要先释放原有冻结的预算
	if req.Details != nil {
		// 释放原有冻结的预算
		for _, detail := range application.Details {
			var budgetItem models.BudgetItem
			if err := tx.First(&budgetItem, detail.BudgetItemID).Error; err != nil {
				tx.Rollback()
				return nil, err
			}
			budgetItem.FrozenAmount = budgetItem.FrozenAmount.Sub(detail.Amount)
			if err := tx.Save(&budgetItem).Error; err != nil {
				tx.Rollback()
				return nil, err
			}
		}

		// 删除原有明细
		if err := tx.Where("application_id = ?", id).Delete(&models.ExpenseDetail{}).Error; err != nil {
			tx.Rollback()
			return nil, err
		}

		// 计算新的总金额
		totalAmount := decimal.Zero
		for _, detail := range req.Details {
			totalAmount = totalAmount.Add(detail.Amount)
		}
		application.TotalAmount = totalAmount

		// 创建新的明细并冻结预算
		for _, detailReq := range req.Details {
			// 验证预算明细是否存在
			var budgetItem models.BudgetItem
			if err := tx.First(&budgetItem, detailReq.BudgetItemID).Error; err != nil {
				tx.Rollback()
				return nil, errors.New("预算明细不存在")
			}

			// 检查预算余额
			availableAmount := budgetItem.TotalAmount.Sub(budgetItem.UsedAmount).Sub(budgetItem.FrozenAmount)
			if detailReq.Amount.GreaterThan(availableAmount) {
				tx.Rollback()
				return nil, errors.New("预算余额不足")
			}

			// 冻结预算额度
			budgetItem.FrozenAmount = budgetItem.FrozenAmount.Add(detailReq.Amount)
			if err := tx.Save(&budgetItem).Error; err != nil {
				tx.Rollback()
				return nil, err
			}

			// 解析费用日期
			expenseDate, err := time.Parse("2006-01-02", detailReq.ExpenseDate)
			if err != nil {
				tx.Rollback()
				return nil, errors.New("费用日期格式错误")
			}

			// 创建报销明细
			detail := models.ExpenseDetail{
				ApplicationID: application.ID,
				BudgetItemID:  detailReq.BudgetItemID,
				Description:   detailReq.Description,
				Amount:        detailReq.Amount,
				ExpenseDate:   expenseDate,
				InvoiceInfo:   (*models.JSONMap)(&detailReq.InvoiceInfo),
			}

			if err := tx.Create(&detail).Error; err != nil {
				tx.Rollback()
				logger.Error("Failed to create expense detail:", err)
				return nil, err
			}
		}
	}

	// 更新其他字段
	if req.PreApplicationID != nil {
		// 验证事前申请
		var preApp models.PreApplication
		if err := tx.First(&preApp, *req.PreApplicationID).Error; err != nil {
			tx.Rollback()
			return nil, errors.New("事前申请不存在")
		}
		if preApp.Status != "APPROVED" {
			tx.Rollback()
			return nil, errors.New("事前申请未审批通过")
		}
		application.PreApplicationID = req.PreApplicationID
	}
	if req.Title != nil {
		application.Title = *req.Title
	}
	application.UpdatedBy = &userID

	if err := tx.Save(&application).Error; err != nil {
		tx.Rollback()
		logger.Error("Failed to update expense application:", err)
		return nil, err
	}

	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// 获取完整数据
	response, err := s.GetExpenseApplication(application.ID)
	if err != nil {
		return nil, err
	}

	return response, nil
}

// SubmitExpenseApplication 提交报销申请
func (s *ExpenseService) SubmitExpenseApplication(id uuid.UUID, userID uuid.UUID) error {
	var application models.ExpenseApplication
	if err := s.db.First(&application, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("报销申请不存在")
		}
		return err
	}

	// 检查状态
	if application.Status != "DRAFT" {
		return errors.New("只能提交草稿状态的申请")
	}

	// 检查权限
	if application.ApplicantID != userID {
		return errors.New("只能提交自己的申请")
	}

	// 更新状态
	application.Status = "PENDING"
	application.UpdatedBy = &userID

	if err := s.db.Save(&application).Error; err != nil {
		logger.Error("Failed to submit expense application:", err)
		return err
	}

	// TODO: 这里应该启动审批流程
	logger.Info("Expense application submitted:", application.ID)

	return nil
}
