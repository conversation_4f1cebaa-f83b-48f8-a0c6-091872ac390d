package services

import (
	"errors"
	"hospital-management/internal/dto"
	"hospital-management/internal/models"
	"hospital-management/pkg/logger"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type MessageService struct {
	db *gorm.DB
}

func NewMessageService(db *gorm.DB) *MessageService {
	return &MessageService{db: db}
}

// CreateMessage 创建消息
func (s *MessageService) CreateMessage(req *dto.CreateMessageRequest, userID uuid.UUID) (*dto.MessageResponse, error) {
	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建消息
	message := models.Message{
		MessageType:  req.MessageType,
		Title:        req.Title,
		Content:      req.Content,
		Priority:     req.Priority,
		Status:       "draft",
		BusinessID:   req.BusinessID,
		BusinessType: req.BusinessType,
	}
	message.CreatedBy = &userID

	// 处理定时发送
	if req.ScheduledAt != nil {
		scheduledTime, err := time.Parse(time.RFC3339, *req.ScheduledAt)
		if err != nil {
			tx.Rollback()
			return nil, errors.New("定时发送时间格式错误")
		}
		message.ScheduledAt = &scheduledTime
	}

	if err := tx.Create(&message).Error; err != nil {
		tx.Rollback()
		logger.Error("Failed to create message:", err)
		return nil, err
	}

	// 创建消息接收者记录
	if len(req.ReceiverIDs) > 0 {
		for _, receiverID := range req.ReceiverIDs {
			receiver := models.MessageReceiver{
				MessageID:  message.ID,
				ReceiverID: receiverID,
				Status:     "unread",
			}
			receiver.CreatedBy = &userID

			if err := tx.Create(&receiver).Error; err != nil {
				tx.Rollback()
				logger.Error("Failed to create message receiver:", err)
				return nil, err
			}
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logger.Error("Failed to commit transaction:", err)
		return nil, err
	}

	// 获取完整的消息信息
	return s.GetMessageByID(message.ID, userID)
}

// GetMessages 获取消息列表
func (s *MessageService) GetMessages(userID uuid.UUID, req *dto.MessageListRequest) ([]dto.MessageResponse, int64, error) {
	var messages []models.Message
	var total int64

	// 构建查询，只获取用户相关的消息
	query := s.db.Model(&models.Message{}).
		Preload("Sender").
		Joins("LEFT JOIN tbl_message_receivers ON tbl_messages.id = tbl_message_receivers.message_id").
		Where("tbl_message_receivers.receiver_id = ? OR tbl_messages.created_by = ?", userID, userID)

	// 搜索条件
	if req.MessageType != "" {
		query = query.Where("tbl_messages.message_type = ?", req.MessageType)
	}
	if req.Priority != "" {
		query = query.Where("tbl_messages.priority = ?", req.Priority)
	}
	if req.Status != "" {
		query = query.Where("tbl_messages.status = ?", req.Status)
	}
	if req.IsRead != nil {
		if *req.IsRead {
			query = query.Where("tbl_message_receivers.status = ?", "read")
		} else {
			query = query.Where("tbl_message_receivers.status = ?", "unread")
		}
	}
	if req.BusinessType != "" {
		query = query.Where("tbl_messages.business_type = ?", req.BusinessType)
	}
	if req.SenderID != nil {
		query = query.Where("tbl_messages.created_by = ?", *req.SenderID)
	}
	if req.Keyword != "" {
		query = query.Where("tbl_messages.title ILIKE ? OR tbl_messages.content ILIKE ?", 
			"%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}
	if req.StartDate != "" {
		query = query.Where("tbl_messages.created_at >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		query = query.Where("tbl_messages.created_at <= ?", req.EndDate+" 23:59:59")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count messages:", err)
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).
		Order("tbl_messages.created_at DESC").Find(&messages).Error; err != nil {
		logger.Error("Failed to get messages:", err)
		return nil, 0, err
	}

	// 转换为响应格式
	responses := make([]dto.MessageResponse, len(messages))
	for i, message := range messages {
		responses[i] = *s.convertToResponse(message, userID)
	}

	return responses, total, nil
}

// GetMessageByID 根据ID获取消息
func (s *MessageService) GetMessageByID(id uuid.UUID, userID uuid.UUID) (*dto.MessageResponse, error) {
	var message models.Message
	if err := s.db.Preload("Sender").First(&message, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("消息不存在")
		}
		logger.Error("Failed to get message:", err)
		return nil, err
	}

	return s.convertToResponse(message, userID), nil
}

// MarkAsRead 标记消息为已读
func (s *MessageService) MarkAsRead(userID uuid.UUID, req *dto.MarkReadRequest) error {
	now := time.Now()
	
	// 批量更新消息接收者状态
	if err := s.db.Model(&models.MessageReceiver{}).
		Where("receiver_id = ? AND message_id IN ?", userID, req.MessageIDs).
		Updates(map[string]interface{}{
			"status":  "read",
			"read_at": &now,
		}).Error; err != nil {
		logger.Error("Failed to mark messages as read:", err)
		return err
	}

	return nil
}

// GetUnreadCount 获取未读消息数量
func (s *MessageService) GetUnreadCount(userID uuid.UUID) (*dto.UnreadCountResponse, error) {
	var totalCount int64
	
	// 获取总未读数量
	if err := s.db.Model(&models.MessageReceiver{}).
		Where("receiver_id = ? AND status = ?", userID, "unread").
		Count(&totalCount).Error; err != nil {
		logger.Error("Failed to count unread messages:", err)
		return nil, err
	}

	response := &dto.UnreadCountResponse{
		TotalCount:     totalCount,
		ByPriority:     make(map[string]int64),
		ByBusinessType: make(map[string]int64),
	}

	// 按消息类型统计
	var typeStats []struct {
		MessageType string
		Count       int64
	}
	if err := s.db.Model(&models.MessageReceiver{}).
		Select("tbl_messages.message_type, COUNT(*) as count").
		Joins("JOIN tbl_messages ON tbl_message_receivers.message_id = tbl_messages.id").
		Where("tbl_message_receivers.receiver_id = ? AND tbl_message_receivers.status = ?", userID, "unread").
		Group("tbl_messages.message_type").
		Scan(&typeStats).Error; err != nil {
		logger.Error("Failed to get message type stats:", err)
		return nil, err
	}

	for _, stat := range typeStats {
		switch stat.MessageType {
		case "notification":
			response.NotificationCount = stat.Count
		case "announcement":
			response.AnnouncementCount = stat.Count
		case "todo":
			response.TodoCount = stat.Count
		case "reminder":
			response.ReminderCount = stat.Count
		}
	}

	// 按优先级统计
	var priorityStats []struct {
		Priority string
		Count    int64
	}
	if err := s.db.Model(&models.MessageReceiver{}).
		Select("tbl_messages.priority, COUNT(*) as count").
		Joins("JOIN tbl_messages ON tbl_message_receivers.message_id = tbl_messages.id").
		Where("tbl_message_receivers.receiver_id = ? AND tbl_message_receivers.status = ?", userID, "unread").
		Group("tbl_messages.priority").
		Scan(&priorityStats).Error; err != nil {
		logger.Error("Failed to get priority stats:", err)
		return nil, err
	}

	for _, stat := range priorityStats {
		response.ByPriority[stat.Priority] = stat.Count
	}

	// 按业务类型统计
	var businessStats []struct {
		BusinessType string
		Count        int64
	}
	if err := s.db.Model(&models.MessageReceiver{}).
		Select("tbl_messages.business_type, COUNT(*) as count").
		Joins("JOIN tbl_messages ON tbl_message_receivers.message_id = tbl_messages.id").
		Where("tbl_message_receivers.receiver_id = ? AND tbl_message_receivers.status = ? AND tbl_messages.business_type IS NOT NULL", userID, "unread").
		Group("tbl_messages.business_type").
		Scan(&businessStats).Error; err != nil {
		logger.Error("Failed to get business type stats:", err)
		return nil, err
	}

	for _, stat := range businessStats {
		response.ByBusinessType[stat.BusinessType] = stat.Count
	}

	return response, nil
}

// SendMessage 发送消息
func (s *MessageService) SendMessage(messageID uuid.UUID, userID uuid.UUID) error {
	var message models.Message
	if err := s.db.First(&message, messageID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("消息不存在")
		}
		return err
	}

	// 检查权限
	if message.CreatedBy == nil || *message.CreatedBy != userID {
		return errors.New("无权限发送该消息")
	}

	// 检查状态
	if message.Status != "draft" {
		return errors.New("消息已发送")
	}

	// 更新消息状态
	now := time.Now()
	if err := s.db.Model(&message).Updates(map[string]interface{}{
		"status":  "sent",
		"sent_at": &now,
	}).Error; err != nil {
		logger.Error("Failed to send message:", err)
		return err
	}

	return nil
}

// BroadcastMessage 广播消息
func (s *MessageService) BroadcastMessage(req *dto.BroadcastMessageRequest, userID uuid.UUID) (*dto.MessageResponse, error) {
	// 根据目标类型获取接收者列表
	var receiverIDs []uuid.UUID
	
	switch req.TargetType {
	case "all":
		// 获取所有活跃用户
		var users []models.User
		if err := s.db.Where("is_active = ?", true).Find(&users).Error; err != nil {
			return nil, err
		}
		for _, user := range users {
			receiverIDs = append(receiverIDs, user.ID)
		}
	case "department":
		// 获取指定部门的用户
		if len(req.TargetIDs) == 0 {
			return nil, errors.New("部门ID不能为空")
		}
		var users []models.User
		if err := s.db.Where("department_id IN ? AND is_active = ?", req.TargetIDs, true).Find(&users).Error; err != nil {
			return nil, err
		}
		for _, user := range users {
			receiverIDs = append(receiverIDs, user.ID)
		}
	case "role":
		// 获取指定角色的用户
		if len(req.TargetIDs) == 0 {
			return nil, errors.New("角色ID不能为空")
		}
		var users []models.User
		if err := s.db.Joins("JOIN tbl_user_roles ON tbl_users.id = tbl_user_roles.user_id").
			Where("tbl_user_roles.role_id IN ? AND tbl_users.is_active = ?", req.TargetIDs, true).
			Find(&users).Error; err != nil {
			return nil, err
		}
		for _, user := range users {
			receiverIDs = append(receiverIDs, user.ID)
		}
	default:
		return nil, errors.New("无效的目标类型")
	}

	// 创建消息
	createReq := &dto.CreateMessageRequest{
		MessageType: req.MessageType,
		Title:       req.Title,
		Content:     req.Content,
		Priority:    req.Priority,
		ReceiverIDs: receiverIDs,
		ScheduledAt: req.ScheduledAt,
	}

	message, err := s.CreateMessage(createReq, userID)
	if err != nil {
		return nil, err
	}

	// 如果没有定时发送，立即发送
	if req.ScheduledAt == nil {
		if err := s.SendMessage(message.ID, userID); err != nil {
			return nil, err
		}
	}

	return message, nil
}

// convertToResponse 转换为响应格式
func (s *MessageService) convertToResponse(message models.Message, userID uuid.UUID) *dto.MessageResponse {
	response := &dto.MessageResponse{
		ID:           message.ID,
		MessageType:  message.MessageType,
		Title:        message.Title,
		Content:      message.Content,
		Priority:     message.Priority,
		Status:       message.Status,
		BusinessID:   message.BusinessID,
		BusinessType: message.BusinessType,
		CreatedAt:    message.CreatedAt.Format(time.RFC3339),
		UpdatedAt:    message.UpdatedAt.Format(time.RFC3339),
		CreatedBy:    *message.CreatedBy,
	}

	if message.ScheduledAt != nil {
		scheduledAtStr := message.ScheduledAt.Format(time.RFC3339)
		response.ScheduledAt = &scheduledAtStr
	}

	if message.SentAt != nil {
		sentAtStr := message.SentAt.Format(time.RFC3339)
		response.SentAt = &sentAtStr
	}

	if message.Sender != nil {
		response.Sender = &dto.UserSimpleResponse{
			ID:       message.Sender.ID,
			UserName: message.Sender.UserName,
			JobTitle: message.Sender.JobTitle,
		}
	}

	// 获取当前用户的阅读状态
	var receiver models.MessageReceiver
	if err := s.db.Where("message_id = ? AND receiver_id = ?", message.ID, userID).First(&receiver).Error; err == nil {
		response.ReadStatus = &receiver.Status
		if receiver.ReadAt != nil {
			readAtStr := receiver.ReadAt.Format(time.RFC3339)
			response.ReadAt = &readAtStr
		}
	}

	return response
}
