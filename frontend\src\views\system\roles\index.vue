<template>
  <div class="roles-page">
    <a-card :bordered="false">
      <template #title>
        <span>角色管理</span>
      </template>
      
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon><PlusOutlined /></template>
            新增角色
          </a-button>
          <a-button @click="handleRefresh">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="roleList"
        :loading="loading"
        :pagination="false"
        row-key="id"
        size="middle"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'permissions'">
            <a-space wrap>
              <template v-if="record.permissions && Array.isArray(record.permissions) && record.permissions.length > 0">
                <a-tag v-for="permission in record.permissions.slice(0, 3)" :key="permission" color="blue">
                  {{ permission }}
                </a-tag>
                <a-tag v-if="record.permissions.length > 3" color="default">
                  +{{ record.permissions.length - 3 }}
                </a-tag>
              </template>
              <span v-else class="text-gray-400">暂无权限</span>
            </a-space>
          </template>
          
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)">
                编辑
              </a-button>
              <a-button type="link" size="small" @click="handlePermissions(record)">
                权限配置
              </a-button>
              <a-popconfirm
                title="确定要删除这个角色吗？"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑角色弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :confirm-loading="modalLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="角色名称" name="role_name">
          <a-input v-model:value="formData.role_name" placeholder="请输入角色名称" />
        </a-form-item>

        <a-form-item label="角色编码" name="role_code">
          <a-input
            v-model:value="formData.role_code"
            placeholder="请输入角色编码"
            :disabled="!!editingId"
          />
        </a-form-item>
        
        <a-form-item label="角色描述" name="description">
          <a-textarea
            v-model:value="formData.description"
            placeholder="请输入角色描述"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 权限配置弹窗 -->
    <a-modal
      v-model:open="permissionModalVisible"
      title="权限配置"
      :confirm-loading="permissionLoading"
      width="800px"
      @ok="handlePermissionSubmit"
      @cancel="permissionModalVisible = false"
    >
      <div class="permission-config">
        <a-tree
          v-model:checkedKeys="checkedPermissions"
          :tree-data="permissionTreeData"
          checkable
          :default-expand-all="true"
          :check-strictly="false"
        />
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import {
  getRoles,
  createRole,
  updateRole,
  deleteRole,
  assignPermissions,
  type Role,
  type RoleForm
} from '@/api/system'
import type { Rule } from 'ant-design-vue/es/form'
import type { FormInstance } from 'ant-design-vue'

const loading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const permissionModalVisible = ref(false)
const permissionLoading = ref(false)
const formRef = ref<FormInstance>()
const editingId = ref<number | null>(null)
const currentRole = ref<Role | null>(null)

const roleList = ref<Role[]>([])
const checkedPermissions = ref<string[]>([])

const formData = reactive<RoleForm>({
  role_name: '',
  role_code: '',
  description: '',
  is_active: true,
  permissions: []
})

// 表格列配置
const columns = [
  {
    title: '角色名称',
    dataIndex: 'role_name',
    key: 'role_name',
    width: 150
  },
  {
    title: '角色编码',
    dataIndex: 'role_code',
    key: 'role_code',
    width: 150
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true
  },
  {
    title: '权限',
    key: 'permissions',
    width: 300
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 表单验证规则
const rules: Record<string, Rule[]> = {
  role_name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' }
  ],
  role_code: [
    { required: false, message: '请输入角色编码', trigger: 'blur' },
    { pattern: /^[A-Z_]*$/, message: '角色编码只能包含大写字母和下划线', trigger: 'blur' }
  ]
}

// 弹窗标题
const modalTitle = computed(() => {
  return editingId.value ? '编辑角色' : '新增角色'
})

// 权限树数据（模拟数据，实际应从后端获取）
const permissionTreeData = ref([
  {
    title: '系统管理',
    key: 'system',
    children: [
      { title: '部门管理', key: 'system:department' },
      { title: '用户管理', key: 'system:user' },
      { title: '角色管理', key: 'system:role' }
    ]
  },
  {
    title: '预算管理',
    key: 'budget',
    children: [
      { title: '预算方案', key: 'budget:scheme' },
      { title: '预算科目', key: 'budget:subject' },
      { title: '预算明细', key: 'budget:item' }
    ]
  },
  {
    title: '支出控制',
    key: 'expense',
    children: [
      { title: '事前申请', key: 'expense:pre-application' },
      { title: '费用报销', key: 'expense:application' },
      { title: '审批工作台', key: 'expense:approval' },
      { title: '付款管理', key: 'expense:payment' }
    ]
  },
  {
    title: '采购管理',
    key: 'procurement',
    children: [
      { title: '供应商管理', key: 'procurement:supplier' },
      { title: '采购申请', key: 'procurement:requisition' }
    ]
  },
  {
    title: '合同管理',
    key: 'contract',
    children: [
      { title: '合同台账', key: 'contract:contract' },
      { title: '付款计划', key: 'contract:payment-schedule' }
    ]
  },
  {
    title: '资产管理',
    key: 'asset',
    children: [
      { title: '资产分类', key: 'asset:category' },
      { title: '资产台账', key: 'asset:asset' }
    ]
  }
])

// 加载角色数据
const loadRoles = async () => {
  loading.value = true
  try {
    const response = await getRoles()
    console.log('角色数据响应:', response)
    // 检查响应格式，如果是分页格式则提取list，否则直接使用
    roleList.value = Array.isArray(response) ? response : response.list || []
  } catch (error) {
    console.error('加载角色数据失败:', error)
    message.error('加载角色数据失败')
  } finally {
    loading.value = false
  }
}

// 新增角色
const handleAdd = () => {
  editingId.value = null
  resetForm()
  modalVisible.value = true
}

// 编辑角色
const handleEdit = (record: Role) => {
  editingId.value = record.id
  Object.assign(formData, {
    role_name: record.role_name,
    role_code: record.role_code || '',
    description: record.description || '',
    is_active: record.is_active,
    permissions: record.permissions || []
  })
  modalVisible.value = true
}

// 删除角色
const handleDelete = async (record: Role) => {
  try {
    await deleteRole(record.id)
    message.success('删除成功')
    loadRoles()
  } catch (error) {
    message.error('删除失败')
  }
}

// 权限配置
const handlePermissions = (record: Role) => {
  currentRole.value = record
  checkedPermissions.value = record.permissions || []
  permissionModalVisible.value = true
}

// 刷新数据
const handleRefresh = () => {
  loadRoles()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    modalLoading.value = true
    
    if (editingId.value) {
      await updateRole(editingId.value, formData)
      message.success('更新成功')
    } else {
      await createRole(formData)
      message.success('创建成功')
    }
    
    modalVisible.value = false
    loadRoles()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    modalLoading.value = false
  }
}

// 提交权限配置
const handlePermissionSubmit = async () => {
  if (!currentRole.value) return
  
  permissionLoading.value = true
  try {
    // 这里需要将权限key转换为权限ID，实际实现时需要维护权限映射关系
    const permissionIds = checkedPermissions.value.map(key => parseInt(key.replace(/\D/g, '')) || 1)
    await assignPermissions(currentRole.value.id, permissionIds)
    message.success('权限配置成功')
    permissionModalVisible.value = false
    loadRoles()
  } catch (error) {
    message.error('权限配置失败')
  } finally {
    permissionLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    role_name: '',
    role_code: '',
    description: '',
    is_active: true,
    permissions: []
  })
  formRef.value?.resetFields()
}

onMounted(() => {
  loadRoles()
})
</script>

<style scoped>
.roles-page {
  padding: 24px;
}

.permission-config {
  max-height: 400px;
  overflow-y: auto;
}
</style>
