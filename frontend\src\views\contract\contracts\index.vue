<template>
  <div class="contracts-page">
    <a-card :bordered="false">
      <template #title>
        <span>合同台账管理</span>
      </template>

      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <PlusOutlined />
            </template>
            新增合同
          </a-button>
          <a-button @click="handleRefresh">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 搜索区域 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="关键词">
            <a-input v-model:value="searchForm.keyword" placeholder="合同编号/标题/乙方" allow-clear style="width: 200px" />
          </a-form-item>

          <a-form-item label="合同类型">
            <a-select v-model:value="searchForm.type" placeholder="请选择类型" allow-clear style="width: 120px">
              <a-select-option value="purchase">采购合同</a-select-option>
              <a-select-option value="service">服务合同</a-select-option>
              <a-select-option value="lease">租赁合同</a-select-option>
              <a-select-option value="other">其他合同</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="合同状态">
            <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear style="width: 120px">
              <a-select-option value="draft">草稿</a-select-option>
              <a-select-option value="signed">已签署</a-select-option>
              <a-select-option value="executing">执行中</a-select-option>
              <a-select-option value="completed">已完成</a-select-option>
              <a-select-option value="terminated">已终止</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="签署时间">
            <a-range-picker v-model:value="dateRange" style="width: 240px" @change="handleDateChange" />
          </a-form-item>

          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon>
                  <SearchOutlined />
                </template>
                搜索
              </a-button>
              <a-button @click="handleReset">
                <template #icon>
                  <ReloadOutlined />
                </template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 合同列表 -->
      <a-table :columns="columns" :data-source="contractList" :loading="loading" :pagination="pagination" row-key="id"
        size="middle" @change="handleTableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'contract_no'">
            <a @click="handleView(record)">{{ record.contract_no }}</a>
          </template>

          <template v-else-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeText(record.type) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'contract_amount'">
            <span class="amount-text">{{ formatAmount(record.contract_amount) }}</span>
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleView(record)">
                查看
              </a-button>
              <a-button type="link" size="small" @click="handleEdit(record)">
                编辑
              </a-button>
              <a-button type="link" size="small" @click="handlePaymentSchedule(record)">
                付款计划
              </a-button>
              <a-dropdown>
                <a-button type="link" size="small">
                  更多
                  <DownOutlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="sign" @click="handleUpdateStatus(record, 'signed')"
                      v-if="record.status === 'draft'">
                      标记为已签署
                    </a-menu-item>
                    <a-menu-item key="execute" @click="handleUpdateStatus(record, 'executing')"
                      v-if="record.status === 'signed'">
                      开始执行
                    </a-menu-item>
                    <a-menu-item key="complete" @click="handleUpdateStatus(record, 'completed')"
                      v-if="record.status === 'executing'">
                      标记为完成
                    </a-menu-item>
                    <a-menu-item key="terminate" @click="handleUpdateStatus(record, 'terminated')"
                      v-if="['signed', 'executing'].includes(record.status)">
                      终止合同
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" @click="handleDelete(record)" danger>
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑合同弹窗 -->
    <a-modal v-model:open="modalVisible" :title="modalTitle" :confirm-loading="modalLoading" width="1000px"
      @ok="handleSubmit" @cancel="handleCancel">
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="合同标题" name="title">
          <a-input v-model:value="formData.title" placeholder="请输入合同标题" />
        </a-form-item>

        <a-form-item label="合同类型" name="type">
          <a-radio-group v-model:value="formData.type">
            <a-radio value="purchase">采购合同</a-radio>
            <a-radio value="service">服务合同</a-radio>
            <a-radio value="lease">租赁合同</a-radio>
            <a-radio value="other">其他合同</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="甲方" name="party_a">
          <a-input v-model:value="formData.party_a" placeholder="请输入甲方名称" />
        </a-form-item>

        <a-form-item label="乙方" name="party_b">
          <a-input v-model:value="formData.party_b" placeholder="请输入乙方名称" />
        </a-form-item>

        <a-form-item label="乙方联系人" name="party_b_contact">
          <a-input v-model:value="formData.party_b_contact" placeholder="请输入乙方联系人" />
        </a-form-item>

        <a-form-item label="联系电话" name="party_b_phone">
          <a-input v-model:value="formData.party_b_phone" placeholder="请输入联系电话" />
        </a-form-item>

        <a-form-item label="合同金额" name="contract_amount">
          <a-input-number v-model:value="formData.contract_amount" :min="0" :precision="2" placeholder="请输入合同金额"
            style="width: 100%" :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/¥\s?|(,*)/g, '')" />
        </a-form-item>

        <a-form-item label="币种" name="currency">
          <a-select v-model:value="formData.currency" placeholder="请选择币种">
            <a-select-option value="CNY">人民币</a-select-option>
            <a-select-option value="USD">美元</a-select-option>
            <a-select-option value="EUR">欧元</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="签署日期" name="sign_date">
          <a-date-picker v-model:value="formData.sign_date" placeholder="请选择签署日期" style="width: 100%" />
        </a-form-item>

        <a-form-item label="开始日期" name="start_date">
          <a-date-picker v-model:value="formData.start_date" placeholder="请选择开始日期" style="width: 100%" />
        </a-form-item>

        <a-form-item label="结束日期" name="end_date">
          <a-date-picker v-model:value="formData.end_date" placeholder="请选择结束日期" style="width: 100%" />
        </a-form-item>

        <a-form-item label="付款方式" name="payment_method">
          <a-select v-model:value="formData.payment_method" placeholder="请选择付款方式">
            <a-select-option value="lump_sum">一次性付款</a-select-option>
            <a-select-option value="installment">分期付款</a-select-option>
            <a-select-option value="monthly">按月付款</a-select-option>
            <a-select-option value="quarterly">按季付款</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="所属部门" name="department_id">
          <a-tree-select v-model:value="formData.department_id" :tree-data="departmentTreeOptions" placeholder="请选择部门"
            tree-default-expand-all />
        </a-form-item>

        <a-form-item label="合同负责人" name="manager_id">
          <a-select v-model:value="formData.manager_id" placeholder="请选择负责人" show-search
            :filter-option="filterUserOption" :options="userOptions" />
        </a-form-item>

        <a-form-item label="合同描述" name="description">
          <a-textarea v-model:value="formData.description" placeholder="请输入合同描述" :rows="3" />
        </a-form-item>

        <a-form-item label="合同附件" name="attachment_urls">
          <a-upload v-model:file-list="fileList" :before-upload="beforeUpload" :remove="handleRemoveFile" multiple>
            <a-button>
              <template #icon>
                <UploadOutlined />
              </template>
              上传附件
            </a-button>
          </a-upload>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 查看详情弹窗 -->
    <a-modal v-model:open="viewModalVisible" title="合同详情" :footer="null" width="1000px">
      <a-descriptions :column="2" bordered v-if="currentRecord">
        <a-descriptions-item label="合同编号" :span="2">
          {{ currentRecord.contract_no }}
        </a-descriptions-item>
        <a-descriptions-item label="合同标题" :span="2">
          {{ currentRecord.title }}
        </a-descriptions-item>
        <a-descriptions-item label="合同类型">
          <a-tag :color="getTypeColor(currentRecord.type)">
            {{ getTypeText(currentRecord.type) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="合同状态">
          <a-tag :color="getStatusColor(currentRecord.status)">
            {{ getStatusText(currentRecord.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="甲方">
          {{ currentRecord.party_a }}
        </a-descriptions-item>
        <a-descriptions-item label="乙方">
          {{ currentRecord.party_b }}
        </a-descriptions-item>
        <a-descriptions-item label="乙方联系人">
          {{ currentRecord.party_b_contact }}
        </a-descriptions-item>
        <a-descriptions-item label="联系电话">
          {{ currentRecord.party_b_phone }}
        </a-descriptions-item>
        <a-descriptions-item label="合同金额">
          <span class="amount-text">{{ formatAmount(currentRecord.contract_amount) }}</span>
        </a-descriptions-item>
        <a-descriptions-item label="币种">
          {{ getCurrencyText(currentRecord.currency) }}
        </a-descriptions-item>
        <a-descriptions-item label="签署日期">
          {{ currentRecord.sign_date }}
        </a-descriptions-item>
        <a-descriptions-item label="开始日期">
          {{ currentRecord.start_date }}
        </a-descriptions-item>
        <a-descriptions-item label="结束日期">
          {{ currentRecord.end_date }}
        </a-descriptions-item>
        <a-descriptions-item label="付款方式">
          {{ getPaymentMethodText(currentRecord.payment_method) }}
        </a-descriptions-item>
        <a-descriptions-item label="所属部门">
          {{ currentRecord.department_name }}
        </a-descriptions-item>
        <a-descriptions-item label="合同负责人">
          {{ currentRecord.manager_name }}
        </a-descriptions-item>
        <a-descriptions-item label="合同描述" :span="2" v-if="currentRecord.description">
          {{ currentRecord.description }}
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">
          {{ currentRecord.created_at }}
        </a-descriptions-item>
        <a-descriptions-item label="更新时间">
          {{ currentRecord.updated_at }}
        </a-descriptions-item>
      </a-descriptions>

      <a-divider v-if="currentRecord.attachment_urls && currentRecord.attachment_urls.length > 0">
        合同附件
      </a-divider>

      <div v-if="currentRecord.attachment_urls && currentRecord.attachment_urls.length > 0" class="attachment-list">
        <a-list :data-source="currentRecord.attachment_urls" size="small">
          <template #renderItem="{ item }">
            <a-list-item>
              <a :href="item" target="_blank">{{ getFileName(item) }}</a>
            </a-list-item>
          </template>
        </a-list>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
  DownOutlined,
  UploadOutlined
} from '@ant-design/icons-vue'
import dayjs, { type Dayjs } from 'dayjs'
import {
  getContracts,
  createContract,
  updateContract,
  deleteContract,
  updateContractStatus,
  uploadContractFile,
  type Contract,
  type ContractForm,
  type ContractQuery
} from '@/api/contract'
import { getDepartmentTree, getUsers, type Department, type User } from '@/api/system'
import type { Rule } from 'ant-design-vue/es/form'
import type { FormInstance, UploadFile } from 'ant-design-vue'

const loading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const viewModalVisible = ref(false)
const formRef = ref<FormInstance>()
const editingId = ref<number | null>(null)

const contractList = ref<Contract[]>([])
const departmentList = ref<Department[]>([])
const userList = ref<User[]>([])
const currentRecord = ref<Contract | null>(null)
const dateRange = ref<[Dayjs, Dayjs] | null>(null)
const fileList = ref<UploadFile[]>([])

const searchForm = reactive<ContractQuery>({
  keyword: '',
  type: undefined,
  status: undefined,
  start_date: undefined,
  end_date: undefined,
  page: 1,
  page_size: 10
})

const formData = reactive<ContractForm & {
  sign_date: Dayjs | null
  start_date: Dayjs | null
  end_date: Dayjs | null
}>({
  title: '',
  type: 'purchase',
  party_a: '',
  party_b: '',
  party_b_contact: '',
  party_b_phone: '',
  contract_amount: 0,
  currency: 'CNY',
  sign_date: null,
  start_date: null,
  end_date: null,
  payment_method: 'lump_sum',
  department_id: 0,
  manager_id: 0,
  description: '',
  attachment_urls: []
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '合同编号',
    key: 'contract_no',
    width: 150
  },
  {
    title: '合同标题',
    dataIndex: 'title',
    key: 'title',
    width: 200,
    ellipsis: true
  },
  {
    title: '合同类型',
    key: 'type',
    width: 100
  },
  {
    title: '乙方',
    dataIndex: 'party_b',
    key: 'party_b',
    width: 150,
    ellipsis: true
  },
  {
    title: '合同金额',
    key: 'contract_amount',
    width: 120,
    align: 'right'
  },
  {
    title: '签署日期',
    dataIndex: 'sign_date',
    key: 'sign_date',
    width: 120
  },
  {
    title: '开始日期',
    dataIndex: 'start_date',
    key: 'start_date',
    width: 120
  },
  {
    title: '结束日期',
    dataIndex: 'end_date',
    key: 'end_date',
    width: 120
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '负责人',
    dataIndex: 'manager_name',
    key: 'manager_name',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 表单验证规则
const rules: Record<string, Rule[]> = {
  title: [
    { required: true, message: '请输入合同标题', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择合同类型', trigger: 'change' }
  ],
  party_a: [
    { required: true, message: '请输入甲方名称', trigger: 'blur' }
  ],
  party_b: [
    { required: true, message: '请输入乙方名称', trigger: 'blur' }
  ],
  party_b_contact: [
    { required: true, message: '请输入乙方联系人', trigger: 'blur' }
  ],
  party_b_phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  contract_amount: [
    { required: true, message: '请输入合同金额', trigger: 'blur' }
  ],
  currency: [
    { required: true, message: '请选择币种', trigger: 'change' }
  ],
  sign_date: [
    { required: true, message: '请选择签署日期', trigger: 'change' }
  ],
  start_date: [
    { required: true, message: '请选择开始日期', trigger: 'change' }
  ],
  end_date: [
    { required: true, message: '请选择结束日期', trigger: 'change' }
  ],
  payment_method: [
    { required: true, message: '请选择付款方式', trigger: 'change' }
  ],
  department_id: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ],
  manager_id: [
    { required: true, message: '请选择负责人', trigger: 'change' }
  ]
}

// 弹窗标题
const modalTitle = computed(() => {
  return editingId.value ? '编辑合同' : '新增合同'
})

// 部门树形选择器选项
const departmentTreeOptions = computed(() => {
  const convertToTreeData = (departments: Department[]): any[] => {
    return departments.map(dept => ({
      title: dept.name,
      value: dept.id,
      key: dept.id,
      children: dept.children ? convertToTreeData(dept.children) : undefined
    }))
  }
  return convertToTreeData(departmentList.value)
})

// 用户选项
const userOptions = computed(() => {
  return userList.value.map(user => ({
    label: `${user.name} (${user.department_name})`,
    value: user.id
  }))
})
</script>
