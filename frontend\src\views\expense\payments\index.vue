<template>
  <div class="payments-page">
    <a-card :bordered="false">
      <template #title>
        <span>付款管理</span>
      </template>

      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <PlusOutlined />
            </template>
            新建付款
          </a-button>
          <a-button @click="handleBatchPayment" :disabled="selectedRowKeys.length === 0">
            <template #icon>
              <DollarOutlined />
            </template>
            批量付款
          </a-button>
          <a-button @click="handleRefresh">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 搜索区域 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="付款状态">
            <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear style="width: 120px">
              <a-select-option value="PENDING">待付款</a-select-option>
              <a-select-option value="PAID">已付款</a-select-option>
              <a-select-option value="FAILED">付款失败</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="收款方类型">
            <a-select v-model:value="searchForm.payee_type" placeholder="请选择类型" allow-clear style="width: 120px">
              <a-select-option value="employee">员工</a-select-option>
              <a-select-option value="supplier">供应商</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="付款方式">
            <a-select v-model:value="searchForm.payment_method" placeholder="请选择方式" allow-clear style="width: 120px">
              <a-select-option value="bank_transfer">银行转账</a-select-option>
              <a-select-option value="cash">现金</a-select-option>
              <a-select-option value="check">支票</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="付款时间">
            <a-range-picker v-model:value="dateRange" style="width: 240px" @change="handleDateChange" />
          </a-form-item>

          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon>
                  <SearchOutlined />
                </template>
                搜索
              </a-button>
              <a-button @click="handleReset">
                <template #icon>
                  <ReloadOutlined />
                </template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 付款列表 -->
      <a-table :columns="columns" :data-source="paymentList" :loading="loading" :pagination="pagination"
        :row-selection="rowSelection" row-key="id" size="middle" @change="handleTableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'payment_no'">
            <a @click="handleView(record)">{{ record.payment_no }}</a>
          </template>

          <template v-else-if="column.key === 'payee_type'">
            <a-tag :color="record.payee_type === 'employee' ? 'blue' : 'green'">
              {{ record.payee_type === 'employee' ? '员工' : '供应商' }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'amount'">
            <span class="amount-text">{{ formatAmount(record.amount) }}</span>
          </template>

          <template v-else-if="column.key === 'payment_method'">
            <a-tag :color="getPaymentMethodColor(record.payment_method)">
              {{ getPaymentMethodText(record.payment_method) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleView(record)">
                查看
              </a-button>
              <a-button type="link" size="small" @click="handleEdit(record)" v-if="record.status === 'PENDING'">
                编辑
              </a-button>
              <a-dropdown v-if="record.status !== 'PAID'">
                <a-button type="link" size="small">
                  更多
                  <DownOutlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="paid" @click="handleUpdateStatus(record, 'PAID')">
                      标记为已付款
                    </a-menu-item>
                    <a-menu-item key="failed" @click="handleUpdateStatus(record, 'FAILED')">
                      标记为失败
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑付款弹窗 -->
    <a-modal v-model:open="modalVisible" :title="modalTitle" :confirm-loading="modalLoading" width="800px"
      @ok="handleModalSubmit" @cancel="handleCancel">
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="收款方类型" name="payee_type">
          <a-radio-group v-model:value="formData.payee_type">
            <a-radio value="employee">员工</a-radio>
            <a-radio value="supplier">供应商</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="收款方" name="payee_id">
          <a-select v-model:value="formData.payee_id" placeholder="请选择收款方" show-search
            :filter-option="filterPayeeOption" :options="payeeOptions" />
        </a-form-item>

        <a-form-item label="付款金额" name="amount">
          <a-input-number v-model:value="formData.amount" :min="0" :precision="2" placeholder="请输入付款金额"
            style="width: 100%" :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/¥\s?|(,*)/g, '')" />
        </a-form-item>

        <a-form-item label="付款方式" name="payment_method">
          <a-select v-model:value="formData.payment_method" placeholder="请选择付款方式">
            <a-select-option value="bank_transfer">银行转账</a-select-option>
            <a-select-option value="cash">现金</a-select-option>
            <a-select-option value="check">支票</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="银行账号" name="bank_account" v-if="formData.payment_method === 'bank_transfer'">
          <a-input v-model:value="formData.bank_account" placeholder="请输入银行账号" />
        </a-form-item>

        <a-form-item label="开户银行" name="bank_name" v-if="formData.payment_method === 'bank_transfer'">
          <a-input v-model:value="formData.bank_name" placeholder="请输入开户银行" />
        </a-form-item>

        <a-form-item label="业务类型" name="business_type">
          <a-select v-model:value="formData.business_type" placeholder="请选择业务类型">
            <a-select-option value="expense_application">费用报销</a-select-option>
            <a-select-option value="procurement">采购付款</a-select-option>
            <a-select-option value="contract">合同付款</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="关联业务" name="business_id">
          <a-input-number v-model:value="formData.business_id" placeholder="请输入关联业务ID" style="width: 100%" />
        </a-form-item>

        <a-form-item label="预计付款日期" name="payment_date">
          <a-date-picker v-model:value="formData.payment_date" placeholder="请选择预计付款日期" style="width: 100%" />
        </a-form-item>

        <a-form-item label="备注说明" name="remark">
          <a-textarea v-model:value="formData.remark" placeholder="请输入备注说明" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 查看详情弹窗 -->
    <a-modal v-model:open="viewModalVisible" title="付款详情" :footer="null" width="800px">
      <a-descriptions :column="2" bordered v-if="currentRecord">
        <a-descriptions-item label="付款单号" :span="2">
          {{ currentRecord.payment_no }}
        </a-descriptions-item>
        <a-descriptions-item label="收款方类型">
          <a-tag :color="currentRecord.payee_type === 'employee' ? 'blue' : 'green'">
            {{ currentRecord.payee_type === 'employee' ? '员工' : '供应商' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="收款方">
          {{ currentRecord.payee_name }}
        </a-descriptions-item>
        <a-descriptions-item label="付款金额">
          <span class="amount-text">{{ formatAmount(currentRecord.amount) }}</span>
        </a-descriptions-item>
        <a-descriptions-item label="付款方式">
          <a-tag :color="getPaymentMethodColor(currentRecord.payment_method)">
            {{ getPaymentMethodText(currentRecord.payment_method) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="银行账号" v-if="currentRecord.bank_account">
          {{ currentRecord.bank_account }}
        </a-descriptions-item>
        <a-descriptions-item label="开户银行" v-if="currentRecord.bank_name">
          {{ currentRecord.bank_name }}
        </a-descriptions-item>
        <a-descriptions-item label="业务类型">
          {{ currentRecord.business_type }}
        </a-descriptions-item>
        <a-descriptions-item label="关联业务">
          {{ currentRecord.business_title }}
        </a-descriptions-item>
        <a-descriptions-item label="付款状态">
          <a-tag :color="getStatusColor(currentRecord.status)">
            {{ getStatusText(currentRecord.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="付款日期" v-if="currentRecord.payment_date">
          {{ currentRecord.payment_date }}
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">
          {{ currentRecord.created_at }}
        </a-descriptions-item>
        <a-descriptions-item label="备注说明" :span="2" v-if="currentRecord.remark">
          {{ currentRecord.remark }}
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  DollarOutlined,
  ReloadOutlined,
  SearchOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import dayjs, { type Dayjs } from 'dayjs'
import {
  getPayments,
  createPayment,
  updatePaymentStatus,
  batchPayment,
  type Payment,
  type PaymentForm,
  type PaymentQuery
} from '@/api/expense'
import { getUsers, type User } from '@/api/system'
import type { Rule } from 'ant-design-vue/es/form'
import type { FormInstance } from 'ant-design-vue'

const loading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const viewModalVisible = ref(false)
const formRef = ref<FormInstance>()
const editingId = ref<number | null>(null)

const paymentList = ref<Payment[]>([])
const userList = ref<User[]>([])
const currentRecord = ref<Payment | null>(null)
const dateRange = ref<[Dayjs, Dayjs] | null>(null)
const selectedRowKeys = ref<number[]>([])

const searchForm = reactive<PaymentQuery>({
  status: undefined,
  payee_type: undefined,
  payment_method: undefined,
  start_date: undefined,
  end_date: undefined,
  page: 1,
  page_size: 10
})

const formData = reactive<PaymentForm & {
  payment_date: Dayjs | null
}>({
  payee_type: 'employee',
  payee_id: 0,
  bank_account: '',
  bank_name: '',
  amount: 0,
  payment_method: 'bank_transfer',
  business_type: 'expense_application',
  business_id: 0,
  payment_date: null,
  remark: ''
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys
  },
  getCheckboxProps: (record: Payment) => ({
    disabled: record.status === 'completed'
  })
}

// 表格列配置
const columns = [
  {
    title: '付款单号',
    key: 'payment_no',
    width: 150
  },
  {
    title: '收款方类型',
    key: 'payee_type',
    width: 100
  },
  {
    title: '收款方',
    dataIndex: 'payee_name',
    key: 'payee_name',
    width: 150
  },
  {
    title: '付款金额',
    key: 'amount',
    width: 120,
    align: 'right'
  },
  {
    title: '付款方式',
    key: 'payment_method',
    width: 100
  },
  {
    title: '关联业务',
    dataIndex: 'business_title',
    key: 'business_title',
    width: 150,
    ellipsis: true
  },
  {
    title: '付款状态',
    key: 'status',
    width: 100
  },
  {
    title: '付款日期',
    dataIndex: 'payment_date',
    key: 'payment_date',
    width: 120
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right'
  }
]

// 表单验证规则
const rules: Record<string, Rule[]> = {
  payee_type: [
    { required: true, message: '请选择收款方类型', trigger: 'change' }
  ],
  payee_id: [
    { required: true, message: '请选择收款方', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入付款金额', trigger: 'blur' }
  ],
  payment_method: [
    { required: true, message: '请选择付款方式', trigger: 'change' }
  ],
  business_type: [
    { required: true, message: '请选择业务类型', trigger: 'change' }
  ],
  business_id: [
    { required: true, message: '请输入关联业务ID', trigger: 'blur' }
  ]
}

// 弹窗标题
const modalTitle = computed(() => {
  return editingId.value ? '编辑付款单' : '新建付款单'
})

// 收款方选项
const payeeOptions = computed(() => {
  if (formData.payee_type === 'employee') {
    return userList.value.map(user => ({
      label: `${user.name} (${user.department_name})`,
      value: user.id
    }))
  } else {
    // TODO: 这里应该是供应商列表，暂时使用用户列表
    return userList.value.map(user => ({
      label: user.name,
      value: user.id
    }))
  }
})

// 获取付款方式颜色
const getPaymentMethodColor = (method: string) => {
  const colorMap: Record<string, string> = {
    'bank_transfer': 'blue',
    'cash': 'green',
    'check': 'orange'
  }
  return colorMap[method] || 'default'
}

// 获取付款方式文本
const getPaymentMethodText = (method: string) => {
  const textMap: Record<string, string> = {
    'bank_transfer': '银行转账',
    'cash': '现金',
    'check': '支票'
  }
  return textMap[method] || method
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'PENDING': 'orange',
    'PAID': 'green',
    'FAILED': 'red'
  }
  return colorMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'PENDING': '待付款',
    'PAID': '已付款',
    'FAILED': '付款失败'
  }
  return textMap[status] || status
}

// 格式化金额
const formatAmount = (amount: number) => {
  return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`
}

// 收款方筛选
const filterPayeeOption = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase())
}

// 加载付款数据
const loadPayments = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.current,
      page_size: pagination.pageSize
    }
    const response = await getPayments(params)
    paymentList.value = response.list
    pagination.total = response.total
  } catch (error) {
    message.error('加载付款数据失败')
  } finally {
    loading.value = false
  }
}

// 加载用户数据
const loadUsers = async () => {
  try {
    const response = await getUsers({ page: 1, page_size: 100 })
    userList.value = response.list
  } catch (error) {
    message.error('加载用户数据失败')
  }
}

// 日期范围变化处理
const handleDateChange = (dates: [Dayjs, Dayjs] | null) => {
  if (dates) {
    searchForm.start_date = dates[0].format('YYYY-MM-DD')
    searchForm.end_date = dates[1].format('YYYY-MM-DD')
  } else {
    searchForm.start_date = undefined
    searchForm.end_date = undefined
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadPayments()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    status: undefined,
    payee_type: undefined,
    payment_method: undefined,
    start_date: undefined,
    end_date: undefined
  })
  dateRange.value = null
  pagination.current = 1
  loadPayments()
}

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadPayments()
}

// 新建付款
const handleAdd = () => {
  editingId.value = null
  resetForm()
  modalVisible.value = true
}

// 编辑付款
const handleEdit = (record: Payment) => {
  editingId.value = record.id
  Object.assign(formData, {
    payee_type: record.payee_type,
    payee_id: record.payee_id,
    bank_account: record.bank_account,
    bank_name: record.bank_name,
    amount: record.amount,
    payment_method: record.payment_method,
    business_type: record.business_type,
    business_id: record.business_id,
    payment_date: record.payment_date ? dayjs(record.payment_date) : null,
    remark: record.remark
  })
  modalVisible.value = true
}

// 查看详情
const handleView = (record: Payment) => {
  currentRecord.value = record
  viewModalVisible.value = true
}

// 更新付款状态
const handleUpdateStatus = (record: Payment, status: string) => {
  Modal.confirm({
    title: '确认操作',
    content: `确定要将付款状态更新为"${getStatusText(status)}"吗？`,
    onOk: async () => {
      try {
        await updatePaymentStatus(record.id, status)
        message.success('状态更新成功')
        loadPayments()
      } catch (error) {
        message.error('状态更新失败')
      }
    }
  })
}

// 批量付款
const handleBatchPayment = () => {
  if (selectedRowKeys.value.length === 0) {
    message.error('请选择要付款的记录')
    return
  }

  Modal.confirm({
    title: '确认批量付款',
    content: `确定要对选中的 ${selectedRowKeys.value.length} 条记录进行批量付款吗？`,
    onOk: async () => {
      try {
        await batchPayment(selectedRowKeys.value)
        message.success('批量付款成功')
        selectedRowKeys.value = []
        loadPayments()
      } catch (error) {
        message.error('批量付款失败')
      }
    }
  })
}

// 刷新数据
const handleRefresh = () => {
  loadPayments()
}

// 提交表单
const handleModalSubmit = async () => {
  try {
    await formRef.value?.validate()
    modalLoading.value = true

    const submitData = {
      payee_type: formData.payee_type,
      payee_id: formData.payee_id,
      bank_account: formData.bank_account,
      bank_name: formData.bank_name,
      amount: formData.amount,
      payment_method: formData.payment_method,
      business_type: formData.business_type,
      business_id: formData.business_id,
      payment_date: formData.payment_date?.format('YYYY-MM-DD'),
      remark: formData.remark
    }

    if (editingId.value) {
      // TODO: 实现更新付款单API
      message.success('更新成功')
    } else {
      await createPayment(submitData)
      message.success('创建成功')
    }

    modalVisible.value = false
    loadPayments()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    modalLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    payee_type: 'employee',
    payee_id: 0,
    bank_account: '',
    bank_name: '',
    amount: 0,
    payment_method: 'bank_transfer',
    business_type: 'expense_application',
    business_id: 0,
    payment_date: null,
    remark: ''
  })
  formRef.value?.resetFields()
}

onMounted(() => {
  loadUsers()
  loadPayments()
})
</script>

<style scoped>
.payments-page {
  padding: 24px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.amount-text {
  font-weight: 500;
  color: #1890ff;
}
</style>
