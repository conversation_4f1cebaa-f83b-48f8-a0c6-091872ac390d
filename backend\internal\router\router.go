package router

import (
	"hospital-management/internal/config"
	"hospital-management/internal/controllers"
	"hospital-management/internal/middleware"
	"hospital-management/internal/services"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"gorm.io/gorm"
)

func Setup(db *gorm.DB, cfg *config.Config) *gin.Engine {
	r := gin.Default()

	// 中间件
	r.Use(middleware.CORS())
	r.Use(middleware.Logger())
	r.Use(middleware.Recovery())

	// Swagger文档
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "Hospital Management System API is running",
		})
	})

	// 初始化服务
	departmentService := services.NewDepartmentService(db)
	userService := services.NewUserService(db)
	roleService := services.NewRoleService(db)
	authService := services.NewAuthService(db, cfg.JWT.Secret)
	approvalService := services.NewApprovalService(db)
	messageService := services.NewMessageService(db)
	fileService := services.NewFileService(db, cfg.Upload.UploadDir, "http://localhost:8080", cfg.Upload.MaxSize)
	budgetService := services.NewBudgetService(db)
	expenseService := services.NewExpenseService(db)
	paymentService := services.NewPaymentService(db)
	procurementService := services.NewProcurementService(db)
	contractService := services.NewContractService(db)
	assetService := services.NewAssetService(db)
	assetDepreciationService := services.NewAssetDepreciationService(db)
	assetMaintenanceService := services.NewAssetMaintenanceService(db)
	commonService := services.NewCommonService(db)

	// 初始化控制器
	departmentController := controllers.NewDepartmentController(departmentService)
	userController := controllers.NewUserController(userService)
	roleController := controllers.NewRoleController(roleService)
	authController := controllers.NewAuthController(authService)
	approvalController := controllers.NewApprovalController(approvalService)
	messageController := controllers.NewMessageController(messageService)
	fileController := controllers.NewFileController(fileService)
	budgetController := controllers.NewBudgetController(budgetService)
	expenseController := controllers.NewExpenseController(expenseService, paymentService)
	procurementController := controllers.NewProcurementController(procurementService)
	contractController := controllers.NewContractController(contractService)
	assetController := controllers.NewAssetController(assetService, assetDepreciationService, assetMaintenanceService)
	commonController := controllers.NewCommonController(commonService)

	// API路由组
	api := r.Group("/api/v1")
	{
		// 认证相关（无需认证）
		auth := api.Group("/auth")
		{
			auth.POST("/login", authController.Login)
			auth.POST("/refresh", authController.RefreshToken)
		}

		// 公开路由（不需要认证）
		api.GET("/health", commonController.HealthCheck)
		api.GET("/version", commonController.GetVersion)

		// 需要认证的认证相关接口
		authProtected := api.Group("/auth")
		authProtected.Use(middleware.JWTAuth(cfg.JWT.Secret))
		{
			authProtected.POST("/logout", authController.Logout)
			authProtected.GET("/profile", authController.GetProfile)
			authProtected.POST("/change-password", authController.ChangePassword)
		}

		// 需要认证的路由
		protected := api.Group("/")
		protected.Use(middleware.JWTAuth(cfg.JWT.Secret))
		{
			// 用户管理
			users := protected.Group("/users")
			{
				users.GET("", userController.GetUsers)
				users.POST("", userController.CreateUser)
				users.GET("/:id", userController.GetUserByID)
				users.PUT("/:id", userController.UpdateUser)
				users.DELETE("/:id", userController.DeleteUser)
				users.POST("/import", userController.ImportUsers)
				users.GET("/export", userController.ExportUsers)
			}

			// 部门管理
			departments := protected.Group("/departments")
			{
				departments.GET("", departmentController.GetDepartments)
				departments.POST("", departmentController.CreateDepartment)
				departments.GET("/tree", departmentController.GetDepartmentTree)
				departments.PUT("/:id", departmentController.UpdateDepartment)
				departments.DELETE("/:id", departmentController.DeleteDepartment)
			}

			// 角色管理
			roles := protected.Group("/roles")
			{
				roles.GET("", roleController.GetRoles)
				roles.POST("", roleController.CreateRole)
				roles.PUT("/:id", roleController.UpdateRole)
				roles.DELETE("/:id", roleController.DeleteRole)
				roles.POST("/:id/permissions", roleController.AssignPermissions)
				roles.GET("/permissions", roleController.GetAvailablePermissions)
			}

			// 消息中心
			messages := protected.Group("/messages")
			{
				messages.GET("", messageController.GetMessages)
				messages.POST("", messageController.CreateMessage)
				messages.PUT("/:id/read", messageController.MarkAsRead)
				messages.GET("/unread-count", messageController.GetUnreadCount)
				messages.POST("/send", messageController.SendMessage)
				messages.POST("/broadcast", messageController.BroadcastMessage)
			}

			// 文件管理
			files := protected.Group("/files")
			{
				files.POST("/upload", fileController.UploadFile)
				files.GET("", fileController.GetFiles)
				files.GET("/:id/download", fileController.DownloadFile)
				files.DELETE("/:id", fileController.DeleteFile)
				files.GET("/business/:businessId", fileController.GetBusinessFiles)
			}

			// 预算管理
			budget := protected.Group("/budget")
			{
				// 预算方案管理
				budget.GET("/schemes", budgetController.GetBudgetSchemes)
				budget.POST("/schemes", budgetController.CreateBudgetScheme)
				budget.PUT("/schemes/:id", budgetController.UpdateBudgetScheme)
				budget.DELETE("/schemes/:id", budgetController.DeleteBudgetScheme)

				// 预算科目管理
				budget.GET("/subjects", budgetController.GetBudgetSubjects)
				budget.POST("/subjects", budgetController.CreateBudgetSubject)
				budget.PUT("/subjects/:id", budgetController.UpdateBudgetSubject)
				budget.GET("/subjects/tree", budgetController.GetBudgetSubjectTree)

				// 预算明细管理
				budget.GET("/items", budgetController.GetBudgetItems)
				budget.POST("/items", budgetController.CreateBudgetItem)
				budget.PUT("/items/:id", budgetController.UpdateBudgetItem)
				budget.POST("/items/batch", budgetController.BatchCreateBudgetItems)

				// 预算控制
				budget.POST("/freeze", budgetController.FreezeBudget)
				budget.POST("/unfreeze", budgetController.UnfreezeBudget)
				budget.POST("/consume", budgetController.ConsumeBudget)
				budget.GET("/balance/:itemId", budgetController.GetBudgetBalance)

				// 预算分析
				budget.GET("/analysis/execution", budgetController.GetBudgetExecutionAnalysis)
				budget.GET("/analysis/department", budgetController.GetBudgetDepartmentAnalysis)
				budget.GET("/analysis/subject", budgetController.GetBudgetSubjectAnalysis)
			}

			// 支出控制管理
			// 事前申请管理
			protected.GET("/pre-applications", expenseController.GetPreApplications)
			protected.GET("/pre-applications/:id", expenseController.GetPreApplication)
			protected.POST("/pre-applications", expenseController.CreatePreApplication)
			protected.PUT("/pre-applications/:id", expenseController.UpdatePreApplication)
			protected.POST("/pre-applications/:id/submit", expenseController.SubmitPreApplication)

			// 费用报销管理
			expense := protected.Group("/expense")
			{
				expense.GET("/applications", expenseController.GetExpenseApplications)
				expense.GET("/applications/:id", expenseController.GetExpenseApplication)
				expense.POST("/applications", expenseController.CreateExpenseApplication)
				expense.PUT("/applications/:id", expenseController.UpdateExpenseApplication)
				expense.POST("/applications/:id/submit", expenseController.SubmitExpenseApplication)
				expense.GET("/applications/my", expenseController.GetMyExpenseApplications)
			}

			// 付款管理
			protected.GET("/payments", expenseController.GetPayments)
			protected.POST("/payments", expenseController.CreatePayment)
			protected.PUT("/payments/:id/status", expenseController.UpdatePaymentStatus)
			protected.POST("/payments/batch", expenseController.BatchPayment)

			// 采购管理
			// 供应商管理
			protected.GET("/suppliers", procurementController.GetSuppliers)
			protected.POST("/suppliers", procurementController.CreateSupplier)
			protected.PUT("/suppliers/:id", procurementController.UpdateSupplier)
			protected.PUT("/suppliers/:id/status", procurementController.UpdateSupplierStatus)

			// 采购申请管理
			purchase := protected.Group("/purchase")
			{
				purchase.GET("/requisitions", procurementController.GetPurchaseRequisitions)
				purchase.GET("/requisitions/:id", procurementController.GetPurchaseRequisition)
				purchase.POST("/requisitions", procurementController.CreatePurchaseRequisition)
				purchase.PUT("/requisitions/:id", procurementController.UpdatePurchaseRequisition)
				purchase.POST("/requisitions/:id/submit", procurementController.SubmitPurchaseRequisition)
			}

			// 合同管理
			protected.GET("/contracts", contractController.GetContracts)
			protected.GET("/contracts/:id", contractController.GetContract)
			protected.POST("/contracts", contractController.CreateContract)
			protected.PUT("/contracts/:id", contractController.UpdateContract)
			protected.PUT("/contracts/:id/status", contractController.UpdateContractStatus)
			protected.DELETE("/contracts/:id", contractController.DeleteContract)

			// 付款计划管理
			protected.GET("/payment-schedules", contractController.GetPaymentSchedules)
			protected.POST("/payment-schedules", contractController.CreatePaymentSchedule)
			protected.PUT("/payment-schedules/:id", contractController.UpdatePaymentSchedule)
			protected.POST("/payment-schedules/batch", contractController.BatchCreatePaymentSchedules)

			// 资产管理
			// 资产分类管理
			asset := protected.Group("/asset")
			{
				asset.GET("/categories", assetController.GetAssetCategories)
				asset.GET("/categories/tree", assetController.GetAssetCategoryTree)
				asset.POST("/categories", assetController.CreateAssetCategory)
				asset.PUT("/categories/:id", assetController.UpdateAssetCategory)
				asset.DELETE("/categories/:id", assetController.DeleteAssetCategory)
			}

			// 资产管理
			protected.GET("/assets", assetController.GetAssets)
			protected.GET("/assets/:id", assetController.GetAsset)
			protected.POST("/assets", assetController.CreateAsset)
			protected.PUT("/assets/:id", assetController.UpdateAsset)
			protected.PUT("/assets/:id/status", assetController.UpdateAssetStatus)
			protected.PUT("/assets/batch/status", assetController.BatchUpdateAssetStatus)
			protected.DELETE("/assets/:id", assetController.DeleteAsset)

			// 资产折旧管理
			protected.GET("/asset/depreciation", assetController.GetDepreciationRecords)
			protected.POST("/asset/depreciation", assetController.CreateDepreciationRecord)
			protected.GET("/asset/depreciation/:id", assetController.GetDepreciationRecord)
			protected.POST("/asset/depreciation/batch-calculate", assetController.BatchCalculateDepreciation)

			// 资产维护管理
			protected.GET("/asset/maintenance", assetController.GetMaintenanceRecords)
			protected.POST("/asset/maintenance", assetController.CreateMaintenanceRecord)
			protected.PUT("/asset/maintenance/:id", assetController.UpdateMaintenanceRecord)
			protected.DELETE("/asset/maintenance/:id", assetController.DeleteMaintenanceRecord)
			protected.GET("/asset/maintenance/:id", assetController.GetMaintenanceRecord)
			protected.GET("/asset/maintenance/upcoming", assetController.GetUpcomingMaintenances)

			// 通用功能
			protected.GET("/dictionaries", commonController.GetDictionaries)
			protected.GET("/dashboard/statistics", commonController.GetDashboardStatistics)
			protected.GET("/system/info", commonController.GetSystemInfo)

			// 报销管理已在上面定义，删除重复定义

			// 审批管理
			approval := protected.Group("/approval")
			{
				approval.POST("/flows", approvalController.CreateApprovalFlow)
				approval.GET("/flows/:id", approvalController.GetApprovalFlowByID)
				approval.GET("/flows", approvalController.GetApprovalFlows)
				approval.PUT("/flows/:id/status", approvalController.UpdateApprovalFlowStatus)
				approval.GET("/todo", approvalController.GetTodoApprovals)
				approval.POST("/approve/:nodeId", approvalController.ApproveNode)
				approval.POST("/reject/:nodeId", approvalController.RejectNode)
				approval.POST("/transfer/:nodeId", approvalController.TransferNode)
			}
		}
	}

	return r
}
